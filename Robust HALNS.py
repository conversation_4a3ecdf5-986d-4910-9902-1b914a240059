import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import random
import tempfile
import os
import importlib.util
import sys
from datetime import datetime, timedelta
import copy
from itertools import product
import math
import time

class OCDAEvaluator:
    """OCDA算法评估器，用于评估定价方案的效果"""
    
    def __init__(self, debug_max_consumers=None, theta_t=0.3, travel_time_deviation_ratio=0.2, driver_capacity=50,
                 k_dist=0.1, k_val=0.01, num_drivers=30):
        self.debug_max_consumers = debug_max_consumers
        self.theta_t = theta_t
        self.travel_time_deviation_ratio = travel_time_deviation_ratio
        self.driver_capacity = driver_capacity
        self.k_dist = k_dist
        self.k_val = k_val
        self.num_drivers = num_drivers # 新增：存储司机数量
        self.load_ocda_modules()
        self.setup_time_structure()
        self.load_base_data()
        self._generate_driver_positions_central() # 新增：集中加载司机位置

    @staticmethod
    def haversine(lon1, lat1, lon2, lat2):
        """计算两点间的地理距离（单位：公里）"""
        lon1, lat1, lon2, lat2 = map(math.radians, [lon1, lat1, lon2, lat2])
        dlon = lon2 - lon1 
        dlat = lat2 - lat1 
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a)) 
        r = 6371
        return c * r

    def _generate_driver_positions_central(self):
        """生成司机初始位置 - 供所有模块统一使用"""
        df = pd.read_csv('meituan_orders_with_delivery_time.csv')
        grab_positions = df[['grab_lng_decimal', 'grab_lat_decimal']].dropna()
        grab_positions = grab_positions[(grab_positions['grab_lng_decimal'] != 0) & 
                                       (grab_positions['grab_lat_decimal'] != 0)]
        
        if len(grab_positions) < self.num_drivers:
            self.driver_positions = grab_positions.values.tolist()
        else:
            selected_positions = grab_positions.sample(n=self.num_drivers, random_state=42)
            self.driver_positions = selected_positions.values.tolist()

        
    def load_ocda_modules(self):
        """动态加载OCDA算法模块"""
        try:
            # 加载 Robust OGGM.py
            spec = importlib.util.spec_from_file_location("oggm_module", "Robust OGGM.py")
            oggm_module = importlib.util.module_from_spec(spec)
            sys.modules["oggm_module"] = oggm_module
            spec.loader.exec_module(oggm_module)
            self.OGGMAlgorithm = oggm_module.OGGMAlgorithm
            
            # 加载 Robust OGSA.py
            spec = importlib.util.spec_from_file_location("ogsa_module", "Robust OGSA.py")
            ogsa_module = importlib.util.module_from_spec(spec)
            sys.modules["ogsa_module"] = ogsa_module
            spec.loader.exec_module(ogsa_module)
            self.OGSAAlgorithm = ogsa_module.OGSAAlgorithm
            
            # 加载 Robust OGAH.py
            spec = importlib.util.spec_from_file_location("ogah_module", "Robust OGAH.py")
            ogah_module = importlib.util.module_from_spec(spec)
            sys.modules["ogah_module"] = ogah_module
            spec.loader.exec_module(ogah_module)
            self.OGAHAlgorithm = ogah_module.OGAHAlgorithm
            
            print("✓ OCDA算法模块加载完成")
        except Exception as e:
            print(f"OCDA模块加载失败: {e}")
            raise
        
    def setup_time_structure(self):
        """建立时间结构 - 严格按照论文设定"""
        # 11:00-15:00，每30分钟一个时间段，共8个时间段
        time_ranges = pd.date_range(start='2022-10-17 11:00:00', end='2022-10-17 15:00:00', freq='30min')
        self.time_periods = [f"{t.hour}:{t.minute:02d}-{(t + pd.Timedelta(minutes=30)).hour}:{(t + pd.Timedelta(minutes=30)).minute:02d}" 
                           for t in time_ranges[:-1]]
        
        # 每个时间段包含3个STP，每10分钟一个
        base_time = datetime(2022, 10, 17, 11, 0, 0)
        self.time_points = [(base_time + timedelta(minutes=10*i)).strftime('%H:%M') for i in range(24)]
        
        # 建立时间点到时间段的映射
        self.time_point_to_period = {}
        for i, time_point in enumerate(self.time_points):
            period_index = i // 3
            if period_index < len(self.time_periods):
                self.time_point_to_period[time_point] = self.time_periods[period_index]
            
        
    def load_base_data(self):
        """加载基础数据"""
        try:
            self.df_preferences = pd.read_csv('consumer_delivery_fee_preferences.csv')
            self.orders_df = pd.read_csv('meituan_orders_with_delivery_time.csv')
            
            # 计算时间段订单统计 (基于原始订单时间，作为基准)
            self.orders_df['platform_order_time'] = pd.to_datetime(self.orders_df['platform_order_time'])
            
            self.period_orders = {}
            time_ranges = pd.date_range(start='2022-10-17 11:00:00', end='2022-10-17 15:00:00', freq='30min')

            for i in range(len(self.time_periods)):
                period = self.time_periods[i]
                start_time = time_ranges[i]
                end_time = time_ranges[i+1]
                count = self.orders_df[(self.orders_df['platform_order_time'] >= start_time) & 
                                       (self.orders_df['platform_order_time'] < end_time)].shape[0]
                self.period_orders[period] = count
                
        except Exception as e:
            print(f"基础数据加载失败: {e}")
            raise
        
    def evaluate_pricing_scheme(self, period_price_map):
        """评估定价方案的效果 - 严格按照论文公式计算"""
        try:
            
            # 1. 根据新的定价方案计算消费者STP选择（论文中的CIIP求解）
            new_stp_choices = self.calculate_stp_choices(period_price_map)
            purchase_count = len([c for c in new_stp_choices if c['choice_type'] == 'purchase'])
            
            # 2. 如果没有消费者购买，返回极低利润
            if purchase_count == 0:
                print("警告：没有消费者购买，返回极低利润")
                return float('-inf'), 0, float('inf')
            
            
            # 3. 生成临时STP选择文件
            temp_stp_file = self.create_temp_stp_file(new_stp_choices)
            
            # 4. 调用OCDA算法（按照论文顺序：OGGM -> OGSA -> OGAH）
            cost_breakdown, revenue_breakdown = self.run_ocda_algorithm(temp_stp_file, period_price_map)
            
            # 5. 计算总利润（严格按照论文公式(5): Q(y,ξw) = R̃w - ∑Qt(y,ξw)）
            total_cost = cost_breakdown['total_cost']
            total_revenue = revenue_breakdown['total_revenue']
            total_profit = total_revenue - total_cost
            
            
            # 6. 清理临时文件
            self.cleanup_temp_files(temp_stp_file)
            
            return total_profit, total_revenue, total_cost
            
        except Exception as e:
            print(f"评估定价方案时出错: {e}")
            import traceback
            traceback.print_exc()
            return float('-inf'), 0, float('inf')
    
    def calculate_stp_choices(self, period_base_price_map):
        """根据新的基础定价方案和动态费用，计算消费者STP选择"""
        
        # 预计算骑手平均起始位置，用于预估距离
        avg_rider_pos = np.mean(self.driver_positions, axis=0) if self.driver_positions else (0,0)

        time_point_prices = {}
        for time_point, period in self.time_point_to_period.items():
            if period in period_base_price_map:
                time_point_prices[time_point] = period_base_price_map[period]
            else:
                time_point_prices[time_point] = None
        
        consumer_choices = []
        df_prefs_to_use = self.df_preferences.head(self.debug_max_consumers) if self.debug_max_consumers is not None else self.df_preferences

        for _, row in df_prefs_to_use.iterrows():
            consumer_id = row['consumer_id']
            preference_str = row['preferences']
            consumer_preferences = self.parse_consumer_preferences(preference_str)
            
            # 为该消费者构建包含动态费用的可用选择列表
            available_tpcs_dynamic = []
            order_info = self.orders_df.iloc[consumer_id - 1]
            order_value = order_info['price']
            
            for time_point, base_price in time_point_prices.items():
                if base_price is not None:
                    # 预估距离 = 平均骑手位置到餐厅 + 餐厅到顾客
                    dist_to_rest = self.haversine(avg_rider_pos[0], avg_rider_pos[1], order_info['sender_lng_decimal'], order_info['sender_lat_decimal'])
                    dist_rest_to_cust = self.haversine(order_info['sender_lng_decimal'], order_info['sender_lat_decimal'], order_info['recipient_lng_decimal'], order_info['recipient_lat_decimal'])
                    estimated_dist = dist_to_rest + dist_rest_to_cust
                    
                    # 预估动态费用
                    dynamic_fee = base_price + self.k_dist * estimated_dist + self.k_val * order_value
                    # 将费用四舍五入到最接近的整数，以匹配偏好列表
                    rounded_fee = int(round(dynamic_fee))
                    
                    available_tpcs_dynamic.append((time_point, rounded_fee))

            available_tpcs_dynamic.append(("不买", None))
            
            optimal_choice, preference_rank = self.solve_ciip_for_consumer(
                consumer_preferences, available_tpcs_dynamic)
            
            choice_type = 'purchase' if optimal_choice[0] != "不买" else 'no_purchase'
            
            consumer_choices.append({
                'consumer_id': consumer_id,
                'chosen_time': optimal_choice[0],
                'chosen_price': optimal_choice[1], # 注意：这里记录的是决策时依据的四舍五入后的动态费用
                'choice_type': choice_type,
                'preference_rank': preference_rank,
                'time_period': self.time_point_to_period.get(optimal_choice[0]) if choice_type == 'purchase' else None
            })
        
        return consumer_choices
    
    def parse_consumer_preferences(self, preference_str):
        """解析消费者偏好列表字符串"""
        preferences = []
        items = preference_str.split(', ')
        
        for item in items:
            if item == "不买":
                preferences.append(("不买", None))
            else:
                parts = item.split('-')
                time = parts[0]
                price_str = parts[1].replace('元', '')
                price = int(price_str)
                preferences.append((time, price))
        
        return preferences
    
    def solve_ciip_for_consumer(self, consumer_preferences, available_tpcs):
        """为单个消费者求解CIIP - 严格按照论文附录D的算法"""
        available_set = set(available_tpcs)
        
        # 按偏好顺序查找第一个可用的TPC（论文约束D.3和D.4）
        for rank, preference_tpc in enumerate(consumer_preferences):
            if preference_tpc in available_set:
                return preference_tpc, rank + 1
        
        # 如果没有找到匹配的偏好，选择不购买
        return ("不买", None), len(consumer_preferences)
    
    def create_temp_stp_file(self, stp_choices):
        """创建临时STP选择文件"""
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='_stp_choices.csv', delete=False)
        temp_filename = temp_file.name
        temp_file.close()
        
        stp_df = pd.DataFrame(stp_choices)
        stp_df.to_csv(temp_filename, index=False)
        
        return temp_filename
    
    def run_ocda_algorithm(self, stp_file, period_price_map):
        """运行完整的OCDA算法流程 - 严格按照论文顺序和参数"""
        temp_prefix = tempfile.mktemp()
        oggm_file = f"{temp_prefix}_oggm.json"
        ogsa_file = f"{temp_prefix}_ogsa.json"
        ogsa_temp_file_path = None
        
        try:
            # Step 1: 运行OGGM算法（订单组生成）
            oggm = self.OGGMAlgorithm(
                stp_choices_path=stp_file,
                orders_data_path='meituan_orders_with_delivery_time.csv',
                theta_t=self.theta_t,
                travel_time_deviation_ratio=self.travel_time_deviation_ratio,
                driver_capacity=self.driver_capacity,
                num_drivers=self.num_drivers
            )
            oggm_results, stp_groups_from_oggm = oggm.run_oggm_for_all_stps()
            oggm.save_results(oggm_results, stp_groups_from_oggm, oggm_file)
            
            # Step 2: 运行OGSA算法（订单组选择）
            ogsa = self.OGSAAlgorithm(
                stp_choices_path=stp_file,
                oggm_results_path=oggm_file,
                orders_data_path='meituan_orders_with_delivery_time.csv',
                theta_t=self.theta_t,
                travel_time_deviation_ratio=self.travel_time_deviation_ratio,
                num_drivers=self.num_drivers
            )
            ogsa_results_all_stps, ogsa_temp_file_path = ogsa.run_ogsa_for_all_stps(output_filename=ogsa_file)
            
            # Step 3: 运行OGAH算法（订单组分配启发式）
            ogah = self.OGAHAlgorithm(
                stp_choices_path=stp_file,
                oggm_results_path=oggm_file,
                ogsa_results_path=ogsa_temp_file_path,
                orders_data_path='meituan_orders_with_delivery_time.csv',
                theta_t=self.theta_t,
                travel_time_deviation_ratio=self.travel_time_deviation_ratio,
                driver_capacity=self.driver_capacity,
                num_drivers=self.num_drivers
            )
            
            # 确保OGAH算法的成本计算与论文一致
            self.ensure_cost_calculation_consistency(ogah)
            
            # 运行OGAH算法，实现司机状态跨STP追踪
            ogah_results_all_stps = self.run_ogah_with_driver_state_tracking(
                ogah, ogsa_results_all_stps)
            
            # Step 4: 计算详细的成本和收益分解（严格按照论文公式）
            cost_breakdown = self.calculate_cost_breakdown_paper_compliant(ogah_results_all_stps)
            revenue_breakdown = self.calculate_revenue_breakdown_paper_compliant(stp_file, ogah_results_all_stps, period_price_map)
            
            return cost_breakdown, revenue_breakdown
            
        except Exception as e:
            print(f"OCDA算法执行失败: {e}")
            import traceback
            traceback.print_exc()
            raise
        finally:
            # 清理临时文件
            for temp_file in [oggm_file, ogsa_file, ogsa_temp_file_path]:
                if temp_file and os.path.exists(temp_file):
                    try:
                        os.unlink(temp_file)
                    except:
                        pass
    
    def ensure_cost_calculation_consistency(self, ogah):
        """确保OGAH算法的成本计算与论文完全一致"""
        # 确保参数设置与论文一致
        ogah.alpha = 0.2  # 旅行成本系数 (RMB/km)
        ogah.beta = 1     # 延迟惩罚系数 (RMB/min)
        ogah.rider_speed_kmh = 20  # 骑手速度 (km/h)
        
    def run_ogah_with_driver_state_tracking(self, ogah, ogsa_results):
        """运行OGAH算法并实现司机状态跨STP追踪"""
        
        # 初始化司机状态
        initial_driver_states = {i: {'lng': pos[0], 'lat': pos[1], 'available_time': 0} 
                                for i, pos in enumerate(ogah.driver_positions)}
        current_driver_states = copy.deepcopy(initial_driver_states)
        
        ogah_results_all_stps = {}
        sorted_stps = sorted(ogsa_results.keys(), key=lambda x: ogah.time_to_minutes(x))
        
        for stp_time_str in sorted_stps:
            ogsa_stp_result = ogsa_results[stp_time_str]
            
            # 获取OGSA选中的订单组
            selected_order_groups_for_ogah = []
            if (ogsa_stp_result and 'best_result' in ogsa_stp_result and 
                'selected_order_groups' in ogsa_stp_result['best_result']):
                for group_info in ogsa_stp_result['best_result']['selected_order_groups']:
                    if 'order_group' in group_info and 'orders' in group_info['order_group']:
                        # 将Order对象转换为字典
                        original_orders = group_info['order_group']['orders']
                        dict_orders = []
                        for order_obj in original_orders:
                            if hasattr(order_obj, '__dict__'): # 检查是否为可转换为dict的对象
                                dict_orders.append(order_obj.__dict__)
                            elif isinstance(order_obj, dict): # 如果已经是字典
                                dict_orders.append(order_obj)
                            else: # 如果无法转换，则保留原样或记录错误
                                print(f"警告：无法将订单对象 {order_obj} 转换为字典。")
                                dict_orders.append(order_obj)


                        # 创建新的订单组字典，其中包含转换后的订单列表
                        reconstructed_group = {
                            'group_id': group_info['order_group'].get('group_id'),
                            'orders': dict_orders,
                            'size': group_info['order_group'].get('size'),
                            'starting_order': group_info['order_group'].get('starting_order'),
                            'sequence': group_info['order_group'].get('sequence', [])
                        }
                        selected_order_groups_for_ogah.append(reconstructed_group)
            
            if not selected_order_groups_for_ogah:
                ogah_results_all_stps[stp_time_str] = {
                    'total_cost': 0,
                    'total_travel_cost': 0,
                    'total_penalty_cost': 0,
                    'assignments': [],
                    'summary': 'No groups for OGAH'
                }
                continue
            
            # 运行OGAH算法
            stp_time_minutes_val = ogah.time_to_minutes(stp_time_str)
            ogah_stp_result_data = ogah.ogah_algorithm_for_stp(
                selected_order_groups=selected_order_groups_for_ogah,
                driver_states=current_driver_states,
                stp=stp_time_str,
                stp_time_minutes=stp_time_minutes_val
            )
            
            ogah_results_all_stps[stp_time_str] = ogah_stp_result_data
            
            # 更新司机状态（为下一个STP做准备）
            for assignment in ogah_stp_result_data.get('assignments', []):
                # 关键修复：确保'order_group'信息被传递
                if 'order_group' in assignment and isinstance(assignment['order_group'], dict):
                    og_orders = assignment['order_group'].get('orders', [])
                    # 将Order对象转换为字典
                    assignment['order_group']['orders'] = [o.__dict__ if hasattr(o, '__dict__') else o for o in og_orders]
                
                driver_id = assignment['driver_id']
                delivery_info = assignment.get('delivery_info', {})
                if delivery_info:
                    current_driver_states[driver_id] = {
                        'lng': delivery_info.get('final_lng', current_driver_states[driver_id]['lng']),
                        'lat': delivery_info.get('final_lat', current_driver_states[driver_id]['lat']),
                        'available_time': delivery_info.get('completion_time', current_driver_states[driver_id]['available_time'])
                    }
        
        return ogah_results_all_stps
    
    def calculate_cost_breakdown_paper_compliant(self, ogah_results):
        """计算成本分解 - 严格按照论文公式"""
        
        total_cost = 0
        total_travel_cost = 0
        total_penalty_cost = 0
        stp_costs = {}
        
        for stp, result in ogah_results.items():
            stp_total = result.get('total_cost', 0)
            total_cost += stp_total
            
            # 计算该STP的旅行成本和延迟成本
            stp_travel = 0
            stp_penalty = 0
            
            assignments = result.get('assignments', [])
            for assignment in assignments:
                stp_travel += assignment.get('travel_cost', 0)
                stp_penalty += assignment.get('penalty_cost', 0)
            
            total_travel_cost += stp_travel
            total_penalty_cost += stp_penalty
            
            stp_costs[stp] = {
                'total_cost': stp_total,
                'travel_cost': stp_travel,
                'penalty_cost': stp_penalty
            }
        
        cost_composition = {}
        if total_cost > 0:
            cost_composition = {
                'travel_percentage': (total_travel_cost / total_cost * 100),
                'penalty_percentage': (total_penalty_cost / total_cost * 100)
            }
        
        
        return {
            'total_cost': total_cost,
            'total_travel_cost': total_travel_cost,
            'total_penalty_cost': total_penalty_cost,
            'stp_breakdown': stp_costs,
            'cost_composition': cost_composition
        }
    
    def calculate_revenue_breakdown_paper_compliant(self, stp_file, ogah_results, period_base_price_map):
        """计算收益分解（已修改为与骑手分配解耦）"""
        
        stp_df = pd.read_csv(stp_file)
        purchase_df = stp_df[stp_df['choice_type'] == 'purchase']
        
        total_revenue = 0
        total_commission = 0
        total_delivery_fees = 0
        commission_rate = 0.18
        order_count = 0
        stp_revenues = {}

        # 预计算骑手平均起始位置，用于预估距离 (与客户选择阶段逻辑保持一致)
        avg_rider_pos = np.mean(self.driver_positions, axis=0) if self.driver_positions else (0,0)

        # 收益计算不再依赖于OGAH的分配结果，而是基于所有购买决策
        for _, row in purchase_df.iterrows():
            consumer_id_str = str(row['consumer_id'])
            order_info = self.orders_df.iloc[int(consumer_id_str) - 1]
            order_value = order_info['price']

            # 计算佣金
            commission = order_value * commission_rate
            
            # 使用与客户选择阶段一致的逻辑来预估动态配送费
            time_period = self.time_point_to_period.get(row['chosen_time'])
            base_price = period_base_price_map.get(time_period, 5) # Default to 5 if not found

            dist_to_rest = self.haversine(avg_rider_pos[0], avg_rider_pos[1], order_info['sender_lng_decimal'], order_info['sender_lat_decimal'])
            dist_rest_to_cust = self.haversine(order_info['sender_lng_decimal'], order_info['sender_lat_decimal'], order_info['recipient_lng_decimal'], order_info['recipient_lat_decimal'])
            estimated_dist = dist_to_rest + dist_rest_to_cust
            
            delivery_fee = base_price + self.k_dist * estimated_dist + self.k_val * order_value

            # 累加收益、佣金和配送费
            order_revenue = commission + delivery_fee
            total_revenue += order_revenue
            total_commission += commission
            total_delivery_fees += delivery_fee
            order_count += 1
            
        return {
            'total_revenue': total_revenue,
            'total_commission': total_commission,
            'total_delivery_fees': total_delivery_fees,
            'order_count': order_count,
            'stp_breakdown': stp_revenues, # 简化：暂不填充分项
            'revenue_composition': {} # 简化：暂不填充
        }

    def _get_node_coords(self, node_str, order_info):
        """辅助函数，从节点字符串获取坐标"""
        # 这是一个简化的辅助函数，实际需要更完整的订单映射
        node_type, _ = node_str.split('_')
        if node_type == 'r':
            return (order_info['sender_lng_decimal'], order_info['sender_lat_decimal'])
        else: # 'c'
            # 需要一种方法从订单ID找到顾客坐标，这里暂时用当前订单的
            return (order_info['recipient_lng_decimal'], order_info['recipient_lat_decimal'])

    
    def cleanup_temp_files(self, *files):
        """清理临时文件"""
        for file in files:
            if file and os.path.exists(file):
                try:
                    os.unlink(file)
                except:
                    pass


class DestroyRepairOperators:
    """破坏算子和修复算子实现 - 严格按照论文5.1.2描述"""
    
    def __init__(self, debug_max_consumers=None, driver_capacity=50, num_drivers=30):
        # === Robust travel-time uncertainty parameters ===
        self.theta_t = 0.3  # Γ: 鲁棒优化预算
        self.travel_time_deviation_ratio = 0.2 # 旅行时间最大偏差率
        self.driver_capacity = driver_capacity # 车辆容量
        self.k_dist = 0.1 # 距离费率
        self.k_val = 0.01 # 价值费率
        
        self.evaluator = OCDAEvaluator(debug_max_consumers=debug_max_consumers, 
                                           theta_t=self.theta_t, 
                                           travel_time_deviation_ratio=self.travel_time_deviation_ratio,
                                           driver_capacity=self.driver_capacity,
                                           k_dist=self.k_dist,
                                           k_val=self.k_val,
                                           num_drivers=num_drivers)
        self.price_set = [2, 3, 4, 5, 6, 7, 8, 9]  # 论文设定的价格集合
        
        # HALNS算法参数（严格按照Table 4）
        self.eta = 40          # η: 段内迭代数
        self.sigma_1 = 30      # σ̄₁: 新最佳解评分
        self.sigma_2 = 9       # σ̄₂: 更好解评分
        self.sigma_3 = 3       # σ̄₃: 被接受恶化解评分
        self.r = 0.1           # r: 反应因子
        self.alpha_cooling = 0.975  # ᾱ: 冷却率
        self.w_hat = 0.05      # ŵ: 恶化百分比(5%)
        self.delta_hat = 7     # δ̂: 可接受邻域大小
        
        # 定义破坏算子（严格按照论文5.1.2）
        self.destroy_operators = [
            ("random_removal", self.random_removal),
            ("maximum_deviation_removal", self.maximum_deviation_removal),
            ("random_pair_removal", self.random_pair_removal),
            ("max_min_removal", self.max_min_removal),
            ("differentiated_period_removal", self.differentiated_period_removal)
        ]
        
        # 定义修复算子（严格按照论文5.1.2）
        self.repair_operators = [
            ("greedy_assignment_repair", self.greedy_assignment_repair),
            ("admissible_neighborhood_repair", self.admissible_neighborhood_repair)
        ]
        
        # 初始化权重（按照论文5.1.3的自适应机制）
        self.destroy_weights = [1.0] * len(self.destroy_operators)
        self.repair_weights = [1.0] * len(self.repair_operators)
        
        # 当前段的统计信息（论文附录I）
        self.destroy_scores = [0.0] * len(self.destroy_operators)
        self.repair_scores = [0.0] * len(self.repair_operators)
        self.destroy_uses = [0] * len(self.destroy_operators)
        self.repair_uses = [0] * len(self.repair_operators)
        
        # 温度相关（论文附录I的模拟退火）
        self.temperature = None
        self.initial_temperature = None
        
        # 解记录
        self.best_solution = None
        self.best_profit = float('-inf')
        self.current_solution = None
        self.current_profit = float('-inf')
        
        
    # ===== 破坏算子（严格按照论文5.1.2）=====
    
    def random_removal(self, period_price_map, period_orders):
        """随机删除算子 - 随机选择一个时间段并移除其价格"""
        result = period_price_map.copy()
        selected_period = random.choice(list(result.keys()))
        del result[selected_period]
        return result, [selected_period]
    
    def maximum_deviation_removal(self, period_price_map, period_orders):
        """最大偏差删除算子 - 选择与平均订单数偏差最大的时间段"""
        result = period_price_map.copy()
        avg_orders = np.mean(list(period_orders.values()))
        max_deviation = 0
        max_dev_period = None
        
        for period, orders in period_orders.items():
            deviation = abs(orders - avg_orders)
            if deviation > max_deviation:
                max_deviation = deviation
                max_dev_period = period
        
        del result[max_dev_period]
        return result, [max_dev_period]
    
    def random_pair_removal(self, period_price_map, period_orders):
        """随机成对删除算子 - 随机选择一对时间段"""
        result = period_price_map.copy()
        periods = list(result.keys())
        if len(periods) < 2:
            return result, []
        
        selected_pair = random.sample(periods, 2)
        for period in selected_pair:
            del result[period]
        
        return result, selected_pair
    
    def max_min_removal(self, period_price_map, period_orders):
        """最大最小删除算子 - 选择订单量最大和最小的时间段"""
        result = period_price_map.copy()
        
        sorted_periods = sorted(period_orders.items(), key=lambda x: x[1])
        
        min_period = sorted_periods[0][0]   # 订单量最少
        max_period = sorted_periods[-1][0]  # 订单量最多
        
        del result[min_period]
        del result[max_period]
        
        return result, [min_period, max_period]
    
    def differentiated_period_removal(self, period_price_map, period_orders):
        """差异化时段删除算子 - 分别选择高峰和非高峰时段"""
        result = period_price_map.copy()
        
        avg_orders = np.mean(list(period_orders.values()))
        
        peak_periods = [period for period, count in period_orders.items() if count >= avg_orders]
        non_peak_periods = [period for period, count in period_orders.items() if count < avg_orders]
        
        if not peak_periods or not non_peak_periods:
            return result, []
        
        peak_period = random.choice(peak_periods)
        non_peak_period = random.choice(non_peak_periods)
        
        del result[peak_period]
        del result[non_peak_period]
        
        return result, [peak_period, non_peak_period]
    
    # ===== 修复算子（严格按照论文5.1.2）=====
    
    def calculate_optional_price_set(self, target_period, current_pricing, period_orders):
        """计算可选价格集合 - 按照论文5.1.2的方法"""
        optional_prices = set()
        periods = self.evaluator.time_periods
        target_idx = periods.index(target_period)
        
        # 第一个价格集合：位于前后时间段价格之间的价格点
        prev_price = None
        next_price = None
        
        for i in range(target_idx - 1, -1, -1):
            if periods[i] in current_pricing:
                prev_price = current_pricing[periods[i]]
                break
        
        for i in range(target_idx + 1, len(periods)):
            if periods[i] in current_pricing:
                next_price = current_pricing[periods[i]]
                break
        
        if prev_price is not None and next_price is not None:
            min_price = min(prev_price, next_price)
            max_price = max(prev_price, next_price)
            for price in self.price_set:
                if min_price <= price <= max_price:
                    optional_prices.add(price)
        
        # 第二个价格集合：基于订单量相对位置的价格点
        target_orders = period_orders[target_period]
        sorted_by_orders = sorted(period_orders.items(), key=lambda x: x[1])
        target_order_idx = next(i for i, (p, _) in enumerate(sorted_by_orders) if p == target_period)
        
        higher_period_price = None
        for i in range(target_order_idx + 1, len(sorted_by_orders)):
            period = sorted_by_orders[i][0]
            if period in current_pricing:
                higher_period_price = current_pricing[period]
                break
        
        lower_period_price = None
        for i in range(target_order_idx - 1, -1, -1):
            period = sorted_by_orders[i][0]
            if period in current_pricing:
                lower_period_price = current_pricing[period]
                break
        
        if higher_period_price is not None and lower_period_price is not None:
            min_price = min(higher_period_price, lower_period_price)
            max_price = max(higher_period_price, lower_period_price)
            for price in self.price_set:
                if min_price <= price <= max_price:
                    optional_prices.add(price)
        
        # 如果可选价格集合为空，使用所有价格
        if not optional_prices:
            optional_prices = set(self.price_set)
        
        return sorted(list(optional_prices))
    
    def greedy_assignment_repair(self, destroyed_solution, removed_periods, period_orders, max_candidates=5):
        """贪心分配修复算子 - 在可选价格集合中选择最佳价格"""
        candidate_solutions = []
        
        if len(removed_periods) == 1:
            period = removed_periods[0]
            optional_prices = self.calculate_optional_price_set(period, destroyed_solution, period_orders)
            
            price_profits = []
            for price in optional_prices:
                test_solution = destroyed_solution.copy()
                test_solution[period] = price
                
                try:
                    profit, revenue, cost = self.evaluator.evaluate_pricing_scheme(test_solution)
                    price_profits.append((test_solution, profit))
                except Exception as e:
                    continue
            
            price_profits.sort(key=lambda x: x[1], reverse=True)
            candidate_solutions = price_profits[:max_candidates]
            
        else:
            # 多个时间段的组合枚举
            period_optional_prices = {}
            for period in removed_periods:
                optional_prices = self.calculate_optional_price_set(period, destroyed_solution, period_orders)
                period_optional_prices[period] = optional_prices[:3]
            
            combinations = list(product(*[period_optional_prices[p] for p in removed_periods]))
            
            if len(combinations) > max_candidates * 3:
                combinations = random.sample(combinations, max_candidates * 3)
            
            combination_profits = []
            for combination in combinations:
                test_solution = destroyed_solution.copy()
                for j, period in enumerate(removed_periods):
                    test_solution[period] = combination[j]
                
                try:
                    profit, revenue, cost = self.evaluator.evaluate_pricing_scheme(test_solution)
                    combination_profits.append((test_solution, profit))
                except Exception as e:
                    continue
            
            combination_profits.sort(key=lambda x: x[1], reverse=True)
            candidate_solutions = combination_profits[:max_candidates]
        
        # 备选解
        if not candidate_solutions:
            backup_solution = destroyed_solution.copy()
            median_price = self.price_set[len(self.price_set) // 2]
            for period in removed_periods:
                backup_solution[period] = median_price
            
            try:
                backup_profit, _, _ = self.evaluator.evaluate_pricing_scheme(backup_solution)
                candidate_solutions = [(backup_solution, backup_profit)]
            except:
                candidate_solutions = [(backup_solution, float('-inf'))]
        
        return candidate_solutions
    
    def admissible_neighborhood_repair(self, destroyed_solution, removed_periods, period_orders, max_candidates=5):
        """可接受邻域修复算子 - 在可接受价格集合中随机选择"""
        candidate_solutions = []
        
        if len(removed_periods) == 1:
            period = removed_periods[0]
            optional_prices = self.calculate_optional_price_set(period, destroyed_solution, period_orders)
            
            if len(optional_prices) <= self.delta_hat:
                admissible_prices = optional_prices
            else:
                admissible_prices = random.sample(optional_prices, self.delta_hat)
            
            price_profits = []
            for price in admissible_prices:
                test_solution = destroyed_solution.copy()
                test_solution[period] = price
                
                try:
                    profit, revenue, cost = self.evaluator.evaluate_pricing_scheme(test_solution)
                    price_profits.append((test_solution, profit))
                except Exception as e:
                    continue
            
            price_profits.sort(key=lambda x: x[1], reverse=True)
            candidate_solutions = price_profits[:max_candidates]
            
        else:
            period_optional_prices = {}
            for period in removed_periods:
                optional_prices = self.calculate_optional_price_set(period, destroyed_solution, period_orders)
                if len(optional_prices) <= 3:
                    period_optional_prices[period] = optional_prices
                else:
                    period_optional_prices[period] = random.sample(optional_prices, 3)
            
            combinations = list(product(*[period_optional_prices[p] for p in removed_periods]))
            
            if len(combinations) > self.delta_hat:
                selected_combinations = random.sample(combinations, self.delta_hat)
            else:
                selected_combinations = combinations
            
            combination_profits = []
            for combination in selected_combinations:
                test_solution = destroyed_solution.copy()
                for j, period in enumerate(removed_periods):
                    test_solution[period] = combination[j]
                
                try:
                    profit, revenue, cost = self.evaluator.evaluate_pricing_scheme(test_solution)
                    combination_profits.append((test_solution, profit))
                except Exception as e:
                    continue
            
            combination_profits.sort(key=lambda x: x[1], reverse=True)
            candidate_solutions = combination_profits[:max_candidates]
        
        if not candidate_solutions:
            return self.greedy_assignment_repair(destroyed_solution, removed_periods, period_orders, max_candidates)
        
        return candidate_solutions
    
    # ===== HALNS算法核心方法（严格按照论文5.1.3和附录I）=====
    
    def roulette_wheel_selection(self, weights):
        """轮盘赌选择 - 按照论文5.1.3的自适应机制"""
        total_weight = sum(weights)
        if total_weight == 0:
            return random.randint(0, len(weights) - 1)
        
        r = random.uniform(0, total_weight)
        cumulative = 0
        for i, weight in enumerate(weights):
            cumulative += weight
            if r <= cumulative:
                return i
        return len(weights) - 1

    def initialize_temperature(self, initial_solution, initial_profit):
        """初始化温度 - 严格按照论文附录I的描述"""
        worse_profit = initial_profit * (1 - self.w_hat)
        delta = abs(initial_profit - worse_profit)
        
        if delta > 0:
            # T_start设置使得ŵ%恶化的解以40%概率被接受
            self.initial_temperature = delta / abs(math.log(0.4))
        else:
            self.initial_temperature = 1000
        
        self.temperature = self.initial_temperature

    def accept_solution(self, new_profit, current_profit):
        """模拟退火接受准则 - 严格按照论文附录I"""
        if new_profit >= current_profit:
            return True
        
        delta = current_profit - new_profit
        probability = math.exp(-delta / self.temperature)
        return random.random() < probability

    def update_scores(self, destroy_idx, repair_idx, score_type):
        """更新算子评分 - 按照论文附录I的评分规则"""
        score_values = {
            'best': self.sigma_1,     # σ̄₁ = 30
            'better': self.sigma_2,   # σ̄₂ = 9
            'accepted': self.sigma_3  # σ̄₃ = 3
        }
        
        score = score_values.get(score_type, 0)
        self.destroy_scores[destroy_idx] += score
        self.repair_scores[repair_idx] += score

    def update_weights(self):
        """更新权重 - 严格按照论文附录I公式(I.1)"""
        # ω_{i,j+1} = (1-r)ω_{ij} + r(π_i/θ̄_{ij}) if θ̄_{ij} ≠ 0
        
        for i in range(len(self.destroy_operators)):
            if self.destroy_uses[i] > 0:
                avg_score = self.destroy_scores[i] / self.destroy_uses[i]
                self.destroy_weights[i] = (1 - self.r) * self.destroy_weights[i] + self.r * avg_score
        
        for i in range(len(self.repair_operators)):
            if self.repair_uses[i] > 0:
                avg_score = self.repair_scores[i] / self.repair_uses[i]
                self.repair_weights[i] = (1 - self.r) * self.repair_weights[i] + self.r * avg_score

    def reset_segment_stats(self):
        """重置段统计信息"""
        self.destroy_scores = [0.0] * len(self.destroy_operators)
        self.repair_scores = [0.0] * len(self.repair_operators)
        self.destroy_uses = [0] * len(self.destroy_operators)
        self.repair_uses = [0] * len(self.repair_operators)

    def run_halns(self, max_iterations=200):
        """运行HALNS算法 - 严格按照论文Algorithm 1和附录I"""
        
        start_time = time.time()
        
        # Step 1: 生成初始解（论文Algorithm 2）
        initial_solution = self.generate_initial_pricing_scheme()
        

        try:
            initial_profit, initial_revenue, initial_cost = self.evaluator.evaluate_pricing_scheme(initial_solution)

        except Exception as e:
            print(f"初始解评估失败: {e}")
            return None, None
        
        # Step 3: 初始化（论文Algorithm 1 Step 2）
        self.current_solution = initial_solution.copy()
        self.current_profit = initial_profit
        self.best_solution = initial_solution.copy()
        self.best_profit = initial_profit
        
        # Step 4: 初始化温度（论文附录I）
        self.initialize_temperature(initial_solution, initial_profit)
        
        period_orders = self.evaluator.period_orders
        
        # Step 5: 主循环（论文Algorithm 1 Step 3-27）
        iteration = 0
        accepted_count = 0
        improved_count = 0
        best_found_iteration = 0
        
        
        while iteration < max_iterations:
            iteration += 1
            
            # 段管理（论文Algorithm 1 Step 23-25）
            if (iteration - 1) % self.eta == 0:
                if iteration > 1:
                    self.update_weights()

                
                self.reset_segment_stats()
            

            
            # Step 5: 选择算子（论文Algorithm 1 Step 5）
            destroy_idx = self.roulette_wheel_selection(self.destroy_weights)
            repair_idx = self.roulette_wheel_selection(self.repair_weights)
            
            destroy_name, destroy_op = self.destroy_operators[destroy_idx]
            repair_name, repair_op = self.repair_operators[repair_idx]
            

            
            # Step 8: 更新使用次数
            self.destroy_uses[destroy_idx] += 1
            self.repair_uses[repair_idx] += 1
            
            try:
                # Step 6: 应用破坏和修复算子（论文Algorithm 1 Step 6）
                destroyed_solution, removed_periods = destroy_op(self.current_solution, period_orders)
                
                candidate_solutions = repair_op(destroyed_solution, removed_periods, period_orders)
                
                if not candidate_solutions:
                    continue
                
                # Step 7: 评估候选解并选择最佳的
                best_candidate_solution, best_candidate_profit = max(candidate_solutions, key=lambda x: x[1])
                
                # Step 9-22: 接受准则和评分更新
                score_type = None
                accept = False
                
                if best_candidate_profit > self.best_profit:
                    # 找到新的最佳解（论文Algorithm 1 Step 11-14）
                    self.best_solution = best_candidate_solution.copy()
                    self.best_profit = best_candidate_profit
                    self.current_solution = best_candidate_solution.copy()
                    self.current_profit = best_candidate_profit
                    score_type = 'best'
                    accept = True
                    best_found_iteration = iteration
                    improved_count += 1
                    
                elif best_candidate_profit > self.current_profit:
                    # 找到更好的解（论文Algorithm 1 Step 9-10）
                    self.current_solution = best_candidate_solution.copy()
                    self.current_profit = best_candidate_profit
                    score_type = 'better'
                    accept = True
                    improved_count += 1
                    
                elif self.accept_solution(best_candidate_profit, self.current_profit):
                    # 接受恶化解（论文Algorithm 1 Step 18-20）
                    self.current_solution = best_candidate_solution.copy()
                    self.current_profit = best_candidate_profit
                    score_type = 'accepted'
                    accept = True
                
                if accept:
                    accepted_count += 1
                
                # 更新评分（论文Algorithm 1 Step 13, 15, 20）
                if score_type:
                    self.update_scores(destroy_idx, repair_idx, score_type)
                
            except Exception as e:
                continue
            
            # Step 26: 更新温度（论文Algorithm 1）
            self.temperature *= self.alpha_cooling
            
            # 进度报告
            if iteration % 20 == 0:
                elapsed = time.time() - start_time
        
        # Step 28: 返回最佳解（论文Algorithm 1）
        total_time = time.time() - start_time
        
        print("\n" + "="*80)
        print("HALNS算法完成（严格按照论文实现）")
        print("="*80)
        print(f"总迭代数: {max_iterations}")
        print(f"总运行时间: {total_time:.1f}秒")
        print(f"平均每次迭代: {total_time/max_iterations:.2f}秒")
        print(f"总接受数: {accepted_count}")
        print(f"总改善数: {improved_count}")
        print(f"接受率: {accepted_count/max_iterations*100:.1f}%")
        print(f"改善率: {improved_count/max_iterations*100:.1f}%")
        print(f"最后改善迭代: {best_found_iteration}")
        
        print(f"\n最佳解:")
        for period, price in self.best_solution.items():
            orders = period_orders.get(period, 0)
            print(f"  {period}: {price}元 (订单量: {orders})")
        
        # 最终详细评估
        print(f"\n最佳解详细性能分析:")
        try:
            final_profit, final_revenue, final_cost = self.evaluator.evaluate_pricing_scheme(self.best_solution)
            
            print(f"\n  === 最终结果（严格按照论文公式计算）===")
            print(f"  总利润: {final_profit:.2f} RMB")
            print(f"  总收益: {final_revenue:.2f} RMB")
            print(f"  总成本: {final_cost:.2f} RMB")
            if final_revenue > 0:
                print(f"  利润率: {(final_profit / final_revenue * 100):.2f}%")
            else:
                print("  利润率: N/A (无收益)")
            
            # 与初始解比较
            profit_improvement = final_profit - initial_profit
            print(f"\n  === 与初始解比较 ===")
            print(f"  利润改善: {profit_improvement:+.2f} RMB")
            if initial_profit != 0:
                print(f"  利润改善率: {profit_improvement/abs(initial_profit)*100:+.2f}%")
                
        except Exception as e:
            print(f"最终详细评估失败: {e}")       
        
        return self.best_solution, self.best_profit
 
    # ===== 初始解生成方法（论文Algorithm 2）=====
    
    def generate_initial_pricing_scheme(self):
        """生成初始定价方案 - 严格按照论文Algorithm 2"""
        print("生成初始定价方案（论文Algorithm 2）...")
        
        # 生成5种候选方案并选择最佳的
        candidate_schemes = []
        
        # 方案1：峰值定价（论文Algorithm 2的主要思路）
        candidate_schemes.append(("峰值定价", self.generate_peak_pricing_scheme()))
        
        # 方案2-5：基于高峰/非高峰时段的四种组合
        peak_non_peak_schemes = self.generate_peak_non_peak_schemes()
        for i, scheme in enumerate(peak_non_peak_schemes):
            candidate_schemes.append((f"高峰非高峰组合{i+1}", scheme))
        
        # 评估所有候选方案
        best_scheme = None
        best_profit = float('-inf')
        best_name = ""

        print("正在评估候选初始方案...")
        for name, scheme in candidate_schemes:
            try:
                profit, revenue, cost = self.evaluator.evaluate_pricing_scheme(scheme)
                print(f"  {name}: 利润={profit:.2f}, 收益={revenue:.2f}, 成本={cost:.2f}")
                
                if profit > best_profit:
                    best_profit = profit
                    best_scheme = scheme.copy()
                    best_name = name
                    
            except Exception as e:
                print(f"  {name}: 评估失败 - {e}")
                continue

        if best_scheme is not None:
            print(f"选择最佳初始方案: {best_name} (利润: {best_profit:.2f})")
        else:
            print("所有候选方案评估失败，使用默认峰值定价方案")
            return self.generate_peak_pricing_scheme()

        return best_scheme

    def generate_peak_pricing_scheme(self):
        """生成峰值定价方案 - 按照论文Algorithm 2的思路"""
        period_orders = self.evaluator.period_orders
        sorted_periods = sorted(period_orders.items(), key=lambda x: x[1], reverse=True)
        
        initial_pricing = {}
        price_set_desc = sorted(self.price_set, reverse=True)
        
        for i, (period, _) in enumerate(sorted_periods):
            if i < len(price_set_desc):
                initial_pricing[period] = price_set_desc[i]
            else:
                initial_pricing[period] = price_set_desc[-1]
        
        return initial_pricing

    def generate_peak_non_peak_schemes(self):
        """生成基于高峰/非高峰时段的四种定价方案 - 按照论文Algorithm 2扩展"""
        period_orders = self.evaluator.period_orders
        
        # 计算平均订单数量（论文Algorithm 2的ξ̄）
        avg_orders = np.mean(list(period_orders.values()))
        
        # 划分高峰和非高峰时间段（论文Algorithm 2的T̂₁和T̂₂）
        peak_periods = [period for period, count in period_orders.items() if count >= avg_orders]
        non_peak_periods = [period for period, count in period_orders.items() if count < avg_orders]
        
        # 确保所有时间段都被覆盖
        all_periods = set(period_orders.keys())
        covered_periods = set(peak_periods) | set(non_peak_periods)
        if all_periods != covered_periods:
            for uncovered in (all_periods - covered_periods):
                non_peak_periods.append(uncovered)
        
        # 计算价格选项（论文Algorithm 2的q₁, q₂, q₃, q₄）
        price_set_sorted = sorted(self.price_set)
        delta = 1/4  # 论文Algorithm 2的δ
        
        q1_idx = int(delta * len(price_set_sorted))
        q2_idx = q1_idx + 1
        q3_idx = int(3 * delta * len(price_set_sorted))
        q4_idx = q3_idx + 1
        
        # 非高峰时间段的价格选项
        non_peak_price_options = [
            price_set_sorted[min(q1_idx, len(price_set_sorted)-1)], 
            price_set_sorted[min(q2_idx, len(price_set_sorted)-1)]
        ]
        
        # 高峰时间段的价格选项
        peak_price_options = [
            price_set_sorted[min(q3_idx, len(price_set_sorted)-1)], 
            price_set_sorted[min(q4_idx, len(price_set_sorted)-1)]
        ]
        
        # 生成四种组合方案
        schemes = []
        for peak_price in peak_price_options:
            for non_peak_price in non_peak_price_options:
                scheme = {}
                
                for period in peak_periods:
                    scheme[period] = peak_price
                
                for period in non_peak_periods:
                    scheme[period] = non_peak_price
                
                schemes.append(scheme)
        
        return schemes


if __name__ == "__main__":
    print("开始运行HALNS算法（严格按照论文实现）...")
    
    # 设置随机种子确保结果可重现
    random.seed(42)
    np.random.seed(42)
    
    try:
        # 创建破坏修复算子实例
        num_drivers_to_use = 10 # 设置骑手数量
        operators = DestroyRepairOperators(debug_max_consumers=20, driver_capacity=50, num_drivers=num_drivers_to_use)
        
        # 运行完整HALNS算法
        max_iterations = 200  # 论文设定的迭代数
        best_solution, best_profit = operators.run_halns(max_iterations=max_iterations)
        
        if best_solution is not None:
            print(f"\n✓ HALNS算法完成！最佳利润: {best_profit:.2f}")
            print("\n最终解验证:")
            for period, price in best_solution.items():
                orders = operators.evaluator.period_orders.get(period, 0)
                print(f"  {period}: {price}元 (订单量: {orders})")
        else:
            print("\n✗ HALNS算法执行失败")
        
        print("\n✓ 程序执行完成！")
        print("严格按照论文Section 5.1.3和Appendix I实现的HALNS算法")
        
    except Exception as e:
        print(f"程序执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()