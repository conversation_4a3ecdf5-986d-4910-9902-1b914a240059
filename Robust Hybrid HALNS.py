import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import random
import tempfile
import os
from datetime import datetime, timedelta
import copy
from itertools import product
import math
import time
from typing import List, Dict, Tuple, Any, Set
import gurobipy as gp
from gurobipy import GRB
from types import SimpleNamespace
from math import radians, sin, cos, asin, sqrt


# =================================================================================
# == 代码从 Robust Hybrid.py 移植并适配 (开始)
# =================================================================================

def haversine(lon1, lat1, lon2, lat2):
    """计算两点间的地理距离（单位：公里）"""
    if any(pd.isna(x) for x in [lon1, lat1, lon2, lat2]):
        return 0.0
    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
    dlon = lon2 - lon1 
    dlat = lat2 - lat1 
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a)) 
    r = 6371  # 地球平均半径，单位为公里
    return c * r

def compute_rg2_robust_cost(sequence, start_pos, start_time, stp_start_time,
                             restaurant_coords, customer_coords,
                             order_restaurants,
                             driver_speed,
                             theta_t, deviation_ratio,
                             travel_cost_per_unit, penalty_cost_per_unit,
                             est_delivery_duration, depot_coord,
                             # 新增参数
                             order_ready_times,
                             order_volumes, driver_capacity):
    """返回 (robust_travel_cost, robust_penalty_cost, total_cost, final_pos, final_time)"""
    if not sequence:
        return 0.0, 0.0, 0.0, start_pos, start_time

    # 容量检查
    current_load = 0
    for node in sequence:
        if node.startswith("pickup_"):
            order_id = int(node.split("_")[1])
            current_load += order_volumes.get(order_id, 1.0)
            if current_load > driver_capacity:
                return float('inf'), float('inf'), float('inf'), start_pos, start_time
        elif node.startswith("delivery_"):
            order_id = int(node.split("_")[1])
            current_load -= order_volumes.get(order_id, 1.0)

    arcs = []
    deliveries = []  # (cust_id, arc_idx)
    pickups = {} # {arc_idx: order_id}
    cur = start_pos
    for i, node in enumerate(sequence):
        if node.startswith("pickup_"):
            idx = int(node.split("_")[1])
            rest_id = order_restaurants[idx]
            nxt = restaurant_coords[rest_id]
            pickups[i] = idx
        elif node.startswith("delivery_"):
            idx = int(node.split("_")[1])
            nxt = customer_coords[idx]
            deliveries.append((idx, len(arcs)))
        else:
            continue
        arcs.append((cur, nxt))
        cur = nxt

    nominal_times, deviations = [], []
    for a, b in arcs:
        dist = haversine(a[0], a[1], b[0], b[1])
        t_nom = dist / driver_speed
        nominal_times.append(t_nom)
        deviations.append(t_nom * deviation_ratio)

    positive_arc_count = sum(1 for t in nominal_times if t > 1e-8)
    gamma = math.ceil(theta_t * positive_arc_count)

    # 使用DP计算最坏情况下的到达时间
    # dp[i][g] = 到达第 i 个节点（完成第 i-1 段弧），用了 g 次扰动时的最坏时间
    dp = [[-1.0] * (gamma + 1) for _ in range(len(arcs) + 1)]
    dp[0][0] = start_time

    for i in range(len(arcs)): # 遍历每段弧 arc i
        t_nom, dev = nominal_times[i], deviations[i]
        
        # 确定在弧i起点的出发时间
        departure_time_at_i = [-1.0] * (gamma + 1)
        for g in range(gamma + 1):
            if dp[i][g] == -1.0:
                continue
            
            # 检查是否为取餐点，并考虑备餐等待
            # 注意：等待发生在节点i，即弧i的起点
            ready_time = -1.0
            if i in pickups:
                order_id = pickups[i]
                prep_time = order_ready_times.get(order_id, 0)
                ready_time = stp_start_time + prep_time

            departure_time_at_i[g] = max(dp[i][g], ready_time)

        # 状态转移
        for g in range(gamma + 1):
            if departure_time_at_i[g] == -1.0:
                continue

            # 不使用扰动（寻找最坏情况，即最大时间）
            next_arrival_no_dev = departure_time_at_i[g] + t_nom
            if dp[i+1][g] == -1.0 or next_arrival_no_dev > dp[i+1][g]:
                dp[i+1][g] = next_arrival_no_dev
            
            # 使用扰动（寻找最坏情况，即最大时间）
            if g < gamma:
                next_arrival_with_dev = departure_time_at_i[g] + t_nom + dev
                if dp[i+1][g+1] == -1.0 or next_arrival_with_dev > dp[i+1][g+1]:
                    dp[i+1][g+1] = next_arrival_with_dev
    
    # 解析结果
    arrival_worst = {}
    for cust_id, arc_idx in deliveries:
        try:
            # 送达时间是弧arc_idx终点的时间，即dp[arc_idx+1]
            worst_arrival = max(val for val in dp[arc_idx + 1] if val != -1.0)
            arrival_worst[cust_id] = worst_arrival
        except ValueError:
            arrival_worst[cust_id] = float('inf')

    try:
        worst_total_time = max(val for val in dp[-1] if val != -1.0)
    except ValueError:
        worst_total_time = start_time

    final_service_pos = arcs[-1][1] if arcs else start_pos
    final_service_time = worst_total_time

    # 按距离计算旅行成本（与等待时间无关）
    total_nominal_distance = sum(t * driver_speed for t in nominal_times)
    robust_travel_cost = total_nominal_distance * travel_cost_per_unit

    robust_penalty_cost = 0.0
    for cust_id, worst_arrival in arrival_worst.items():
        if cust_id in est_delivery_duration:
            target = stp_start_time + est_delivery_duration[cust_id]
            delay = max(worst_arrival - target, 0)
            robust_penalty_cost += delay * penalty_cost_per_unit

    total_cost = robust_travel_cost + robust_penalty_cost
    return robust_travel_cost, robust_penalty_cost, total_cost, final_service_pos, final_service_time



class OrderGroup:
    """订单组类，包含订单序列和相关信息"""
    def __init__(self, orders: List[int], sequence: List[str], starting_order: int):
        self.orders = orders
        self.sequence = sequence
        self.starting_order = starting_order
        self.virtual_driver_id = None
        self.estimated_cost = 0.0
        self.dispatch_cost = 0.0
        
    def __len__(self):
        return len(self.orders)
        
    def contains_order(self, order_id: int) -> bool:
        return order_id in self.orders
    
    def get_orders_set(self) -> Set[int]:
        return set(self.orders)


class HybridOGGM:
    """订单组生成方法 (Order Group Generation Method) - 适配版"""
    
    def __init__(self, food_delivery_optimizer):
        self.optimizer = food_delivery_optimizer
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.max_orders_per_driver = food_delivery_optimizer.max_orders_per_driver
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.customer_coords = food_delivery_optimizer.customer_coords
        self.driver_start_coords = food_delivery_optimizer.driver_start_coords
        self.order_restaurants = food_delivery_optimizer.order_restaurants
        self.estimated_delivery_duration = food_delivery_optimizer.estimated_delivery_duration
        self.delay_threshold = 0.2
        # 新增逻辑：容量和备餐时间
        self.driver_capacity = food_delivery_optimizer.driver_capacity
        self.order_volumes = food_delivery_optimizer.order_volumes
        self.order_ready_times = food_delivery_optimizer.order_ready_times
        # 鲁棒优化参数
        self.theta_t = getattr(food_delivery_optimizer, 'theta_t', 0.3)
        self.travel_time_deviation_ratio = getattr(food_delivery_optimizer, 'travel_time_deviation_ratio', 0.2)
        # 虚拟骑手计算参数（与Robust OGGM.py保持一致）
        self.delta_bar = 2

    def _is_sequence_capacity_feasible(self, sequence: List[str]) -> bool:
        """检查给定序列是否在全程满足容量约束"""
        if not sequence:
            return True
        current_load = 0
        for node in sequence:
            if node.startswith("pickup_"):
                order_id = int(node.split("_")[1])
                current_load += self.order_volumes.get(order_id, 1.0) # 默认为1
                if current_load > self.driver_capacity:
                    return False
            elif node.startswith("delivery_"):
                order_id = int(node.split("_")[1])
                current_load -= self.order_volumes.get(order_id, 1.0)
        return True

    
    def calculate_distance(self, coord1: Tuple[float, float], coord2: Tuple[float, float]) -> float:
        return haversine(coord1[0], coord1[1], coord2[0], coord2[1])
    
    def get_order_coordinates(self, order_id: int) -> Tuple[Tuple[float, float], Tuple[float, float]]:
        restaurant_id = self.order_restaurants[order_id]
        restaurant_coord = self.restaurant_coords[restaurant_id]
        customer_coord = self.customer_coords[order_id]
        return restaurant_coord, customer_coord

    def calculate_single_order_distance(self, order_id: int, driver_start: Tuple[float, float]) -> float:
        restaurant_coord, customer_coord = self.get_order_coordinates(order_id)
        dist_to_restaurant = self.calculate_distance(driver_start, restaurant_coord)
        dist_restaurant_to_customer = self.calculate_distance(restaurant_coord, customer_coord)
        return dist_to_restaurant + dist_restaurant_to_customer

    def calculate_two_orders_combined_distance(self, order1_id: int, order2_id: int, driver_start: Tuple[float, float]) -> Tuple[float, float]:
        rest1_coord, cust1_coord = self.get_order_coordinates(order1_id)
        rest2_coord, cust2_coord = self.get_order_coordinates(order2_id)
        
        dist_start_to_r1 = self.calculate_distance(driver_start, rest1_coord)
        sequences_order1_first = [
            dist_start_to_r1 + self.calculate_distance(rest1_coord, rest2_coord) + self.calculate_distance(rest2_coord, cust2_coord) + self.calculate_distance(cust2_coord, cust1_coord),
            dist_start_to_r1 + self.calculate_distance(rest1_coord, rest2_coord) + self.calculate_distance(rest2_coord, cust1_coord) + self.calculate_distance(cust1_coord, cust2_coord),
            dist_start_to_r1 + self.calculate_distance(rest1_coord, cust1_coord) + self.calculate_distance(cust1_coord, rest2_coord) + self.calculate_distance(rest2_coord, cust2_coord)
        ]
        dist_order1_first = min(sequences_order1_first)
        
        dist_start_to_r2 = self.calculate_distance(driver_start, rest2_coord)
        sequences_order2_first = [
            dist_start_to_r2 + self.calculate_distance(rest2_coord, rest1_coord) + self.calculate_distance(rest1_coord, cust1_coord) + self.calculate_distance(cust1_coord, cust2_coord),
            dist_start_to_r2 + self.calculate_distance(rest2_coord, rest1_coord) + self.calculate_distance(rest1_coord, cust2_coord) + self.calculate_distance(cust2_coord, cust1_coord),
            dist_start_to_r2 + self.calculate_distance(rest2_coord, cust2_coord) + self.calculate_distance(cust2_coord, rest1_coord) + self.calculate_distance(rest1_coord, cust1_coord)
        ]
        dist_order2_first = min(sequences_order2_first)
        return dist_order1_first, dist_order2_first
    
    def calculate_order_matching_degree(self, order1_id: int, order2_id: int, virtual_driver1_pos: Tuple[float, float], virtual_driver2_pos: Tuple[float, float]) -> Tuple[float, float]:
        dist1_separate = self.calculate_single_order_distance(order1_id, virtual_driver1_pos)
        dist2_separate = self.calculate_single_order_distance(order2_id, virtual_driver2_pos)
        total_separate_distance = dist1_separate + dist2_separate
        dist_order1_first, dist_order2_first = self.calculate_two_orders_combined_distance(order1_id, order2_id, virtual_driver1_pos)
        fit_o1_o2 = total_separate_distance - dist_order1_first
        fit_o2_o1 = total_separate_distance - dist_order2_first
        return fit_o1_o2, fit_o2_o1

    def time_to_minutes(self, time_str: str) -> int:
        """将'HH:MM'格式的时间字符串转换为从午夜开始的分钟数"""
        if isinstance(time_str, str):
            try:
                hour, minute = map(int, time_str.split(':'))
                return hour * 60 + minute
            except ValueError:
                return 0
        return 0


    def _get_node_pos(self, node_str: str, order_id: int) -> Tuple[float, float]:
        """根据节点字符串获取坐标"""
        if node_str.startswith("pickup_"):
            restaurant_id = self.order_restaurants[order_id]
            return self.restaurant_coords[restaurant_id]
        elif node_str.startswith("delivery_"):
            return self.customer_coords[order_id]
        raise ValueError(f"未知的节点类型: {node_str}")

    def cheapest_insertion_robust(self, sequence: List[str], new_order_id: int, start_pos: Tuple[float, float], orders_map: Dict, stp_time_minutes: float) -> List[str]:
        """将新订单插入现有序列的最佳位置（基于鲁棒成本）"""
        pickup_node = f'pickup_{new_order_id}'
        delivery_node = f'delivery_{new_order_id}'
        
        best_sequence = None
        min_cost = float('inf')

        for pickup_pos in range(len(sequence) + 1):
            for delivery_pos in range(pickup_pos + 1, len(sequence) + 2):
                temp_sequence = sequence[:pickup_pos] + [pickup_node] + sequence[pickup_pos:delivery_pos-1] + [delivery_node] + sequence[delivery_pos-1:]

                if not self._is_sequence_capacity_feasible(temp_sequence):
                    continue

                cost, _, _ = self._calculate_robust_cost_for_sequence(temp_sequence, start_pos, stp_time_minutes, orders_map)
                
                if cost < min_cost:
                    min_cost = cost
                    best_sequence = temp_sequence
        
        return best_sequence

    def generate_optimal_sequence(self, order_ids: List[int], start_pos: Tuple[float, float], orders_map: Dict, stp_time_minutes: float) -> List[str]:
        """为一组订单生成最优序列"""
        if not order_ids:
            return []
        
        # 从第一个订单开始
        sequence = [f'pickup_{order_ids[0]}', f'delivery_{order_ids[0]}']
        
        # 逐个插入剩余订单
        for i in range(1, len(order_ids)):
            new_order_id = order_ids[i]
            temp_map = {k: v for k, v in orders_map.items() if k in order_ids[:i+1]}
            
            new_sequence = self.cheapest_insertion_robust(sequence, new_order_id, start_pos, temp_map, stp_time_minutes)
            
            if not new_sequence:
                return None  # 无法生成可行序列
            sequence = new_sequence
            
        return sequence

    def calculate_order_group_matching_degree(self, base_order_ids: List[int], new_order_id: int, start_pos: Tuple[float, float], orders_map: Dict, stp_time_minutes: float) -> float:
        """根据鲁棒成本节省计算匹配度"""
        
        # 1. 计算单个新订单的成本
        new_order_map = {new_order_id: orders_map[new_order_id]}
        new_order_seq = self.generate_optimal_sequence([new_order_id], start_pos, new_order_map, stp_time_minutes)
        if not new_order_seq: return -float('inf')
        cost_new, _, _ = self._calculate_robust_cost_for_sequence(new_order_seq, start_pos, stp_time_minutes, new_order_map)

        # 2. 计算现有订单组的成本
        base_map = {k: v for k, v in orders_map.items() if k in base_order_ids}
        base_seq = self.generate_optimal_sequence(base_order_ids, start_pos, base_map, stp_time_minutes)
        if not base_seq: return -float('inf')
        cost_base, _, _ = self._calculate_robust_cost_for_sequence(base_seq, start_pos, stp_time_minutes, base_map)

        # 3. 计算合并后订单组的成本
        merged_ids = base_order_ids + [new_order_id]
        merged_map = {k: v for k, v in orders_map.items() if k in merged_ids}
        merged_seq = self.generate_optimal_sequence(merged_ids, start_pos, merged_map, stp_time_minutes)
        if not merged_seq: return -float('inf')
        cost_merged, _, _ = self._calculate_robust_cost_for_sequence(merged_seq, start_pos, stp_time_minutes, merged_map)
        
        # 成本节省 = 合并前总成本 - 合并后成本
        fit = (cost_base + cost_new) - cost_merged
        return fit

    def calculate_max_delay_degree_for_group(self, order_ids: List[int], start_pos: Tuple[float, float], orders_map: Dict, stp_time_minutes: float) -> float:
        """计算订单组的最大延迟度"""
        sequence = self.generate_optimal_sequence(order_ids, start_pos, orders_map, stp_time_minutes)
        if not sequence: return float('inf')

        _, _, penalty_time = self._calculate_robust_cost_for_sequence(sequence, start_pos, stp_time_minutes, orders_map)
        
        total_estimated_duration = sum(self.estimated_delivery_duration[oid] for oid in order_ids)
        if total_estimated_duration == 0: return float('inf')
        
        return penalty_time / total_estimated_duration

    def create_virtual_driver_info(self, order_id: int) -> Tuple[float, float]:
        """为指定订单创建虚拟骑手信息（与Robust OGGM.py保持一致）"""
        # 获取订单的餐厅位置作为参考点
        restaurant_id = self.order_restaurants[order_id]
        ref_pos = self.restaurant_coords[restaurant_id]
        
        # 计算到所有司机位置的距离
        distances = []
        for driver_id, (driver_lng, driver_lat) in self.driver_start_coords.items():
            dist = haversine(driver_lng, driver_lat, ref_pos[0], ref_pos[1])
            distances.append((driver_lng, driver_lat, dist))
        
        # 按距离排序，取前delta_bar个最近的司机
        distances.sort(key=lambda x: x[2])
        closest_drivers = distances[:self.delta_bar]
        
        # 计算平均位置
        avg_lng = np.mean([d[0] for d in closest_drivers])
        avg_lat = np.mean([d[1] for d in closest_drivers])
        
        return (avg_lng, avg_lat)

    def generate_order_groups_for_stp(self, orders_in_stp: List[int], stp_time_str: str, actual_driver_start_pos: Tuple[float, float], actual_driver_start_time: float) -> List[OrderGroup]:
        if not orders_in_stp:
            return []

        stp_time_minutes = self.time_to_minutes(stp_time_str)
        
        # 准备数据
        orders_map = {oid: SimpleNamespace(order_id=oid) for oid in orders_in_stp}

        all_order_groups = []
        for starting_order_id in orders_in_stp:
            if self.order_volumes.get(starting_order_id, 1.0) > self.driver_capacity:
                continue

            # 为当前起始订单计算专门的虚拟骑手位置（与Robust OGGM.py保持一致）
            virtual_driver_pos = self.create_virtual_driver_info(starting_order_id)

            current_cluster_ids = [starting_order_id]
            
            # 创建初始订单组（使用虚拟骑手位置）
            initial_sequence = self.generate_optimal_sequence(current_cluster_ids, virtual_driver_pos, orders_map, stp_time_minutes)
            if initial_sequence:
                 all_order_groups.append(OrderGroup(current_cluster_ids.copy(), initial_sequence, starting_order_id))

            remaining_orders = [oid for oid in orders_in_stp if oid != starting_order_id]
            
            while len(current_cluster_ids) < self.max_orders_per_driver and remaining_orders:
                scores = []
                for next_order_id in remaining_orders:
                    fit = self.calculate_order_group_matching_degree(current_cluster_ids, next_order_id, virtual_driver_pos, orders_map, stp_time_minutes)
                    scores.append((next_order_id, fit))
                
                scores.sort(key=lambda x: x[1], reverse=True)
                
                if not scores or scores[0][1] <= 0:
                    break
                
                best_next_id, _ = scores[0]
                
                # 检查延迟
                temp_cluster_ids = current_cluster_ids + [best_next_id]
                delay_degree = self.calculate_max_delay_degree_for_group(temp_cluster_ids, virtual_driver_pos, orders_map, stp_time_minutes)
                
                if delay_degree > self.delay_threshold:
                    remaining_orders.remove(best_next_id)
                    continue

                # 接受新订单
                current_cluster_ids.append(best_next_id)
                remaining_orders.remove(best_next_id)
                
                # 为新形成的组生成序列并保存（使用虚拟骑手位置）
                new_sequence = self.generate_optimal_sequence(current_cluster_ids, virtual_driver_pos, orders_map, stp_time_minutes)
                if new_sequence:
                    all_order_groups.append(OrderGroup(current_cluster_ids.copy(), new_sequence, starting_order_id))

        # 去重
        unique_order_groups = []
        seen_order_sets = set()
        for og in sorted(all_order_groups, key=lambda x: (len(x.orders), tuple(sorted(x.orders)))):
            orders_tuple = tuple(sorted(og.orders))
            if orders_tuple not in seen_order_sets:
                unique_order_groups.append(og)
                seen_order_sets.add(orders_tuple)
        
        return unique_order_groups

    def _calculate_robust_cost_for_sequence(self, sequence, start_pos, start_time, orders_map):
        """
        精确计算给定配送序列的鲁棒成本（旅行时间+延迟时间），并考虑备餐时间。
        采用动态规划实现。
        """
        if not sequence:
            return 0.0, 0.0, 0.0

        nodes_in_sequence = ['start'] + sequence
        
        # 1. 构建坐标列表和弧段列表
        coords = [start_pos]
        for node_str in sequence:
            node_type, order_id_str = node_str.split('_', 1)
            order_id = int(order_id_str)
            if node_type == 'pickup':
                restaurant_id = self.order_restaurants[order_id]
                coords.append(self.restaurant_coords[restaurant_id])
            else:  # delivery
                coords.append(self.customer_coords[order_id])
        
        arcs = [(coords[i], coords[i+1]) for i in range(len(coords)-1)]
        nominal_times = [(haversine(s[0], s[1], e[0], e[1]) / self.driver_speed) for s,e in arcs]
        deviations = [t * self.travel_time_deviation_ratio for t in nominal_times]

        # 2. 初始化DP表和不确定性预算
        gamma = math.ceil(self.theta_t * len(arcs)) if arcs else 0
        dp = [[-1.0] * (gamma + 1) for _ in range(len(nodes_in_sequence))]
        dp[0][0] = start_time

        # 3. DP状态转移
        for i in range(len(arcs)):  # 遍历每条弧段, i 是出发节点索引
            t_nom, dev = nominal_times[i], deviations[i]
            
            # 检查当前节点i是否为餐厅取餐点，并计算备餐完成时间
            node_i_str = nodes_in_sequence[i]
            is_restaurant_pickup = (i > 0 and node_i_str.startswith('pickup_'))
            food_ready_time = -1.0
            if is_restaurant_pickup:
                _, order_id_str = node_i_str.split('_', 1)
                order_id = int(order_id_str)
                prep_time = self.order_ready_times.get(order_id, 0)
                food_ready_time = start_time + prep_time
            
            for g in range(gamma + 1):
                if dp[i][g] == -1.0: continue
                
                arrival_at_i = dp[i][g]
                
                # 骑手从节点i的出发时间 = max(到达时间, 备餐完成时间)
                departure_at_i = max(arrival_at_i, food_ready_time) if is_restaurant_pickup else arrival_at_i

                # 使用当前弧段的不确定性预算
                if g < gamma:
                    next_arrival_with_dev = departure_at_i + t_nom + dev
                    if dp[i+1][g+1] == -1.0 or next_arrival_with_dev > dp[i+1][g+1]:
                        dp[i+1][g+1] = next_arrival_with_dev

                # 不使用当前弧段的不确定性预算
                next_arrival_no_dev = departure_at_i + t_nom
                if dp[i+1][g] == -1.0 or next_arrival_no_dev > dp[i+1][g]:
                    dp[i+1][g] = next_arrival_no_dev

        # 4. 计算鲁棒旅行时间和惩罚
        worst_finish_time = max((v for v in dp[-1] if v != -1.0), default=start_time)
        robust_travel_time = worst_finish_time - start_time

        penalty_time = 0.0
        for i, node_str in enumerate(nodes_in_sequence):
            if i == 0: continue
            if node_str.startswith("delivery_"):
                _, order_id_str = node_str.split('_', 1)
                order_id = int(order_id_str)

                # 获取订单的平台下单时间和预期配送时间
                order_info = self.optimizer.orders_df.iloc[order_id-1]
                platform_order_time = order_info['platform_order_time']
                if hasattr(platform_order_time, 'strftime'):
                    platform_order_time_str = platform_order_time.strftime('%H:%M')
                else:
                    platform_order_time_str = str(platform_order_time)
                platform_order_time_minutes = self.time_to_minutes(platform_order_time_str)

                deadline = platform_order_time_minutes + self.estimated_delivery_duration[order_id]
                
                worst_arrival_at_node = max((v for v in dp[i] if v != -1.0), default=-1.0)
                
                if worst_arrival_at_node != -1.0:
                    delay = max(worst_arrival_at_node - deadline, 0)
                    penalty_time += delay

        total_cost_time = robust_travel_time + penalty_time
        return total_cost_time, robust_travel_time, penalty_time


class Driver:
    """司机状态类"""
    def __init__(self, driver_id: int, start_pos: Tuple[float, float], start_time: float, capacity: int):
        self.driver_id = driver_id
        self.current_pos = start_pos
        self.current_time = start_time
        self.remaining_capacity = capacity
        self.assigned_orders = []
        self.total_cost = 0.0
        self.total_distance = 0.0
        
    def can_handle_order_group(self, order_group) -> bool:
        return self.remaining_capacity >= len(order_group)
    
    def assign_order_group(self, order_group, execution_cost: float, new_pos: Tuple[float, float], new_time: float, distance: float):
        self.assigned_orders.extend(order_group.orders)
        self.remaining_capacity -= len(order_group)
        self.total_cost += execution_cost
        self.total_distance += distance
        self.current_pos = new_pos
        self.current_time = new_time


class HybridOGAH:
    """订单组分配启发式算法 - 作为Gurobi模型的备用"""
    def __init__(self, food_delivery_optimizer):
        self.optimizer = food_delivery_optimizer
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.max_orders_per_driver = food_delivery_optimizer.max_orders_per_driver
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.customer_coords = food_delivery_optimizer.customer_coords
        self.driver_start_coords = food_delivery_optimizer.driver_start_coords
        self.order_restaurants = food_delivery_optimizer.order_restaurants
        self.estimated_delivery_duration = food_delivery_optimizer.estimated_delivery_duration
        self.num_drivers = food_delivery_optimizer.num_drivers
        self.current_driver_states = None

    def calculate_distance(self, coord1, coord2):
        return haversine(coord1[0], coord1[1], coord2[0], coord2[1])

    def calculate_sequence_execution_details(self, sequence, driver_start_pos, driver_start_time, stp_start_time_minutes):
        current_pos, current_time, total_distance, total_penalty = driver_start_pos, driver_start_time, 0.0, 0.0
        
        for node in sequence:
            if node.startswith("pickup_"):
                order_id = int(node.split("_")[1])
                next_pos = self.restaurant_coords[self.order_restaurants[order_id]]
            else:
                order_id = int(node.split("_")[1])
                next_pos = self.customer_coords[order_id]
            
            travel_dist = self.calculate_distance(current_pos, next_pos)
            travel_time = travel_dist / self.driver_speed
            total_distance += travel_dist
            current_time += travel_time
            current_pos = next_pos
            
            if node.startswith("delivery_"):
                target_time = stp_start_time_minutes + self.estimated_delivery_duration.get(order_id, 0)
                delay = max(current_time - target_time, 0)
                total_penalty += delay * self.penalty_cost_per_unit
        
        travel_cost = total_distance * self.travel_cost_per_unit
        return travel_cost, total_penalty, current_pos, current_time

    def initialize_drivers(self, stp_time_minutes: float, initial_states: List[Driver] = None) -> List[Driver]:
        drivers = []
        if initial_states:
            for prev_state in initial_states:
                start_time = max(prev_state.current_time, stp_time_minutes)
                driver = Driver(prev_state.driver_id, prev_state.current_pos, start_time, self.max_orders_per_driver)
                drivers.append(driver)
        else:
            for driver_id in range(self.num_drivers):
                start_pos = self.driver_start_coords.get(driver_id, list(self.driver_start_coords.values())[0])
                drivers.append(Driver(driver_id, start_pos, stp_time_minutes, self.max_orders_per_driver))
        return drivers

    def assign_order_groups_to_drivers(self, selected_order_groups: List[OrderGroup], stp_time_str: str, initial_driver_states: List[Driver] = None) -> Tuple[float, List[Driver], Dict]:
        hour, minute = map(int, stp_time_str.split(':'))
        stp_time_minutes = hour * 60 + minute
        drivers = self.initialize_drivers(stp_time_minutes, initial_driver_states)
        
        if not selected_order_groups:
            return 0.0, drivers, {'assignments': [], 'total_travel_cost': 0.0, 'total_penalty_cost': 0.0}

        return 0.0, drivers, {} # Fallback logic not fully implemented as Gurobi is primary


class GurobiSelectorAssigner:
    """使用Gurobi精确求解器替代OGSA和OGAH"""
    def __init__(self, food_delivery_optimizer):
        self.optimizer = food_delivery_optimizer
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.num_drivers = food_delivery_optimizer.num_drivers
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.customer_coords = food_delivery_optimizer.customer_coords
        self.order_restaurants = food_delivery_optimizer.order_restaurants
        self.estimated_delivery_duration = food_delivery_optimizer.estimated_delivery_duration
        self.driver_start_coords = food_delivery_optimizer.driver_start_coords
        self.theta_t = getattr(food_delivery_optimizer, 'theta_t', 0.3)
        self.travel_time_deviation_ratio = getattr(food_delivery_optimizer, 'travel_time_deviation_ratio', 0.2)
        # 新增
        self.order_ready_times = food_delivery_optimizer.order_ready_times
        self.order_volumes = food_delivery_optimizer.order_volumes
        self.driver_capacity = food_delivery_optimizer.driver_capacity

        
    def get_node_pos(self, node_str: str) -> Tuple[float, float]:
        parts = node_str.split('_')
        order_id = int(parts[1])
        if parts[0] == "pickup":
            restaurant_id = self.order_restaurants[order_id]
            return self.restaurant_coords[restaurant_id]
        else:
            return self.customer_coords[order_id]

    def solve(self, order_groups: List[OrderGroup], orders_in_stp: List[int], stp_time_str: str, initial_driver_states: List[Driver] = None) -> Tuple[float, List[Driver], Dict]:
        hour, minute = map(int, stp_time_str.split(':'))
        stp_time_minutes = hour * 60 + minute
        
        ogah_fallback = HybridOGAH(self.optimizer)
        drivers = ogah_fallback.initialize_drivers(stp_time_minutes, initial_driver_states)

        if not order_groups or not orders_in_stp:
            return 0.0, drivers, {'assignments': [], 'total_travel_cost': 0.0, 'total_penalty_cost': 0.0}

        num_groups = len(order_groups)
        model = gp.Model(f"VRP_SP_STP_{stp_time_str}")
        model.setParam('OutputFlag', 0)
        model.setParam('Threads', 16)
        
        y = model.addVars(num_groups, self.num_drivers, vtype=GRB.BINARY, name="y")
        
        total_cost = gp.LinExpr()
        total_travel_cost_expr = gp.LinExpr()
        total_penalty_cost_expr = gp.LinExpr()

        for k in range(self.num_drivers):
            driver = drivers[k]
            for i, og in enumerate(order_groups):
                # 使用 compute_rg2_robust_cost 计算成本
                # slot, slot_duration 在此上下文中不直接使用，可设为名义值
                actual_start_time = max(driver.current_time, stp_time_minutes)
                travel_cost, penalty_cost, cost, _, _ = compute_rg2_robust_cost(
                    og.sequence, driver.current_pos, actual_start_time, stp_time_minutes,
                    self.restaurant_coords, self.customer_coords, self.order_restaurants,
                    self.driver_speed, self.theta_t, self.travel_time_deviation_ratio,
                    self.travel_cost_per_unit, self.penalty_cost_per_unit,
                    self.estimated_delivery_duration, list(self.driver_start_coords.values())[0],
                    self.order_ready_times, self.order_volumes, self.driver_capacity
                )
                total_cost += cost * y[i, k]
                total_travel_cost_expr += travel_cost * y[i, k]
                total_penalty_cost_expr += penalty_cost * y[i, k]
        
        model.setObjective(total_cost, GRB.MINIMIZE)

        for order_id in orders_in_stp:
            model.addConstr(gp.quicksum(y[i, k] for i, og in enumerate(order_groups) if order_id in og.orders for k in range(self.num_drivers)) == 1, f"cover_{order_id}")
        
        for k in range(self.num_drivers):
            model.addConstr(gp.quicksum(len(og.orders) * y[i, k] for i, og in enumerate(order_groups)) <= self.optimizer.max_orders_per_driver, f"capacity_count_{k}")
            # 新增：体积容量约束
            model.addConstr(gp.quicksum(sum(self.order_volumes.get(oid, 1.0) for oid in og.orders) * y[i, k] for i, og in enumerate(order_groups)) <= self.driver_capacity, f"capacity_volume_{k}")

        model.optimize()

        if model.status != GRB.OPTIMAL:
            return float('inf'), drivers, {'assignments': [], 'message': 'Gurobi failed to find optimal solution'}

        final_drivers = copy.deepcopy(drivers)
        assignment_details = {'assignments': [], 'total_travel_cost': 0, 'total_penalty_cost': 0}
        
        for k in range(self.num_drivers):
            for i, og in enumerate(order_groups):
                if y[i, k].X > 0.5:
                    actual_start_time = max(final_drivers[k].current_time, stp_time_minutes)
                    travel_c, penalty_c, cost, final_pos, final_time = compute_rg2_robust_cost(
                        og.sequence, final_drivers[k].current_pos, actual_start_time, stp_time_minutes,
                        self.restaurant_coords, self.customer_coords, self.order_restaurants,
                        self.driver_speed, self.theta_t, self.travel_time_deviation_ratio,
                        self.travel_cost_per_unit, self.penalty_cost_per_unit,
                        self.estimated_delivery_duration, list(self.driver_start_coords.values())[0],
                        self.order_ready_times, self.order_volumes, self.driver_capacity
                    )
                    final_drivers[k].assign_order_group(og, cost, final_pos, final_time, 0)
                    assignment_details['assignments'].append({'order_group': og, 'driver_id': k, 'cost': cost, 'travel_cost': travel_c, 'penalty_cost': penalty_c})
                    assignment_details['total_travel_cost'] = assignment_details.get('total_travel_cost', 0) + travel_c
                    assignment_details['total_penalty_cost'] = assignment_details.get('total_penalty_cost', 0) + penalty_c
        
        
        return model.ObjVal, final_drivers, assignment_details

# =================================================================================
# == 代码从 Robust Hybrid.py 移植并适配 (结束)
# =================================================================================


class OCDAEvaluator:
    """OCDA算法评估器，用于评估定价方案的效果"""
    
    def __init__(self, debug_max_consumers=None, driver_capacity=15, num_drivers=30, k_dist=0.1, k_val=0.01):
        self.debug_max_consumers = debug_max_consumers
        self.driver_capacity = driver_capacity
        self.num_drivers = num_drivers
        self.k_dist = k_dist
        self.k_val = k_val
        self.setup_time_structure()
        self.load_base_data()
        self.prepare_solver_data()
        self.all_delivered_order_ids = set() # 新增：存储所有已成功配送的订单ID
        
    def setup_time_structure(self):
        """建立时间结构 - 严格按照论文设定"""
        # 11:00-15:00，每30分钟一个时间段，共8个时间段
        time_ranges = pd.date_range(start='2022-10-17 11:00:00', end='2022-10-17 15:00:00', freq='30min')
        self.time_periods = [f"{t.hour}:{t.minute:02d}-{(t + pd.Timedelta(minutes=30)).hour}:{(t + pd.Timedelta(minutes=30)).minute:02d}" 
                           for t in time_ranges[:-1]]
        
        # 每个时间段包含3个STP，每10分钟一个
        base_time = datetime(2022, 10, 17, 11, 0, 0)
        self.time_points = [(base_time + timedelta(minutes=10*i)).strftime('%H:%M') for i in range(24)]
        
        # 建立时间点到时间段的映射
        self.time_point_to_period = {}
        for i, time_point in enumerate(self.time_points):
            period_index = i // 3
            if period_index < len(self.time_periods):
                self.time_point_to_period[time_point] = self.time_periods[period_index]
            
    def prepare_solver_data(self):
        """准备求解器所需的数据结构"""
        # 创建一个模拟的优化器对象来存储所需参数
        self.mock_optimizer = SimpleNamespace()
        
        # 算法参数
        self.mock_optimizer.commission_rate = 0.18
        self.mock_optimizer.travel_cost_per_unit = 0.2  # 等同于 alpha
        self.mock_optimizer.penalty_cost_per_unit = 1.0   # 等同于 beta
        self.mock_optimizer.driver_speed = 20 / 60  # km/min，假设坐标单位为km
        self.mock_optimizer.max_orders_per_driver = 10
        self.mock_optimizer.num_drivers = self.num_drivers
        self.mock_optimizer.driver_capacity = self.driver_capacity # U_k

        self.mock_optimizer.theta_t = 0.3
        self.mock_optimizer.travel_time_deviation_ratio = 0.2
        self.mock_optimizer.slot_duration = 30 # Not directly used in cost, but good for context

        # 空间和订单信息
        # 顾客ID(consumer_id)从1开始，映射到DataFrame索引
        self.mock_optimizer.customer_coords = {
            # 错误来源：df_preferences没有坐标信息，应该从orders_df获取
            # index + 1 对应 consumer_id
            index + 1: (row.recipient_lng_decimal, row.recipient_lat_decimal)
            for index, row in self.orders_df.iterrows()
        }
        
        # 餐厅坐标 (去重)
        restaurant_set = self.orders_df[['sender_lng_decimal', 'sender_lat_decimal']].drop_duplicates()
        self.mock_optimizer.restaurant_coords = {i: (row.sender_lng_decimal, row.sender_lat_decimal) for i, row in enumerate(restaurant_set.itertuples())}
        # 创建一个反向映射，便于查找餐厅ID
        restaurant_pos_to_id = {v: k for k, v in self.mock_optimizer.restaurant_coords.items()}

        # 每个订单属于哪个餐厅
        self.mock_optimizer.order_restaurants = {}
        for idx, row in self.orders_df.iterrows():
            consumer_id = idx + 1 # consumer_id = index + 1
            pos = (row.sender_lng_decimal, row.sender_lat_decimal)
            if pos in restaurant_pos_to_id:
                self.mock_optimizer.order_restaurants[consumer_id] = restaurant_pos_to_id[pos]
        
        # 订单价值和预期配送时间，键应为 consumer_id (index + 1)
        self.mock_optimizer.order_values = {
            idx + 1: val for idx, val in self.orders_df['price'].items()
        }
        self.mock_optimizer.estimated_delivery_duration = {
            idx + 1: val for idx, val in self.orders_df['estimated_delivery_time'].items()
        }

        # 新增逻辑：处理餐厅备餐时间和订单体积 (从文件读取，提供默认值)
        base_date = pd.to_datetime(self.orders_df['platform_order_time'].iloc[0]).date()
        start_of_day = pd.Timestamp(base_date)
        
        # 将平台下单时间转换为分钟数
        self.orders_df['order_time_minutes'] = (pd.to_datetime(self.orders_df['platform_order_time']) - start_of_day).dt.total_seconds() / 60

        self.mock_optimizer.order_ready_times = {
            idx + 1: row['preparation_time'] / 2
            for idx, row in self.orders_df.iterrows()
        }
        self.mock_optimizer.order_volumes = {
            idx + 1: row['volume'] for idx, row in self.orders_df.iterrows()
        }
        
        # 添加 orders_df 引用，供 HybridOGGM 访问
        self.mock_optimizer.orders_df = self.orders_df

        # 司机起始位置
        grab_positions = self.orders_df[['grab_lng_decimal', 'grab_lat_decimal']].dropna()
        grab_positions = grab_positions[(grab_positions['grab_lng_decimal'] != 0) & (grab_positions['grab_lat_decimal'] != 0)]
        
        if len(grab_positions) < self.mock_optimizer.num_drivers:
            driver_pos_list = grab_positions.values.tolist()
        else:
            driver_pos_list = grab_positions.sample(n=self.mock_optimizer.num_drivers, random_state=42).values.tolist()
        
        self.mock_optimizer.driver_start_coords = {i: (pos[0], pos[1]) for i, pos in enumerate(driver_pos_list)}

        
    def load_base_data(self):
        """加载基础数据"""
        try:
            self.df_preferences = pd.read_csv('consumer_delivery_fee_preferences.csv')
            self.orders_df = pd.read_csv('meituan_orders_with_delivery_time.csv')
            
            # 计算时间段订单统计 (基于原始订单时间，作为基准)
            self.orders_df['platform_order_time'] = pd.to_datetime(self.orders_df['platform_order_time'])
            
            self.period_orders = {}
            time_ranges = pd.date_range(start='2022-10-17 11:00:00', end='2022-10-17 15:00:00', freq='30min')

            for i in range(len(self.time_periods)):
                period = self.time_periods[i]
                start_time = time_ranges[i]
                end_time = time_ranges[i+1]
                count = self.orders_df[(self.orders_df['platform_order_time'] >= start_time) & 
                                       (self.orders_df['platform_order_time'] < end_time)].shape[0]
                self.period_orders[period] = count
                
        except Exception as e:
            print(f"基础数据加载失败: {e}")
            raise
        
    def evaluate_pricing_scheme(self, period_price_map):
        """评估定价方案的效果 - 严格按照论文公式计算"""
        try:
            self.all_delivered_order_ids.clear() # 在每次评估新定价方案前清空
            
            # 1. 根据新的定价方案计算消费者STP选择（论文中的CIIP求解）
            new_stp_choices = self.calculate_stp_choices(period_price_map)
            purchase_count = len([c for c in new_stp_choices if c['choice_type'] == 'purchase'])
            
            # 2. 如果没有消费者购买，返回极低利润
            if purchase_count == 0:
                print("警告：没有消费者购买，返回极低利润")
                return float('-inf'), 0, float('inf')
            
            
            # 3. 生成临时STP选择文件
            temp_stp_file = self.create_temp_stp_file(new_stp_choices)
            
            # 4. 调用OCDA算法（按照论文顺序：OGGM -> OGSA -> OGAH）
            cost_breakdown, revenue_breakdown = self.run_hybrid_solver(temp_stp_file, period_price_map)
            
            # 5. 计算总利润（严格按照论文公式(5): Q(y,ξw) = R̃w - ∑Qt(y,ξw)）
            total_cost = cost_breakdown['total_cost']
            total_revenue = revenue_breakdown['total_revenue']
            total_profit = total_revenue - total_cost
            
            
            # 6. 清理临时文件
            self.cleanup_temp_files(temp_stp_file)
            
            return total_profit, total_revenue, total_cost
            
        except Exception as e:
            print(f"评估定价方案时出错: {e}")
            import traceback
            traceback.print_exc()
            return float('-inf'), 0, float('inf')
    
    def calculate_stp_choices(self, period_base_price_map):
        """根据新的基础定价方案和动态费用，计算消费者STP选择"""
        
        # 预计算骑手平均起始位置，用于预估距离
        avg_rider_pos = np.mean(list(self.mock_optimizer.driver_start_coords.values()), axis=0)
        
        time_point_prices = {}
        for time_point, period in self.time_point_to_period.items():
            if period in period_base_price_map:
                time_point_prices[time_point] = period_base_price_map[period]
            else:
                time_point_prices[time_point] = None
        
        consumer_choices = []
        df_prefs_to_use = self.df_preferences.head(self.debug_max_consumers) if self.debug_max_consumers is not None else self.df_preferences

        for _, row in df_prefs_to_use.iterrows():
            consumer_id = row['consumer_id']
            preference_str = row['preferences']
            consumer_preferences = self.parse_consumer_preferences(preference_str)
            
            # 为该消费者构建包含动态费用的可用选择列表
            available_tpcs_dynamic = []
            order_info = self.orders_df.iloc[consumer_id - 1]
            order_value = order_info['price']
            
            for time_point, base_price in time_point_prices.items():
                if base_price is not None:
                    # 预估距离 = 平均骑手位置到餐厅 + 餐厅到顾客
                    dist_to_rest = haversine(avg_rider_pos[0], avg_rider_pos[1], order_info['sender_lng_decimal'], order_info['sender_lat_decimal'])
                    dist_rest_to_cust = haversine(order_info['sender_lng_decimal'], order_info['sender_lat_decimal'], order_info['recipient_lng_decimal'], order_info['recipient_lat_decimal'])
                    estimated_dist = dist_to_rest + dist_rest_to_cust
                    
                    # 预估动态费用
                    dynamic_fee = base_price + self.k_dist * estimated_dist + self.k_val * order_value
                    # 将费用四舍五入到最接近的整数，以匹配偏好列表
                    rounded_fee = int(round(dynamic_fee))
                    
                    available_tpcs_dynamic.append((time_point, rounded_fee))

            available_tpcs_dynamic.append(("不买", None))
            
            optimal_choice, preference_rank = self.solve_ciip_for_consumer(
                consumer_preferences, available_tpcs_dynamic)
            
            choice_type = 'purchase' if optimal_choice[0] != "不买" else 'no_purchase'
            
            consumer_choices.append({
                'consumer_id': consumer_id,
                'chosen_time': optimal_choice[0],
                'chosen_price': optimal_choice[1],
                'choice_type': choice_type,
                'preference_rank': preference_rank,
                'time_period': self.time_point_to_period.get(optimal_choice[0]) if choice_type == 'purchase' else None
            })
        
        return consumer_choices
    
    def parse_consumer_preferences(self, preference_str):
        """解析消费者偏好列表字符串"""
        preferences = []
        items = preference_str.split(', ')
        
        for item in items:
            if item == "不买":
                preferences.append(("不买", None))
            else:
                parts = item.split('-')
                time = parts[0]
                price_str = parts[1].replace('元', '')
                price = int(price_str)
                preferences.append((time, price))
        
        return preferences
    
    def solve_ciip_for_consumer(self, consumer_preferences, available_tpcs):
        """为单个消费者求解CIIP - 严格按照论文附录D的算法"""
        available_set = set(available_tpcs)
        
        # 按偏好顺序查找第一个可用的TPC（论文约束D.3和D.4）
        for rank, preference_tpc in enumerate(consumer_preferences):
            if preference_tpc in available_set:
                return preference_tpc, rank + 1
        
        # 如果没有找到匹配的偏好，选择不购买
        return ("不买", None), len(consumer_preferences)
    
    def create_temp_stp_file(self, stp_choices):
        """创建临时STP选择文件"""
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='_stp_choices.csv', delete=False)
        temp_filename = temp_file.name
        temp_file.close()
        
        stp_df = pd.DataFrame(stp_choices)
        stp_df.to_csv(temp_filename, index=False)
        
        return temp_filename
    
    def run_hybrid_solver(self, stp_file, period_base_price_map):
        """运行新的混合求解器 (HybridOGGM + GurobiSelectorAssigner)"""
        try:
            # 1. 准备数据
            stp_df = pd.read_csv(stp_file)
            purchase_df = stp_df[stp_df['choice_type'] == 'purchase']

            # 按STP对订单进行分组
            orders_by_stp = {}
            for _, row in purchase_df.iterrows():
                # 注意：此处应该使用 chosen_time 来确定订单属于哪个10分钟的STP
                stp_time_point = row['chosen_time']
                if stp_time_point not in orders_by_stp:
                    orders_by_stp[stp_time_point] = []
                orders_by_stp[stp_time_point].append(row['consumer_id'])

            # 2. 初始化求解器和司机状态
            oggm = HybridOGGM(self.mock_optimizer)
            gurobi_solver = GurobiSelectorAssigner(self.mock_optimizer)
            
            initial_driver_states = [
                Driver(
                    driver_id=i,
                    start_pos=self.mock_optimizer.driver_start_coords[i],
                    start_time=0,
                    capacity=self.mock_optimizer.max_orders_per_driver
                ) for i in range(self.mock_optimizer.num_drivers)
            ]
            
            # 3. 按STP顺序求解
            total_cost = 0
            total_travel_cost = 0
            total_penalty_cost = 0
            current_driver_states = initial_driver_states
            
            # 使用 time_points 列表来保证STP的正确顺序
            for stp_time_point in self.time_points:
                if stp_time_point in orders_by_stp:
                    orders_in_this_stp = orders_by_stp[stp_time_point]
                    
                    # 使用第一个司机的状态作为代表性状态来生成订单组
                    rep_driver_state = current_driver_states[0]
                    
                    # Step 3.1: OGGM生成订单组
                    order_groups = oggm.generate_order_groups_for_stp(
                        orders_in_this_stp, 
                        stp_time_point,
                        rep_driver_state.current_pos,
                        rep_driver_state.current_time
                    )
                    
                    # Step 3.2: Gurobi选择并分配
                    stp_cost, final_drivers, assignment_details = gurobi_solver.solve(
                        order_groups,
                        orders_in_this_stp,
                        stp_time_point,
                        current_driver_states
                    )
                    
                    if stp_cost == float('inf'): # Gurobi求解失败
                         print(f"警告: Gurobi在STP {stp_time_point}求解失败，成本设为无穷大")
                         total_cost = float('inf')
                         break

                    # 收集当前STP中实际配送的订单ID
                    delivered_orders_in_this_stp = set()
                    for assignment in assignment_details['assignments']:
                        delivered_orders_in_this_stp.update(assignment['order_group'].orders)
                    self.all_delivered_order_ids.update(delivered_orders_in_this_stp)

                    # 更新成本和司机状态
                    total_cost += stp_cost
                    total_travel_cost += assignment_details.get('total_travel_cost', 0)
                    total_penalty_cost += assignment_details.get('total_penalty_cost', 0)
                    current_driver_states = final_drivers

            # 4. 构建成本和收益分解
            cost_breakdown = {
                'total_cost': total_cost,
                'total_travel_cost': total_travel_cost,
                'total_penalty_cost': total_penalty_cost,
                'cost_composition': {
                    'travel_percentage': (total_travel_cost / total_cost * 100) if total_cost > 0 else 0,
                    'penalty_percentage': (total_penalty_cost / total_cost * 100) if total_cost > 0 else 0
                } if total_cost < float('inf') else {}
            }
            
            revenue_breakdown = self.calculate_revenue_breakdown_paper_compliant(stp_file, period_base_price_map, self.all_delivered_order_ids)
            
            return cost_breakdown, revenue_breakdown

        except gp.GurobiError as e:
            print(f"Gurobi错误: {e.message} (代码: {e.errno})")
            # 当Gurobi出错时（例如，没有许可证），返回极差的结果
            return {'total_cost': float('inf')}, {'total_revenue': 0}
        except Exception as e:
            print(f"混合求解器执行失败: {e}")
            import traceback
            traceback.print_exc()
            raise

    def calculate_revenue_breakdown_paper_compliant(self, stp_file, period_base_price_map, delivered_order_ids: Set[int]):
        """计算收益分解（已修改为与动态费用一致）"""
        
        stp_df = pd.read_csv(stp_file)
        purchase_df = stp_df[stp_df['choice_type'] == 'purchase']
        
        # 仅考虑实际配送成功的订单来计算收益
        purchase_df = purchase_df[purchase_df['consumer_id'].isin(delivered_order_ids)]
        
        total_revenue = 0
        total_commission = 0
        total_delivery_fees = 0
        commission_rate = 0.18
        order_count = 0
        
        # 预计算骑手平均起始位置，用于预估距离 (与客户选择阶段逻辑保持一致)
        avg_rider_pos = np.mean(list(self.mock_optimizer.driver_start_coords.values()), axis=0)

        for _, row in purchase_df.iterrows():
            consumer_id = row['consumer_id']
            order_info = self.orders_df.iloc[consumer_id - 1]
            order_value = order_info['price']

            # 计算佣金
            commission = order_value * commission_rate
            
            # 使用与客户选择阶段一致的逻辑来计算实际的动态配送费
            time_period = self.time_point_to_period.get(row['chosen_time'])
            base_price = period_base_price_map.get(time_period, 5) # Default to 5 if not found

            dist_to_rest = haversine(avg_rider_pos[0], avg_rider_pos[1], order_info['sender_lng_decimal'], order_info['sender_lat_decimal'])
            dist_rest_to_cust = haversine(order_info['sender_lng_decimal'], order_info['sender_lat_decimal'], order_info['recipient_lng_decimal'], order_info['recipient_lat_decimal'])
            estimated_dist = dist_to_rest + dist_rest_to_cust
            
            delivery_fee = base_price + self.k_dist * estimated_dist + self.k_val * order_value
            
            # 累加收益、佣金和配送费
            order_revenue = commission + delivery_fee
            total_revenue += order_revenue
            total_commission += commission
            total_delivery_fees += delivery_fee
            order_count += 1
            
        return {
            'total_revenue': total_revenue,
            'total_commission': total_commission,
            'total_delivery_fees': total_delivery_fees,
            'order_count': order_count,
            'stp_breakdown': {}, # 简化
            'revenue_composition': {} # 简化
        }
    
    def cleanup_temp_files(self, *files):
        """清理临时文件"""
        for file in files:
            if file and os.path.exists(file):
                try:
                    os.unlink(file)
                except:
                    pass


class DestroyRepairOperators:
    """破坏算子和修复算子实现 - 严格按照论文5.1.2描述"""
    
    def __init__(self, debug_max_consumers=None, driver_capacity=15, num_drivers=30, k_dist=0.1, k_val=0.01):
        self.evaluator = OCDAEvaluator(debug_max_consumers=debug_max_consumers,
                                       driver_capacity=driver_capacity,
                                       num_drivers=num_drivers,
                                       k_dist=k_dist,
                                       k_val=k_val)
        self.price_set = [2, 3, 4, 5, 6, 7, 8, 9]  # 论文设定的价格集合
        
        # HALNS算法参数（严格按照Table 4）
        self.eta = 40          # η: 段内迭代数
        self.sigma_1 = 30      # σ̄₁: 新最佳解评分
        self.sigma_2 = 9       # σ̄₂: 更好解评分
        self.sigma_3 = 3       # σ̄₃: 被接受恶化解评分
        self.r = 0.1           # r: 反应因子
        self.alpha_cooling = 0.975  # ᾱ: 冷却率
        self.w_hat = 0.05      # ŵ: 恶化百分比(5%)
        self.delta_hat = 7     # δ̂: 可接受邻域大小
        
        # 定义破坏算子（严格按照论文5.1.2）
        self.destroy_operators = [
            ("random_removal", self.random_removal),
            ("maximum_deviation_removal", self.maximum_deviation_removal),
            ("random_pair_removal", self.random_pair_removal),
            ("max_min_removal", self.max_min_removal),
            ("differentiated_period_removal", self.differentiated_period_removal)
        ]
        
        # 定义修复算子（严格按照论文5.1.2）
        self.repair_operators = [
            ("greedy_assignment_repair", self.greedy_assignment_repair),
            ("admissible_neighborhood_repair", self.admissible_neighborhood_repair)
        ]
        
        # 初始化权重（按照论文5.1.3的自适应机制）
        self.destroy_weights = [1.0] * len(self.destroy_operators)
        self.repair_weights = [1.0] * len(self.repair_operators)
        
        # 当前段的统计信息（论文附录I）
        self.destroy_scores = [0.0] * len(self.destroy_operators)
        self.repair_scores = [0.0] * len(self.repair_operators)
        self.destroy_uses = [0] * len(self.destroy_operators)
        self.repair_uses = [0] * len(self.repair_operators)
        
        # 温度相关（论文附录I的模拟退火）
        self.temperature = None
        self.initial_temperature = None
        
        # 解记录
        self.best_solution = None
        self.best_profit = float('-inf')
        self.current_solution = None
        self.current_profit = float('-inf')
        
        
    # ===== 破坏算子（严格按照论文5.1.2）=====
    
    def random_removal(self, period_price_map, period_orders):
        """随机删除算子 - 随机选择一个时间段并移除其价格"""
        result = period_price_map.copy()
        selected_period = random.choice(list(result.keys()))
        del result[selected_period]
        return result, [selected_period]
    
    def maximum_deviation_removal(self, period_price_map, period_orders):
        """最大偏差删除算子 - 选择与平均订单数偏差最大的时间段"""
        result = period_price_map.copy()
        avg_orders = np.mean(list(period_orders.values()))
        max_deviation = 0
        max_dev_period = None
        
        for period, orders in period_orders.items():
            deviation = abs(orders - avg_orders)
            if deviation > max_deviation:
                max_deviation = deviation
                max_dev_period = period
        
        del result[max_dev_period]
        return result, [max_dev_period]
    
    def random_pair_removal(self, period_price_map, period_orders):
        """随机成对删除算子 - 随机选择一对时间段"""
        result = period_price_map.copy()
        periods = list(result.keys())
        if len(periods) < 2:
            return result, []
        
        selected_pair = random.sample(periods, 2)
        for period in selected_pair:
            del result[period]
        
        return result, selected_pair
    
    def max_min_removal(self, period_price_map, period_orders):
        """最大最小删除算子 - 选择订单量最大和最小的时间段"""
        result = period_price_map.copy()
        
        sorted_periods = sorted(period_orders.items(), key=lambda x: x[1])
        
        min_period = sorted_periods[0][0]   # 订单量最少
        max_period = sorted_periods[-1][0]  # 订单量最多
        
        del result[min_period]
        del result[max_period]
        
        return result, [min_period, max_period]
    
    def differentiated_period_removal(self, period_price_map, period_orders):
        """差异化时段删除算子 - 分别选择高峰和非高峰时段"""
        result = period_price_map.copy()
        
        avg_orders = np.mean(list(period_orders.values()))
        
        peak_periods = [period for period, count in period_orders.items() if count >= avg_orders]
        non_peak_periods = [period for period, count in period_orders.items() if count < avg_orders]
        
        if not peak_periods or not non_peak_periods:
            return result, []
        
        peak_period = random.choice(peak_periods)
        non_peak_period = random.choice(non_peak_periods)
        
        del result[peak_period]
        del result[non_peak_period]
        
        return result, [peak_period, non_peak_period]
    
    # ===== 修复算子（严格按照论文5.1.2）=====
    
    def calculate_optional_price_set(self, target_period, current_pricing, period_orders):
        """计算可选价格集合 - 按照论文5.1.2的方法"""
        optional_prices = set()
        periods = self.evaluator.time_periods
        target_idx = periods.index(target_period)
        
        # 第一个价格集合：位于前后时间段价格之间的价格点
        prev_price = None
        next_price = None
        
        for i in range(target_idx - 1, -1, -1):
            if periods[i] in current_pricing:
                prev_price = current_pricing[periods[i]]
                break
        
        for i in range(target_idx + 1, len(periods)):
            if periods[i] in current_pricing:
                next_price = current_pricing[periods[i]]
                break
        
        if prev_price is not None and next_price is not None:
            min_price = min(prev_price, next_price)
            max_price = max(prev_price, next_price)
            for price in self.price_set:
                if min_price <= price <= max_price:
                    optional_prices.add(price)
        
        # 第二个价格集合：基于订单量相对位置的价格点
        target_orders = period_orders[target_period]
        sorted_by_orders = sorted(period_orders.items(), key=lambda x: x[1])
        target_order_idx = next(i for i, (p, _) in enumerate(sorted_by_orders) if p == target_period)
        
        higher_period_price = None
        for i in range(target_order_idx + 1, len(sorted_by_orders)):
            period = sorted_by_orders[i][0]
            if period in current_pricing:
                higher_period_price = current_pricing[period]
                break
        
        lower_period_price = None
        for i in range(target_order_idx - 1, -1, -1):
            period = sorted_by_orders[i][0]
            if period in current_pricing:
                lower_period_price = current_pricing[period]
                break
        
        if higher_period_price is not None and lower_period_price is not None:
            min_price = min(higher_period_price, lower_period_price)
            max_price = max(higher_period_price, lower_period_price)
            for price in self.price_set:
                if min_price <= price <= max_price:
                    optional_prices.add(price)
        
        # 如果可选价格集合为空，使用所有价格
        if not optional_prices:
            optional_prices = set(self.price_set)
        
        return sorted(list(optional_prices))
    
    def greedy_assignment_repair(self, destroyed_solution, removed_periods, period_orders, max_candidates=5):
        """贪心分配修复算子 - 在可选价格集合中选择最佳价格"""
        candidate_solutions = []
        
        if len(removed_periods) == 1:
            period = removed_periods[0]
            optional_prices = self.calculate_optional_price_set(period, destroyed_solution, period_orders)
            
            price_profits = []
            for price in optional_prices:
                test_solution = destroyed_solution.copy()
                test_solution[period] = price
                
                try:
                    profit, revenue, cost = self.evaluator.evaluate_pricing_scheme(test_solution)
                    price_profits.append((test_solution, profit))
                except Exception as e:
                    continue
            
            price_profits.sort(key=lambda x: x[1], reverse=True)
            candidate_solutions = price_profits[:max_candidates]
            
        else:
            # 多个时间段的组合枚举
            period_optional_prices = {}
            for period in removed_periods:
                optional_prices = self.calculate_optional_price_set(period, destroyed_solution, period_orders)
                period_optional_prices[period] = optional_prices[:3]
            
            combinations = list(product(*[period_optional_prices[p] for p in removed_periods]))
            
            if len(combinations) > max_candidates * 3:
                combinations = random.sample(combinations, max_candidates * 3)
            
            combination_profits = []
            for combination in combinations:
                test_solution = destroyed_solution.copy()
                for j, period in enumerate(removed_periods):
                    test_solution[period] = combination[j]
                
                try:
                    profit, revenue, cost = self.evaluator.evaluate_pricing_scheme(test_solution)
                    combination_profits.append((test_solution, profit))
                except Exception as e:
                    continue
            
            combination_profits.sort(key=lambda x: x[1], reverse=True)
            candidate_solutions = combination_profits[:max_candidates]
        
        # 备选解
        if not candidate_solutions:
            backup_solution = destroyed_solution.copy()
            median_price = self.price_set[len(self.price_set) // 2]
            for period in removed_periods:
                backup_solution[period] = median_price
            
            try:
                backup_profit, _, _ = self.evaluator.evaluate_pricing_scheme(backup_solution)
                candidate_solutions = [(backup_solution, backup_profit)]
            except:
                candidate_solutions = [(backup_solution, float('-inf'))]
        
        return candidate_solutions
    
    def admissible_neighborhood_repair(self, destroyed_solution, removed_periods, period_orders, max_candidates=5):
        """可接受邻域修复算子 - 在可接受价格集合中随机选择"""
        candidate_solutions = []
        
        if len(removed_periods) == 1:
            period = removed_periods[0]
            optional_prices = self.calculate_optional_price_set(period, destroyed_solution, period_orders)
            
            if len(optional_prices) <= self.delta_hat:
                admissible_prices = optional_prices
            else:
                admissible_prices = random.sample(optional_prices, self.delta_hat)
            
            price_profits = []
            for price in admissible_prices:
                test_solution = destroyed_solution.copy()
                test_solution[period] = price
                
                try:
                    profit, revenue, cost = self.evaluator.evaluate_pricing_scheme(test_solution)
                    price_profits.append((test_solution, profit))
                except Exception as e:
                    continue
            
            price_profits.sort(key=lambda x: x[1], reverse=True)
            candidate_solutions = price_profits[:max_candidates]
            
        else:
            period_optional_prices = {}
            for period in removed_periods:
                optional_prices = self.calculate_optional_price_set(period, destroyed_solution, period_orders)
                if len(optional_prices) <= 3:
                    period_optional_prices[period] = optional_prices
                else:
                    period_optional_prices[period] = random.sample(optional_prices, 3)
            
            combinations = list(product(*[period_optional_prices[p] for p in removed_periods]))
            
            if len(combinations) > self.delta_hat:
                selected_combinations = random.sample(combinations, self.delta_hat)
            else:
                selected_combinations = combinations
            
            combination_profits = []
            for combination in selected_combinations:
                test_solution = destroyed_solution.copy()
                for j, period in enumerate(removed_periods):
                    test_solution[period] = combination[j]
                
                try:
                    profit, revenue, cost = self.evaluator.evaluate_pricing_scheme(test_solution)
                    combination_profits.append((test_solution, profit))
                except Exception as e:
                    continue
            
            combination_profits.sort(key=lambda x: x[1], reverse=True)
            candidate_solutions = combination_profits[:max_candidates]
        
        if not candidate_solutions:
            return self.greedy_assignment_repair(destroyed_solution, removed_periods, period_orders, max_candidates)
        
        return candidate_solutions
    
    # ===== HALNS算法核心方法（严格按照论文5.1.3和附录I）=====
    
    def roulette_wheel_selection(self, weights):
        """轮盘赌选择 - 按照论文5.1.3的自适应机制"""
        total_weight = sum(weights)
        if total_weight == 0:
            return random.randint(0, len(weights) - 1)
        
        r = random.uniform(0, total_weight)
        cumulative = 0
        for i, weight in enumerate(weights):
            cumulative += weight
            if r <= cumulative:
                return i
        return len(weights) - 1

    def initialize_temperature(self, initial_solution, initial_profit):
        """初始化温度 - 严格按照论文附录I的描述"""
        worse_profit = initial_profit * (1 - self.w_hat)
        delta = abs(initial_profit - worse_profit)
        
        if delta > 0:
            # T_start设置使得ŵ%恶化的解以40%概率被接受
            self.initial_temperature = delta / abs(math.log(0.4))
        else:
            self.initial_temperature = 1000
        
        self.temperature = self.initial_temperature

    def accept_solution(self, new_profit, current_profit):
        """模拟退火接受准则 - 严格按照论文附录I"""
        if new_profit >= current_profit:
            return True
        
        delta = current_profit - new_profit
        probability = math.exp(-delta / self.temperature)
        return random.random() < probability

    def update_scores(self, destroy_idx, repair_idx, score_type):
        """更新算子评分 - 按照论文附录I的评分规则"""
        score_values = {
            'best': self.sigma_1,     # σ̄₁ = 30
            'better': self.sigma_2,   # σ̄₂ = 9
            'accepted': self.sigma_3  # σ̄₃ = 3
        }
        
        score = score_values.get(score_type, 0)
        self.destroy_scores[destroy_idx] += score
        self.repair_scores[repair_idx] += score

    def update_weights(self):
        """更新权重 - 严格按照论文附录I公式(I.1)"""
        # ω_{i,j+1} = (1-r)ω_{ij} + r(π_i/θ̄_{ij}) if θ̄_{ij} ≠ 0
        
        for i in range(len(self.destroy_operators)):
            if self.destroy_uses[i] > 0:
                avg_score = self.destroy_scores[i] / self.destroy_uses[i]
                self.destroy_weights[i] = (1 - self.r) * self.destroy_weights[i] + self.r * avg_score
        
        for i in range(len(self.repair_operators)):
            if self.repair_uses[i] > 0:
                avg_score = self.repair_scores[i] / self.repair_uses[i]
                self.repair_weights[i] = (1 - self.r) * self.repair_weights[i] + self.r * avg_score

    def reset_segment_stats(self):
        """重置段统计信息"""
        self.destroy_scores = [0.0] * len(self.destroy_operators)
        self.repair_scores = [0.0] * len(self.repair_operators)
        self.destroy_uses = [0] * len(self.destroy_operators)
        self.repair_uses = [0] * len(self.repair_operators)

    def run_halns(self, max_iterations=200):
        """运行HALNS算法 - 严格按照论文Algorithm 1和附录I"""
        
        start_time = time.time()
        
        # Step 1: 生成初始解（论文Algorithm 2）
        initial_solution = self.generate_initial_pricing_scheme()
        

        try:
            initial_profit, initial_revenue, initial_cost = self.evaluator.evaluate_pricing_scheme(initial_solution)

        except Exception as e:
            print(f"初始解评估失败: {e}")
            return None, None
        
        # Step 3: 初始化（论文Algorithm 1 Step 2）
        self.current_solution = initial_solution.copy()
        self.current_profit = initial_profit
        self.best_solution = initial_solution.copy()
        self.best_profit = initial_profit
        
        # Step 4: 初始化温度（论文附录I）
        self.initialize_temperature(initial_solution, initial_profit)
        
        period_orders = self.evaluator.period_orders
        
        # Step 5: 主循环（论文Algorithm 1 Step 3-27）
        iteration = 0
        accepted_count = 0
        improved_count = 0
        best_found_iteration = 0
        
        
        while iteration < max_iterations:
            iteration += 1
            
            # 段管理（论文Algorithm 1 Step 23-25）
            if (iteration - 1) % self.eta == 0:
                if iteration > 1:
                    self.update_weights()

                
                self.reset_segment_stats()
            

            
            # Step 5: 选择算子（论文Algorithm 1 Step 5）
            destroy_idx = self.roulette_wheel_selection(self.destroy_weights)
            repair_idx = self.roulette_wheel_selection(self.repair_weights)
            
            destroy_name, destroy_op = self.destroy_operators[destroy_idx]
            repair_name, repair_op = self.repair_operators[repair_idx]
            

            
            # Step 8: 更新使用次数
            self.destroy_uses[destroy_idx] += 1
            self.repair_uses[repair_idx] += 1
            
            try:
                # Step 6: 应用破坏和修复算子（论文Algorithm 1 Step 6）
                destroyed_solution, removed_periods = destroy_op(self.current_solution, period_orders)
                
                candidate_solutions = repair_op(destroyed_solution, removed_periods, period_orders)
                
                if not candidate_solutions:
                    continue
                
                # Step 7: 评估候选解并选择最佳的
                best_candidate_solution, best_candidate_profit = max(candidate_solutions, key=lambda x: x[1])
                
                # Step 9-22: 接受准则和评分更新
                score_type = None
                accept = False
                
                if best_candidate_profit > self.best_profit:
                    # 找到新的最佳解（论文Algorithm 1 Step 11-14）
                    self.best_solution = best_candidate_solution.copy()
                    self.best_profit = best_candidate_profit
                    self.current_solution = best_candidate_solution.copy()
                    self.current_profit = best_candidate_profit
                    score_type = 'best'
                    accept = True
                    best_found_iteration = iteration
                    improved_count += 1
                    
                elif best_candidate_profit > self.current_profit:
                    # 找到更好的解（论文Algorithm 1 Step 9-10）
                    self.current_solution = best_candidate_solution.copy()
                    self.current_profit = best_candidate_profit
                    score_type = 'better'
                    accept = True
                    improved_count += 1
                    
                elif self.accept_solution(best_candidate_profit, self.current_profit):
                    # 接受恶化解（论文Algorithm 1 Step 18-20）
                    self.current_solution = best_candidate_solution.copy()
                    self.current_profit = best_candidate_profit
                    score_type = 'accepted'
                    accept = True
                
                if accept:
                    accepted_count += 1
                
                # 更新评分（论文Algorithm 1 Step 13, 15, 20）
                if score_type:
                    self.update_scores(destroy_idx, repair_idx, score_type)
                
            except Exception as e:
                continue
            
            # Step 26: 更新温度（论文Algorithm 1）
            self.temperature *= self.alpha_cooling
            
            # 进度报告
            if iteration % 20 == 0:
                elapsed = time.time() - start_time
        
        # Step 28: 返回最佳解（论文Algorithm 1）
        total_time = time.time() - start_time
        
        print("\n" + "="*80)
        print("HALNS算法完成（严格按照论文实现）")
        print("="*80)
        print(f"总迭代数: {max_iterations}")
        print(f"总运行时间: {total_time:.1f}秒")
        print(f"平均每次迭代: {total_time/max_iterations:.2f}秒")
        print(f"总接受数: {accepted_count}")
        print(f"总改善数: {improved_count}")
        print(f"接受率: {accepted_count/max_iterations*100:.1f}%")
        print(f"改善率: {improved_count/max_iterations*100:.1f}%")
        print(f"最后改善迭代: {best_found_iteration}")
        
        print(f"\n最佳解:")
        for period, price in self.best_solution.items():
            orders = period_orders.get(period, 0)
            print(f"  {period}: {price}元 (订单量: {orders})")
        
        # 最终详细评估
        print(f"\n最佳解详细性能分析:")
        try:
            final_profit, final_revenue, final_cost = self.evaluator.evaluate_pricing_scheme(self.best_solution)
            
            print(f"\n  === 最终结果（严格按照论文公式计算）===")
            print(f"  总利润: {final_profit:.2f} RMB")
            print(f"  总收益: {final_revenue:.2f} RMB")
            print(f"  总成本: {final_cost:.2f} RMB")
            print(f"  利润率: {(final_profit / final_revenue * 100):.2f}%")
            
            # 与初始解比较
            profit_improvement = final_profit - initial_profit
            print(f"\n  === 与初始解比较 ===")
            print(f"  利润改善: {profit_improvement:+.2f} RMB")
            if initial_profit != 0:
                print(f"  利润改善率: {profit_improvement/abs(initial_profit)*100:+.2f}%")
                
        except Exception as e:
            print(f"最终详细评估失败: {e}")       
        
        return self.best_solution, self.best_profit
 
    # ===== 初始解生成方法（论文Algorithm 2）=====
    
    def generate_initial_pricing_scheme(self):
        """生成初始定价方案 - 严格按照论文Algorithm 2"""
        print("生成初始定价方案（论文Algorithm 2）...")
        
        # 生成5种候选方案并选择最佳的
        candidate_schemes = []
        
        # 方案1：峰值定价（论文Algorithm 2的主要思路）
        candidate_schemes.append(("峰值定价", self.generate_peak_pricing_scheme()))
        
        # 方案2-5：基于高峰/非高峰时段的四种组合
        peak_non_peak_schemes = self.generate_peak_non_peak_schemes()
        for i, scheme in enumerate(peak_non_peak_schemes):
            candidate_schemes.append((f"高峰非高峰组合{i+1}", scheme))
        
        # 评估所有候选方案
        best_scheme = None
        best_profit = float('-inf')
        best_name = ""

        print("正在评估候选初始方案...")
        for name, scheme in candidate_schemes:
            try:
                profit, revenue, cost = self.evaluator.evaluate_pricing_scheme(scheme)
                print(f"  {name}: 利润={profit:.2f}, 收益={revenue:.2f}, 成本={cost:.2f}")
                
                if profit > best_profit:
                    best_profit = profit
                    best_scheme = scheme.copy()
                    best_name = name
                    
            except Exception as e:
                print(f"  {name}: 评估失败 - {e}")
                continue

        if best_scheme is not None:
            print(f"选择最佳初始方案: {best_name} (利润: {best_profit:.2f})")
        else:
            print("所有候选方案评估失败，使用默认峰值定价方案")
            return self.generate_peak_pricing_scheme()

        return best_scheme

    def generate_peak_pricing_scheme(self):
        """生成峰值定价方案 - 按照论文Algorithm 2的思路"""
        period_orders = self.evaluator.period_orders
        sorted_periods = sorted(period_orders.items(), key=lambda x: x[1], reverse=True)
        
        initial_pricing = {}
        price_set_desc = sorted(self.price_set, reverse=True)
        
        for i, (period, _) in enumerate(sorted_periods):
            if i < len(price_set_desc):
                initial_pricing[period] = price_set_desc[i]
            else:
                initial_pricing[period] = price_set_desc[-1]
        
        return initial_pricing

    def generate_peak_non_peak_schemes(self):
        """生成基于高峰/非高峰时段的四种定价方案 - 按照论文Algorithm 2扩展"""
        period_orders = self.evaluator.period_orders
        
        # 计算平均订单数量（论文Algorithm 2的ξ̄）
        avg_orders = np.mean(list(period_orders.values()))
        
        # 划分高峰和非高峰时间段（论文Algorithm 2的T̂₁和T̂₂）
        peak_periods = [period for period, count in period_orders.items() if count >= avg_orders]
        non_peak_periods = [period for period, count in period_orders.items() if count < avg_orders]
        
        # 确保所有时间段都被覆盖
        all_periods = set(period_orders.keys())
        covered_periods = set(peak_periods) | set(non_peak_periods)
        if all_periods != covered_periods:
            for uncovered in (all_periods - covered_periods):
                non_peak_periods.append(uncovered)
        
        # 计算价格选项（论文Algorithm 2的q₁, q₂, q₃, q₄）
        price_set_sorted = sorted(self.price_set)
        delta = 1/4  # 论文Algorithm 2的δ
        
        q1_idx = int(delta * len(price_set_sorted))
        q2_idx = q1_idx + 1
        q3_idx = int(3 * delta * len(price_set_sorted))
        q4_idx = q3_idx + 1
        
        # 非高峰时间段的价格选项
        non_peak_price_options = [
            price_set_sorted[min(q1_idx, len(price_set_sorted)-1)], 
            price_set_sorted[min(q2_idx, len(price_set_sorted)-1)]
        ]
        
        # 高峰时间段的价格选项
        peak_price_options = [
            price_set_sorted[min(q3_idx, len(price_set_sorted)-1)], 
            price_set_sorted[min(q4_idx, len(price_set_sorted)-1)]
        ]
        
        # 生成四种组合方案
        schemes = []
        for peak_price in peak_price_options:
            for non_peak_price in non_peak_price_options:
                scheme = {}
                
                for period in peak_periods:
                    scheme[period] = peak_price
                
                for period in non_peak_periods:
                    scheme[period] = non_peak_price
                
                schemes.append(scheme)
        
        return schemes


if __name__ == "__main__":
    print("开始运行HALNS算法（严格按照论文实现）...")
    
    # 设置随机种子确保结果可重现
    random.seed(42)
    np.random.seed(42)
    
    try:
        # 创建破坏修复算子实例
        operators = DestroyRepairOperators(debug_max_consumers=20, 
                                           driver_capacity=50, 
                                           num_drivers=10, 
                                           k_dist=0.1, 
                                           k_val=0.01)
        
        # 运行完整HALNS算法
        max_iterations = 200  # 论文设定的迭代数
        best_solution, best_profit = operators.run_halns(max_iterations=max_iterations)
        
        if best_solution is not None:
            print(f"\n✓ HALNS算法完成！最佳利润: {best_profit:.2f}")
            print("\n最终解验证:")
            for period, price in best_solution.items():
                orders = operators.evaluator.period_orders.get(period, 0)
                print(f"  {period}: {price}元 (订单量: {orders})")
        else:
            print("\n✗ HALNS算法执行失败")
        
        print("\n✓ 程序执行完成！")
        print("严格按照论文Section 5.1.3和Appendix I实现的HALNS算法")
        
    except Exception as e:
        print(f"程序执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()