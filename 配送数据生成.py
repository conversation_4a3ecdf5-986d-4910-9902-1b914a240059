import pandas as pd
import numpy as np
from math import radians, cos, sin, asin, sqrt

def haversine(lon1, lat1, lon2, lat2):
    """
    计算两点间的地理距离（单位：公里）
    """
    # 转换为弧度
    lon1, lat1, lon2, lat2 = map(radians, [float(lon1), float(lat1), float(lon2), float(lat2)])
    
    # haversine公式
    dlon = lon2 - lon1 
    dlat = lat2 - lat1 
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a)) 
    r = 6371 # 地球平均半径，单位为公里
    return c * r

def prepare_delivery_data():
    """
    准备包含预计送达时间的基础配送数据。
    """
    print("=== 开始准备基础配送数据 ===")

    # 1. 读取原始数据集
    print("1. 读取原始数据集 `all_waybill_info_meituan_0322.csv`...")
    try:
        df = pd.read_csv('all_waybill_info_meituan_0322.csv')
    except FileNotFoundError:
        print("错误: 原始数据文件 `all_waybill_info_meituan_0322.csv` 未找到。")
        return

    # 2. 筛选数据：特定日期和已抢单
    print("2. 筛选2022-10-17已抢单的订单...")
    df = df[df['dt'] == 20221017]
    df = df[df['is_courier_grabbed'] == 1]

    # 3. 转换时间戳
    print("3. 转换时间戳列...")
    time_cols = [col for col in df.columns if 'time' in col]
    for col in time_cols:
        if pd.api.types.is_numeric_dtype(df[col]):
            df[col] = pd.to_datetime(df[col], unit='s', errors='coerce').dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai').dt.strftime('%Y-%m-%d %H:%M:%S')

    # 4. 筛选特定时间范围
    print("4. 筛选11:00至15:00之间的订单...")
    df['platform_order_time'] = pd.to_datetime(df['platform_order_time'])
    start_time = pd.to_datetime('2022-10-17 11:00:00')
    end_time = pd.to_datetime('2022-10-17 15:00:00')
    df = df[(df['platform_order_time'] >= start_time) & (df['platform_order_time'] <= end_time)]

    # 5. 转换经纬度格式
    print("5. 转换经纬度格式...")
    coord_columns = ['sender_lng', 'sender_lat', 'recipient_lng', 'recipient_lat', 'grab_lng', 'grab_lat']
    for col in coord_columns:
        if col in df.columns:
            df[f'{col}_decimal'] = df[col].apply(lambda x: float(x) / 1e6 if pd.notnull(x) else None)

    # 6. 地理位置聚类
    print("6. 根据地理位置进行聚类...")
    locations = df[['sender_lng_decimal', 'sender_lat_decimal', 'recipient_lng_decimal', 'recipient_lat_decimal']].dropna()
    if not locations.empty:
        from sklearn.cluster import DBSCAN
        clustering = DBSCAN(eps=0.01, min_samples=5).fit(locations.values)
        df.loc[locations.index, 'location_cluster'] = clustering.labels_
        df_clustered = df[df['location_cluster'] != -1]
        df_cluster2 = df_clustered[df_clustered['location_cluster'] == 2].copy()
        print(f"   筛选出聚簇2，包含 {len(df_cluster2)} 条订单。")
    else:
        print("   警告: 没有有效的地理位置信息进行聚类。")
        df_cluster2 = df.copy() # 如果聚类失败，则使用所有数据

    if df_cluster2.empty:
        print("错误: 聚类后没有符合条件的订单，无法继续。")
        return

    # 7. 添加订单价格
    print("7. 为订单生成价格...")
    np.random.seed(42)
    mu, sigma = 3.5, 0.4
    price = np.random.lognormal(mu, sigma, size=len(df_cluster2))
    df_cluster2['price'] = np.round(price, 1)

    # 8. 计算配送距离和时间
    print("8. 计算配送距离和预计送达时间...")
    
    # 计算餐厅到用户的距离
    df_cluster2['delivery_distance'] = df_cluster2.apply(
        lambda row: haversine(
            row['sender_lng_decimal'], row['sender_lat_decimal'],
            row['recipient_lng_decimal'], row['recipient_lat_decimal']
        ) if pd.notnull(row['sender_lng_decimal']) and pd.notnull(row['recipient_lng_decimal']) else 0,
        axis=1
    )
    
    # 计算配送时间（分钟），假设骑手速度为20km/h
    rider_speed = 20
    df_cluster2['delivery_time_minutes'] = (df_cluster2['delivery_distance'] / rider_speed) * 60
    
    # 计算预计送达时间（配送时间*1.2，作为算法基准）
    df_cluster2['estimated_delivery_time'] = df_cluster2['delivery_time_minutes'] * 1.2
    
    # 计算preparation_time
    print("9. 计算备餐时间 `preparation_time`...")
    df_cluster2['estimate_meal_prepare_time'] = pd.to_datetime(df_cluster2['estimate_meal_prepare_time'])
    df_cluster2['grab_time'] = pd.to_datetime(df_cluster2['grab_time'])
    df_cluster2['preparation_time'] = (df_cluster2['estimate_meal_prepare_time'] - df_cluster2['grab_time']).dt.total_seconds() / 60

    # 10. 添加订单体积 `volume`
    print("10. 添加订单体积 `volume`...")
    df_cluster2['volume'] = df_cluster2['price'] / 5

    # 11. 保存结果
    output_filename = 'meituan_orders_with_delivery_time.csv'
    print(f"11. 保存结果到 `{output_filename}`...")
    df_cluster2.to_csv(output_filename, index=False)
    
    print("\n=== 数据准备完成 ===")
    print(f"最终数据集包含 {len(df_cluster2)} 条订单。")
    print("关键列 `estimated_delivery_time` 已添加。")
    print("关键列 `preparation_time` 已添加。")
    print("关键列 `volume` 已添加。")
    print(f"平均预计送达时间: {df_cluster2['estimated_delivery_time'].mean():.2f} 分钟")
    print(f"平均备餐时间: {df_cluster2['preparation_time'].mean():.2f} 分钟")
    print(f"平均订单体积: {df_cluster2['volume'].mean():.2f}")

# 执行主函数
if __name__ == "__main__":
    try:
        prepare_delivery_data()
    except Exception as e:
        print(f"执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()