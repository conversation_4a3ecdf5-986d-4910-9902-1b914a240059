import json
import pandas as pd
import numpy as np
from math import radians, cos, sin, asin, sqrt
import copy

class OGAHAlgorithm:
    """订单组分配启发式算法（OGAH）"""
    
    def __init__(self, stp_choices_path, oggm_results_path, ogsa_results_path,
                 orders_data_path='meituan_orders_with_delivery_time.csv',
                 alpha=0.2, beta=1, epsilon=1.6, delta_max=3, delta_bar=2, 
                 rider_speed_kmh=20, num_drivers=30):
        """
        初始化OGAH算法
        
        Args:
            stp_choices_path: 消费者STP选择结果文件路径
            oggm_results_path: OGGM算法结果文件路径
            ogsa_results_path: OGSA算法结果文件路径
            orders_data_path: 订单数据文件路径
            alpha: 旅行成本系数
            beta: 延迟成本系数
            epsilon: 订单组规模调节参数
            delta_max: 最大δ值
            delta_bar: 虚拟司机计算参数
            rider_speed_kmh: 骑手速度（公里/小时）
            num_drivers: 司机数量
        """
        self.orders_data_path = orders_data_path
        self.stp_choices_path = stp_choices_path
        self.ogsa_results_path = ogsa_results_path
        self.oggm_results_path = oggm_results_path
        
        # 算法参数
        self.alpha = alpha
        self.beta = beta
        self.epsilon = epsilon
        self.delta_max = delta_max
        self.delta_bar = delta_bar
        self.rider_speed_kmh = rider_speed_kmh
        self.num_drivers = num_drivers
        
        # 数据存储
        self.driver_positions = None
        self.order_positions = None
        self.ogsa_results = None
        self.oggm_results = None
        self.reconstructed_order_groups = None
        
        # 初始化数据
        self._load_all_data()
    
    def _load_all_data(self):
        """加载所有必要的数据"""
        
        # 加载基础数据
        self._load_driver_positions()
        self._load_order_positions()
        self._load_ogsa_results()
        self._process_loaded_ogsa_results()
        self._load_oggm_results()
        
        # 重构订单组数据
        self._reconstruct_order_groups()
        
    
    def _load_driver_positions(self):
        """加载司机初始位置数据"""
      
        df = pd.read_csv(self.orders_data_path)
        grab_positions = df[['grab_lng_decimal', 'grab_lat_decimal']].dropna()
        grab_positions = grab_positions[(grab_positions['grab_lng_decimal'] != 0) & 
                                       (grab_positions['grab_lat_decimal'] != 0)]
        
        if len(grab_positions) < self.num_drivers:
            self.driver_positions = grab_positions.values.tolist()
        else:
            selected_positions = grab_positions.sample(n=self.num_drivers, random_state=42)
            self.driver_positions = selected_positions.values.tolist()
        
    
    def _load_order_positions(self):
        """加载订单位置数据"""
        
        # 读取订单数据（包含位置信息）
        orders_df = pd.read_csv(self.orders_data_path)
        
        # 读取消费者STP选择结果  
        stp_df = pd.read_csv(self.stp_choices_path)
        stp_df = stp_df[stp_df['choice_type'] == 'purchase']
        
        
        # 建立consumer_id到订单位置的映射
        self.order_positions = {}
        
        for _, stp_row in stp_df.iterrows():
            consumer_id = stp_row['consumer_id']
            
            # consumer_id从1开始，对应orders_df的索引从0开始
            if consumer_id <= len(orders_df):
                order_row = orders_df.iloc[consumer_id - 1]
                
                # 检查必要字段是否存在
                if (pd.notna(order_row['sender_lng_decimal']) and pd.notna(order_row['sender_lat_decimal']) and
                    pd.notna(order_row['recipient_lng_decimal']) and pd.notna(order_row['recipient_lat_decimal'])):
                    
                    # 直接读取预期配送时长（已经是分钟为单位的时间段）
                    estimated_delivery_time_value = order_row.get('estimated_delivery_time', None)
                    
                    if pd.notna(estimated_delivery_time_value) and estimated_delivery_time_value > 0:
                        estimated_delivery_time = float(estimated_delivery_time_value)
                    else:
                        continue
                    
                    preparation_time = order_row.get('preparation_time', 0)  # 数据单位已经是分钟
                    volume = order_row.get('volume', 1)

                    self.order_positions[str(consumer_id)] = {
                                'restaurant_lng': order_row['sender_lng_decimal'],
                                'restaurant_lat': order_row['sender_lat_decimal'],
                                'customer_lng': order_row['recipient_lng_decimal'],
                                'customer_lat': order_row['recipient_lat_decimal'],
                                'chosen_time': stp_row['chosen_time'],
                                'chosen_price': stp_row['chosen_price'],
                                'order_value': order_row['price'],
                                'order_id': order_row['order_id'],
                                'estimated_delivery_time': estimated_delivery_time,
                                'preparation_time': preparation_time,
                                'volume': volume
                            } 
        
    
    def _load_ogsa_results(self):
        """加载OGSA算法的详细结果"""
        
        with open(self.ogsa_results_path, 'r', encoding='utf-8') as f:
            self.ogsa_results = json.load(f)
    
    def _process_loaded_ogsa_results(self):
        """处理从文件加载的OGSA结果，确保订单包含estimated_delivery_time"""
        if not hasattr(self, 'ogsa_results') or not self.ogsa_results or \
           not hasattr(self, 'order_positions') or not self.order_positions:
            return

        for stp, stp_data in self.ogsa_results.items():
            if 'best_result' in stp_data and 'selected_order_groups' in stp_data['best_result']:
                for group in stp_data['best_result']['selected_order_groups']:
                    if 'orders' in group and isinstance(group['orders'], list):
                        processed_orders = []
                        for order_in_group in group['orders']:
                            order_id_str = None
                            reconstructed_order = None

                            if isinstance(order_in_group, dict) and 'order_id' in order_in_group:
                                order_id_str = str(order_in_group['order_id'])
                                reconstructed_order = order_in_group
                            elif isinstance(order_in_group, (str, int)):
                                order_id_str = str(order_in_group)
                            else: # Unknown structure
                                processed_orders.append(order_in_group)
                                continue
                            
                            if order_id_str:
                                if order_id_str in self.order_positions:
                                    pos_info = self.order_positions[order_id_str]
                                    if reconstructed_order is None:
                                        reconstructed_order = {
                                            'order_id': order_id_str,
                                            'restaurant_lng': pos_info['restaurant_lng'],
                                            'restaurant_lat': pos_info['restaurant_lat'],
                                            'customer_lng': pos_info['customer_lng'],
                                            'customer_lat': pos_info['customer_lat'],
                                            'chosen_time': pos_info['chosen_time'],
                                            'chosen_price': pos_info['chosen_price'],
                                            'order_value': pos_info['order_value'],
                                            'original_order_id': pos_info['order_id'],
                                            'estimated_delivery_time': pos_info['estimated_delivery_time'],
                                            'preparation_time': pos_info['preparation_time'],
                                            'volume': pos_info['volume']
                                        }
                                    else:
                                      if 'estimated_delivery_time' not in reconstructed_order:
                                          reconstructed_order['estimated_delivery_time'] = pos_info['estimated_delivery_time']
                                      if 'preparation_time' not in reconstructed_order:
                                           reconstructed_order['preparation_time'] = pos_info['preparation_time']
                                      if 'volume' not in reconstructed_order:
                                           reconstructed_order['volume'] = pos_info['volume']
                                else:
                                    if reconstructed_order is None: 
                                         processed_orders.append(order_in_group)
                                    else: 
                                         processed_orders.append(reconstructed_order)
                                    continue
                            
                            if reconstructed_order is not None:
                                processed_orders.append(reconstructed_order)
                            elif order_in_group is not None:
                                processed_orders.append(order_in_group)

                        group['orders'] = processed_orders
    
    def _load_oggm_results(self):
        """加载OGGM算法结果"""
        
        with open(self.oggm_results_path, 'r', encoding='utf-8') as f:
            self.oggm_results = json.load(f)
        
    
    def _reconstruct_order_groups(self):
        """重构订单组，补充位置信息"""
        
        reconstructed_results = {}
        
        for stp, stp_data in self.oggm_results.items():
            reconstructed_groups = []
            
            for group_detail in stp_data['groups_detail']:
                order_ids = group_detail['orders']  # 这些是consumer_id
                
                # 重构订单组，添加位置信息
                reconstructed_orders = []
                valid_orders = 0
                
                for consumer_id in order_ids:
                    if consumer_id in self.order_positions:
                        pos_info = self.order_positions[consumer_id]
                        
                        reconstructed_orders.append({
                            'order_id': consumer_id,  # 使用consumer_id作为标识
                            'restaurant_lng': pos_info['restaurant_lng'],
                            'restaurant_lat': pos_info['restaurant_lat'],
                            'customer_lng': pos_info['customer_lng'],
                            'customer_lat': pos_info['customer_lat'],
                            'chosen_time': pos_info['chosen_time'],
                            'chosen_price': pos_info['chosen_price'],
                            'order_value': pos_info['order_value'],
                            'original_order_id': pos_info['order_id'],
                            'estimated_delivery_time': pos_info['estimated_delivery_time'],
                            'preparation_time': pos_info['preparation_time'],
                            'volume': pos_info['volume']
                        })
                        valid_orders += 1
                
                if reconstructed_orders:  # 只保留有效的订单组
                    reconstructed_groups.append({
                        'group_id': group_detail['group_id'],
                        'orders': reconstructed_orders,
                        'size': len(reconstructed_orders),
                        'starting_order': group_detail['starting_order'],
                        'sequence': group_detail.get('sequence', [])  # 关键：从OGGM结果中提取序列
                    })
            
            reconstructed_results[stp] = {
                'total_groups': len(reconstructed_groups),
                'groups_detail': reconstructed_groups
            }
            
        
        self.reconstructed_order_groups = reconstructed_results
    
    @staticmethod
    def haversine(lon1, lat1, lon2, lat2):
        """计算两点间的地理距离（单位：公里）"""
        if pd.isna(lon1) or pd.isna(lat1) or pd.isna(lon2) or pd.isna(lat2):
            return 0
        
        lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
        dlon = lon2 - lon1 
        dlat = lat2 - lat1 
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a)) 
        r = 6371
        return c * r
    
    @staticmethod
    def time_to_minutes(time_str):
        """将时间字符串转换为从午夜开始的分钟数"""
        if isinstance(time_str, str):
            hour, minute = map(int, time_str.split(':'))
            return hour * 60 + minute
    
    def get_virtual_driver_info(self, order_group, delta):
        """
        获取订单组的虚拟司机信息
        与OGSA.py中的逻辑完全一致
        """
        if not order_group['orders']:
            return {'lng': 0, 'lat': 0, 'start_time': 0}
        
        # 获取第一个订单的餐厅位置作为参考点（与OGGM.py一致）
        first_order = order_group['orders'][0]
        ref_lng = first_order['restaurant_lng']
        ref_lat = first_order['restaurant_lat']
        
        # 计算到所有司机位置的距离
        distances = []
        for driver_lng, driver_lat in self.driver_positions:
            dist = self.haversine(driver_lng, driver_lat, ref_lng, ref_lat)
            distances.append((driver_lng, driver_lat, dist))
        
        # 按距离排序，取前delta个最近的司机
        distances.sort(key=lambda x: x[2])
        closest_drivers = distances[:delta]
        
        # 计算平均位置
        avg_lng = np.mean([d[0] for d in closest_drivers])
        avg_lat = np.mean([d[1] for d in closest_drivers])
        
        # 获取STP时间作为起始时间
        stp_time_minutes = self.time_to_minutes(first_order['chosen_time'])
        
        return {
            'lng': avg_lng,
            'lat': avg_lat,
            'start_time': stp_time_minutes
        }
    
    def simulate_delivery_sequence_for_cost(self, order_group, virtual_driver_lng, virtual_driver_lat):
        """
        模拟订单组的配送过程，计算总距离和延迟成本
        与OGSA.py中的配送策略完全一致：先依次取餐，再按距离最近原则送达
        """
        if not order_group:
            return 0, 0
        
        current_lng = virtual_driver_lng
        current_lat = virtual_driver_lat
        current_time = 0
        total_distance = 0
        
        # 第一阶段：依次取餐
        for order in order_group:
            distance_to_restaurant = self.haversine(current_lng, current_lat, 
                                             order['restaurant_lng'], order['restaurant_lat'])
            total_distance += distance_to_restaurant
            travel_time = (distance_to_restaurant / self.rider_speed_kmh) * 60

            current_time += travel_time
            # 餐厅从STP开始就立即备餐，骑手到达时需要等待的时间
            wait_time = max(0, order['preparation_time'] / 2 - current_time)
            current_time += wait_time
            
            current_lng = order['restaurant_lng']
            current_lat = order['restaurant_lat']
        
        # 第二阶段：按距离最近原则送达
        remaining_orders = order_group.copy()
        delivery_times = {}
        
        while remaining_orders:
            min_distance = float('inf')
            nearest_order = None
            
            for order in remaining_orders:
                distance = self.haversine(current_lng, current_lat, 
                                   order['customer_lng'], order['customer_lat'])
                if distance < min_distance:
                    min_distance = distance
                    nearest_order = order
            
            total_distance += min_distance
            travel_time = (min_distance / self.rider_speed_kmh) * 60
            current_time += travel_time
            delivery_times[nearest_order['order_id']] = current_time
            
            current_lng = nearest_order['customer_lng']
            current_lat = nearest_order['customer_lat']
            remaining_orders.remove(nearest_order)
        
        # 计算延迟成本
        penalty_cost = 0
        for order in order_group:
            # 获取该订单在合并配送中的实际送达时间
            actual_delivery_time = delivery_times.get(order['order_id'], 0)
            
            expected_delivery_time = order['estimated_delivery_time']
            
            # 延迟 = max(合并配送时间 - 预期配送时长, 0)
            delay = max(actual_delivery_time - expected_delivery_time, 0)
            penalty_cost += delay  # beta = 1
        
        return total_distance, penalty_cost
    
    def run_ogsa_for_specific_delta(self, stp, delta):
        """
        为特定的δ值运行OGSA算法
        复制OGSA.py中的核心选择逻辑，但只处理指定的δ值
        """
        
        if stp not in self.reconstructed_order_groups:
            return []
        
        stp_data = self.reconstructed_order_groups[stp]
        all_order_groups = stp_data['groups_detail']
        
        if not all_order_groups:
            return []
        
        # Step 1: 初始化
        selected_order_groups = []  # Fδ
        remaining_order_groups = all_order_groups.copy()  # B̄
        covered_orders = set()
        
        # Step 3 & 4: 为每个订单组计算虚拟司机信息和配送成本
        order_group_evaluations = {}
        
        for i, og in enumerate(remaining_order_groups):
            # Step 3: 获取虚拟司机信息，使用delta个最近司机
            virtual_driver_info = self.get_virtual_driver_info(og, delta)
            
            # Step 4: 计算配送成本（旅行成本 + 延迟惩罚成本）
            total_distance, penalty_cost = self.simulate_delivery_sequence_for_cost(
                og['orders'], 
                virtual_driver_info['lng'], 
                virtual_driver_info['lat']
            )
            
            travel_cost = total_distance * self.alpha
            total_cost = travel_cost + penalty_cost
            
            # 计算f值（根据公式24）
            og_size = len(og['orders'])
            f_value = total_cost / (og_size ** self.epsilon)
            
            order_group_evaluations[i] = {
                'order_group': og,
                'cost': total_cost,
                'travel_cost': travel_cost,
                'penalty_cost': penalty_cost,
                'size': og_size,
                'f_value': f_value,
                'virtual_driver_info': virtual_driver_info
            }
        
        # Step 5-7: 集合覆盖迭代
        while remaining_order_groups and order_group_evaluations:
            # Step 6: 找到f值最小的订单组
            best_og_idx = min(order_group_evaluations.keys(), 
                            key=lambda x: order_group_evaluations[x]['f_value'])
            
            best_evaluation = order_group_evaluations[best_og_idx]
            selected_og = best_evaluation['order_group']
            
            # 添加到已选择集合
            selected_order_groups.append(selected_og)
            
            # 记录覆盖的订单
            selected_order_ids = set(order['order_id'] for order in selected_og['orders'])
            covered_orders.update(selected_order_ids)
            
            # Step 7: 移除有重叠的订单组
            indices_to_remove = []
            
            for idx, evaluation in order_group_evaluations.items():
                og_order_ids = set(order['order_id'] for order in evaluation['order_group']['orders'])
                if og_order_ids & selected_order_ids:  # 有交集
                    indices_to_remove.append(idx)
            
            # 移除重叠的订单组
            for idx in indices_to_remove:
                del order_group_evaluations[idx]
            
            # 更新剩余订单组列表
            remaining_order_groups = [evaluation['order_group'] 
                                    for evaluation in order_group_evaluations.values()]
            
            # Step 5: 检查终止条件
            if not remaining_order_groups:
                break
        
        return selected_order_groups
    
    def simulate_order_group_delivery(self, order_group, start_lng, start_lat, start_time):
        """
        模拟订单组的配送过程，返回总距离、总时间和最终位置
        OGAH不重新生成序列，而是使用OGSA传递的序列信息
        """
        if not order_group['orders']:
            return 0, 0, start_lng, start_lat
        
        # 尝试使用OGSA传递的序列
        sequence = order_group.get('sequence')
        if sequence:
            # 返回所有5个值
            return self._simulate_with_sequence(order_group, start_lng, start_lat, start_time)
        
        # 如果序列不存在，这是数据流错误，返回一个表示失败的值
        return float('inf'), float('inf'), start_lng, start_lat, {}
    
    def _simulate_with_sequence(self, order_group, start_lng, start_lat, start_time):
        """使用OGSA传递的序列模拟配送过程, 返回详细信息包括每个订单的送达时间。"""
        sequence = order_group['sequence']
        orders_list = order_group['orders']
        
        order_map = {order['order_id']: order for order in orders_list}
        
        current_lng, current_lat = start_lng, start_lat
        current_time = start_time
        total_distance = 0
        delivery_times = {} # 新增：记录每个订单的送达时间
        
        for node in sequence:
            node_type, order_id = node.split('_')
            if order_id not in order_map:
                continue
                
            order = order_map[order_id]
            
            if node_type == 'r':  # pickup/restaurant
                target_lng, target_lat = order['restaurant_lng'], order['restaurant_lat']
                distance = self.haversine(current_lng, current_lat, target_lng, target_lat)
                travel_time = (distance / self.rider_speed_kmh) * 60
                
                arrival_at_restaurant = current_time + travel_time
                
                # 关键的等待逻辑
                food_ready_time = self.time_to_minutes(order['chosen_time']) + order.get('preparation_time', 0) / 2
                departure_time = max(arrival_at_restaurant, food_ready_time)
                
                current_time = departure_time
                total_distance += distance
                current_lng, current_lat = target_lng, target_lat
                
            elif node_type == 'c':  # delivery/customer
                target_lng, target_lat = order['customer_lng'], order['customer_lat']
                distance = self.haversine(current_lng, current_lat, target_lng, target_lat)
                travel_time = (distance / self.rider_speed_kmh) * 60
                
                current_time += travel_time
                total_distance += distance
                delivery_times[order_id] = current_time # 记录送达时间点
                
                current_lng, current_lat = target_lng, target_lat
        
        return total_distance, current_time - start_time, current_lng, current_lat, delivery_times
    
    def calculate_og_delivery_cost_for_driver(self, order_group, driver_state, stp_time_minutes):
        """
        计算特定司机执行特定订单组的成本（已修正延迟计算逻辑）。
        """
        # 司机实际开始时间 = max(司机可用时间, STP时间)
        actual_start_time = max(driver_state['available_time'], stp_time_minutes)
        
        # 1. 使用精确的模拟函数获取总距离、最终状态和每个订单的精确送达时间
        total_distance, delivery_duration, final_lng, final_lat, delivery_times = self._simulate_with_sequence(
            order_group, 
            driver_state['lng'], 
            driver_state['lat'],
            actual_start_time
        )
        
        # 2. 计算旅行成本
        travel_cost = total_distance * self.alpha
        
        # 3. 基于精确的送达时间计算延迟惩罚成本
        penalty_cost = 0
        for order in order_group['orders']:
            order_id = order['order_id']
            actual_delivery_time = delivery_times.get(order_id)
            
            if actual_delivery_time is not None:
                # 预期送达时间点 = STP时间点 + 预期配送时长
                expected_delivery_deadline = self.time_to_minutes(order['chosen_time']) + order['estimated_delivery_time']
                
                # 延迟 = 实际送达时间点 - 预期送达时间点
                delay = max(0, actual_delivery_time - expected_delivery_deadline)
                penalty_cost += delay * self.beta
        
        # 4. 计算总成本
        total_cost = travel_cost + penalty_cost
        
        return {
            'total_cost': total_cost,
            'travel_cost': travel_cost,
            'penalty_cost': penalty_cost,
            'delivery_duration': delivery_duration,
            'final_lng': final_lng,
            'final_lat': final_lat,
            'completion_time': actual_start_time + delivery_duration
        }
    
    def ogah_algorithm_for_stp(self, selected_order_groups, driver_states, stp, stp_time_minutes):
        """
        为单个STP运行OGAH算法，包含强制分配逻辑以确保所有订单组都被服务。
        """
        # 1. 初始化
        assignments = []
        total_travel_cost = 0.0
        total_penalty_cost = 0.0
        
        unassigned_ogs = [copy.deepcopy(og) for og in selected_order_groups]
        # 核心逻辑修正：根据论文，应在所有司机中进行选择，而不是仅在当前空闲的司机中
        remaining_driver_indices = list(driver_states.keys())

        # 2. 主分配循环 (Regret-k Heuristic)，此循环为每个可用司机分配一个最优的OG
        while remaining_driver_indices and unassigned_ogs:
            regret_values = []
            
            for i, og in enumerate(unassigned_ogs):
                costs_for_og = []
                # 在所有剩余的司机中为当前OG计算成本
                for driver_id in remaining_driver_indices:
                    costs_for_og.append((self.calculate_og_delivery_cost_for_driver(og, driver_states[driver_id], stp_time_minutes)['total_cost'], driver_id))
                
                if not costs_for_og: continue
                
                costs_for_og.sort() # 按成本升序排序
                
                # 计算悔值（与最优选择、次优选择等的成本差异总和）
                regret = sum(costs_for_og[j][0] - costs_for_og[0][0] for j in range(1, min(len(costs_for_og), 3)))
                best_cost, best_driver_id_for_og = costs_for_og[0]
                
                regret_values.append({
                    'idx': i, 
                    'og': og, 
                    'regret': regret, 
                    'best_cost': best_cost, 
                    'best_driver_id': best_driver_id_for_og
                })

            if not regret_values: break
            
            # 选择具有最大悔值的订单组进行优先分配
            best_og_to_assign_info = max(regret_values, key=lambda x: x['regret'])
            
            # 获取该订单组的最佳司机和成本信息
            best_driver_id = best_og_to_assign_info['best_driver_id']
            min_cost = best_og_to_assign_info['best_cost']
            
            # 重新计算完整的配送细节
            best_delivery_details = self.calculate_og_delivery_cost_for_driver(best_og_to_assign_info['og'], driver_states[best_driver_id], stp_time_minutes)
            
            if best_driver_id != -1:
                assignments.append({
                    'driver_id': best_driver_id, 'order_group': best_og_to_assign_info['og'],
                    'delivery_info': best_delivery_details, 'cost': min_cost, 'is_concatenated': False,
                    'travel_cost': best_delivery_details['travel_cost'], 'penalty_cost': best_delivery_details['penalty_cost']
                })
                total_travel_cost += best_delivery_details['travel_cost']
                total_penalty_cost += best_delivery_details['penalty_cost']
                
                # 更新被分配司机的状态，为下一个STP做准备
                # 注意：此更新是基于整个OGAH算法是按STP顺序执行的假设
                driver_states[best_driver_id].update({
                    'lng': best_delivery_details['final_lng'],
                    'lat': best_delivery_details['final_lat'],
                    'available_time': best_delivery_details['completion_time']
                })
                
                # 从待分配池中移除已分配的OG和司机
                unassigned_ogs.pop(best_og_to_assign_info['idx'])
                remaining_driver_indices.remove(best_driver_id)

        # 3. 强制分配剩余的订单组
        if unassigned_ogs:
            for og_to_assign in unassigned_ogs:
                min_incremental_cost = float('inf')
                best_driver_for_concat = -1
                best_concat_details = None

                for driver_id in range(self.num_drivers):
                    result = self.calculate_og_delivery_cost_for_driver(og_to_assign, driver_states[driver_id], stp_time_minutes)
                    if result['total_cost'] < min_incremental_cost:
                        min_incremental_cost = result['total_cost']
                        best_driver_for_concat = driver_id
                        best_concat_details = result

                if best_driver_for_concat != -1:
                    assignments.append({
                        'driver_id': best_driver_for_concat, 'order_group': og_to_assign,
                        'delivery_info': best_concat_details, 'cost': min_incremental_cost, 'is_concatenated': True,
                        'travel_cost': best_concat_details['travel_cost'], 'penalty_cost': best_concat_details['penalty_cost']
                    })
                    total_travel_cost += best_concat_details['travel_cost']
                    total_penalty_cost += best_concat_details['penalty_cost']
                    
                    driver_states[best_driver_for_concat].update({
                        'lng': best_concat_details['final_lng'],
                        'lat': best_concat_details['final_lat'],
                        'available_time': best_concat_details['completion_time']
                    })
        
        return {
            'stp': stp,
            'assignments': assignments,
            'total_cost': total_travel_cost + total_penalty_cost,
            'total_travel_cost': total_travel_cost,
            'total_penalty_cost': total_penalty_cost,
            'assigned_count': len([a for a in assignments if not a.get('is_concatenated')]),
            'concatenated_count': len([a for a in assignments if a.get('is_concatenated')])
        }
    
    def run_ogah_for_all_stps(self):
        """为所有STP运行OGAH算法，实现跨STP的司机状态跟踪"""
        
        # 初始化司机状态
        driver_states = {}
        for i, (lng, lat) in enumerate(self.driver_positions):
            driver_states[i] = {
                'lng': lng,
                'lat': lat,
                'available_time': 0  # 初始时所有司机都可用
            }
        
        # 按时间顺序处理所有STP
        all_ogah_results = {}
        sorted_stps = sorted(self.ogsa_results.keys())
        
        
        # 对每个STP运行OGAH
        for stp_idx, stp in enumerate(sorted_stps):
            
            # 获取该STP的OGSA结果
            stp_ogsa_result = self.ogsa_results.get(stp)
            if not stp_ogsa_result or not stp_ogsa_result.get('best_result'): continue

            selected_order_groups = stp_ogsa_result['best_result']['selected_order_groups']
            
            if not selected_order_groups:
                continue
            
            # 获取STP时间（分钟）
            stp_time_minutes = self.time_to_minutes(stp)
            
            # 显示当前司机状态
            available_drivers = sum(1 for d in driver_states.values() 
                                  if d['available_time'] <= stp_time_minutes)
            
            # 为每个δ值运行OGAH
            delta_results = {}
            for delta in range(1, self.delta_max + 1):
                
                # 为每个δ值重新运行OGSA选择逻辑
                if delta == stp_ogsa_result['best_delta']:
                    # 对于最优δ值，直接使用已有结果
                    delta_order_groups = selected_order_groups
                else:
                    # 对于非最优δ值，重新运行OGSA选择逻辑
                    delta_selected_groups = self.run_ogsa_for_specific_delta(stp, delta)
                    delta_order_groups = delta_selected_groups
                
                if not delta_order_groups:
                    continue
                
                # 复制司机状态（每个δ独立计算）
                delta_driver_states = copy.deepcopy(driver_states)
                
                # 运行OGAH
                ogah_result = self.ogah_algorithm_for_stp(
                    delta_order_groups,
                    delta_driver_states,
                    stp,
                    stp_time_minutes
                )
                
                delta_results[delta] = ogah_result
                
            
            # 选择最优δ
            if delta_results:
                best_delta = min(delta_results.keys(), 
                               key=lambda d: delta_results[d]['total_cost'])
                best_ogah_result = delta_results[best_delta]
                
                
                # 更新全局司机状态（使用最优结果）
                for assignment in best_ogah_result['assignments']:
                    driver_id = assignment['driver_id']
                    delivery_info = assignment['delivery_info']
                    driver_states[driver_id] = {
                        'lng': delivery_info['final_lng'],
                        'lat': delivery_info['final_lat'],
                        'available_time': delivery_info['completion_time']
                    }
                
                # 保存结果，包含所有δ值的信息
                all_ogah_results[stp] = {
                    'best_delta': best_delta,
                    'best_result': best_ogah_result,
                    'all_delta_results': delta_results
                }
                
                
        # 保存结果
        self.save_ogah_results(all_ogah_results)
        
        
        return all_ogah_results
    
    def save_ogah_results(self, all_ogah_results):
        """保存OGAH结果到JSON文件"""
        
        # 准备可序列化的结果
        serializable_results = {}
        
        for stp, result in all_ogah_results.items():
            best_result = result['best_result']
            
            serializable_stp_result = {
                'stp': stp,
                'best_delta': result['best_delta'],
                'total_cost': round(best_result['total_cost'], 2),
                'assigned_count': best_result['assigned_count'],
                'concatenated_count': best_result['concatenated_count'],
                'assignments': [],
                'all_delta_results': {}
            }
            
            # 保存最优结果的详细分配信息
            for assignment in best_result['assignments']:
                assignment_data = {
                    'order_group_idx': assignment['order_group_idx'],
                    'driver_id': assignment['driver_id'],
                    'cost': round(assignment['cost'], 2),
                    'travel_cost': round(assignment.get('travel_cost', 0), 2),
                    'penalty_cost': round(assignment.get('penalty_cost', 0), 2),
                    'is_concatenated': assignment.get('is_concatenated', False),
                    'delivery_info': {
                        'delivery_duration': round(assignment['delivery_info']['delivery_duration'], 2),
                        'completion_time': round(assignment['delivery_info']['completion_time'], 2),
                        'final_position': {
                            'lng': assignment['delivery_info']['final_lng'],
                            'lat': assignment['delivery_info']['final_lat']
                        }
                    }
                }
                
                # 添加订单组详情（如果存在）
                if 'order_group' in assignment:
                    assignment_data['order_group_details'] = {
                        'group_id': assignment['order_group']['group_id'],
                        'size': assignment['order_group']['size'],
                        'orders': [order['order_id'] for order in assignment['order_group']['orders']]
                    }
                
                serializable_stp_result['assignments'].append(assignment_data)
            
            # 保存所有delta值的结果统计
            for delta, delta_result in result['all_delta_results'].items():
                serializable_stp_result['all_delta_results'][str(delta)] = {
                    'total_cost': round(delta_result['total_cost'], 2),
                    'assigned_count': delta_result['assigned_count'],
                    'concatenated_count': delta_result['concatenated_count'],
                    'assignments_count': len(delta_result['assignments'])
                }
            
            serializable_results[stp] = serializable_stp_result
        
        # 保存详细结果
        with open('ogah_results.json', 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        
    
    def get_algorithm_data(self):
        """获取算法所需的基础数据，供其他算法使用"""
        return {
            'driver_positions': self.driver_positions,
            'order_positions': self.order_positions,
            'ogsa_results': self.ogsa_results,
            'oggm_results': self.oggm_results,
            'reconstructed_order_groups': self.reconstructed_order_groups,
            'algorithm_parameters': {
                'alpha': self.alpha,
                'beta': self.beta,
                'epsilon': self.epsilon,
                'delta_max': self.delta_max,
                'delta_bar': self.delta_bar,
                'rider_speed_kmh': self.rider_speed_kmh,
                'num_drivers': self.num_drivers
            }
        }
