import pandas as pd
import numpy as np
import gurobipy as gp
from gurobipy import GRB
import math
import time
from datetime import datetime, timedelta
import random
from itertools import product

class LargeScaleFoodDeliveryOptimizer:
    def __init__(self, debug_max_consumers=None):
        """初始化大规模配送优化器"""
        self.debug_max_consumers = debug_max_consumers
        # 基本参数设置 - 与论文和HALNS.py保持一致
        self.commission_rate = 0.18  # θ - 平台佣金率
        self.travel_cost_per_km = 0.2  # α - 旅行成本系数 (RMB/km)
        self.penalty_cost_per_min = 1.0  # β - 延迟惩罚系数 (RMB/min)
        self.rider_speed_kmh = 20  # 骑手速度 (km/h)
        self.rider_speed_per_min = self.rider_speed_kmh / 60  # km/min
        
        # 配送费选项 - 与HALNS.py保持一致
        self.delivery_fees = [2, 3, 4, 5, 6, 7, 8, 9]
        
        # HALNS算法参数（严格按照Table 4）
        self.eta = 40          # η: 段内迭代数
        self.sigma_1 = 30      # σ̄₁: 新最佳解评分
        self.sigma_2 = 9       # σ̄₂: 更好解评分
        self.sigma_3 = 3       # σ̄₃: 被接受恶化解评分
        self.r = 0.1           # r: 反应因子
        self.alpha_cooling = 0.975  # ᾱ: 冷却率
        self.w_hat = 0.05      # ŵ: 恶化百分比(5%)
        self.delta_hat = 7     # δ̂: 可接受邻域大小

        # 时间结构设置 - 11:00-15:00
        self.setup_time_structure()
        
        # 数据加载
        self.load_data()
        
        # 司机相关参数
        self.max_orders_per_driver = 10  # 每个司机最多承接订单数
        self.num_drivers = 2  # 与HALNS保持一致，固定为30个司机
        self.load_driver_positions() # 加载司机初始位置

        # 定义破坏算子
        self.destroy_operators = [
            ("random_removal", self.random_removal),
            ("maximum_deviation_removal", self.maximum_deviation_removal),
            ("random_pair_removal", self.random_pair_removal),
            ("max_min_removal", self.max_min_removal),
            ("differentiated_period_removal", self.differentiated_period_removal)
        ]
        
        # 定义修复算子
        self.repair_operators = [
            ("greedy_assignment_repair", self.greedy_assignment_repair),
            ("admissible_neighborhood_repair", self.admissible_neighborhood_repair)
        ]

        # 初始化权重
        self.destroy_weights = [1.0] * len(self.destroy_operators)
        self.repair_weights = [1.0] * len(self.repair_operators)
        
        # 当前段的统计信息
        self.destroy_scores = [0.0] * len(self.destroy_operators)
        self.repair_scores = [0.0] * len(self.repair_operators)
        self.destroy_uses = [0] * len(self.destroy_operators)
        self.repair_uses = [0] * len(self.repair_operators)

        # 温度相关
        self.temperature = None
        self.initial_temperature = None
        
        # 解记录
        self.best_solution = None
        self.best_profit = float('-inf')
        self.current_solution = None
        self.current_profit = float('-inf')
        
        print(f"✓ 初始化完成")
        print(f"  订单数量: {len(self.orders_df)}")
        print(f"  消费者数量: {len(self.preferences_df)}")
        print(f"  时间点数量: {len(self.time_points)}")
        print(f"  时间段数量: {len(self.time_periods)}")
        print(f"  司机数量: {self.num_drivers}")
        print(f"  配送费选项: {self.delivery_fees}")

    def setup_time_structure(self):
        """建立时间结构"""
        # 11:00-15:00，每30分钟一个时间段，共8个时间段
        time_ranges_periods = pd.date_range(start='2022-10-17 11:00:00', end='2022-10-17 15:00:00', freq='30min')
        self.time_periods = [f"{t.hour}:{t.minute:02d}-{(t + pd.Timedelta(minutes=30)).hour}:{(t + pd.Timedelta(minutes=30)).minute:02d}" 
                           for t in time_ranges_periods[:-1]]
        
        # 创建24个时间点：11:00-15:00，每10分钟一个
        base_time = datetime(2022, 10, 17, 11, 0, 0)
        self.time_points = []
        self.time_points_minutes = {}
        
        for i in range(24):
            time_dt = base_time + timedelta(minutes=10*i)
            time_str = time_dt.strftime('%H:%M')
            self.time_points.append(time_str)
            # 将时间转换为从11:00开始的分钟数
            self.time_points_minutes[time_str] = i * 10
            
        # 建立时间点到时间段的映射
        self.time_point_to_period = {}
        for i, time_point in enumerate(self.time_points):
            period_index = i // 3  # 10分钟一个点, 3个点组成一个30分钟的段
            if period_index < len(self.time_periods):
                self.time_point_to_period[time_point] = self.time_periods[period_index]
        
        print(f"✓ 时间结构设置完成，共{len(self.time_points)}个时间点, {len(self.time_periods)}个时间段")

    def load_data(self):
        """加载数据"""
        try:
            # 加载配送数据
            self.orders_df = pd.read_csv('meituan_orders_with_delivery_time.csv')
            print(f"✓ 配送数据加载完成: {len(self.orders_df)}条订单")
            
            # 加载偏好数据
            self.preferences_df = pd.read_csv('consumer_delivery_fee_preferences.csv')
            print(f"✓ 偏好数据加载完成: {len(self.preferences_df)}个消费者")

            # 如果设置了调试数量，则截取数据
            if self.debug_max_consumers is not None:
                print(f"  [调试模式] 使用 {self.debug_max_consumers} 条消费者数据进行调试...")
                self.preferences_df = self.preferences_df.head(self.debug_max_consumers)
            
            # 确保数据一致性
            min_records = min(len(self.orders_df), len(self.preferences_df))
            self.orders_df = self.orders_df.head(min_records)
            self.preferences_df = self.preferences_df.head(min_records)
            
            print(f"✓ 数据对齐完成，使用{min_records}条记录")

            # 计算每个时间段的订单量 (为初始解生成做准备)
            # 关键修正：period_orders的计算应该基于完整数据集，以确保与HALNS的行为一致
            full_orders_df = pd.read_csv('meituan_orders_with_delivery_time.csv')
            full_orders_df['platform_order_time'] = pd.to_datetime(full_orders_df['platform_order_time'])
            self.period_orders = {}
            time_ranges = pd.date_range(start='2022-10-17 11:00:00', end='2022-10-17 15:00:00', freq='30min')

            for i in range(len(self.time_periods)):
                period = self.time_periods[i]
                start_time = time_ranges[i]
                end_time = time_ranges[i+1]
                count = full_orders_df[(full_orders_df['platform_order_time'] >= start_time) & 
                                       (full_orders_df['platform_order_time'] < end_time)].shape[0]
                self.period_orders[period] = count
            print(f"✓ 时间段订单量计算完成: {self.period_orders}")
            
        except Exception as e:
            print(f"✗ 数据加载失败: {e}")
            raise

    def load_driver_positions(self):
        """加载司机初始位置 - 与HALNS(OGGM/OGSA/OGAH)保持一致"""
        print("正在加载司机初始位置...")
        try:
            df = pd.read_csv('meituan_orders_with_delivery_time.csv')
            grab_positions_df = df[['grab_lng_decimal', 'grab_lat_decimal']].dropna()
            grab_positions_df = grab_positions_df[(grab_positions_df['grab_lng_decimal'] != 0) & 
                                                  (grab_positions_df['grab_lat_decimal'] != 0)]
            
            if len(grab_positions_df) < self.num_drivers:
                sampled_positions = grab_positions_df.values.tolist()
            else:
                sampled_positions = grab_positions_df.sample(n=self.num_drivers, random_state=42).values.tolist()
            
            self.driver_initial_positions = {}
            for i in range(len(sampled_positions)):
                self.driver_initial_positions[i] = {
                    'lng': sampled_positions[i][0],
                    'lat': sampled_positions[i][1]
                }
            
            # 确保司机数量与实际加载位置数量一致
            self.num_drivers = len(self.driver_initial_positions)
            print(f"✓ 司机初始位置加载完成，共{self.num_drivers}个司机")

        except Exception as e:
            print(f"✗ 司机初始位置加载失败: {e}")
            # 创建备用位置，避免程序崩溃
            self.driver_initial_positions = {i: {'lng': 116.4, 'lat': 39.9} for i in range(self.num_drivers)}
            print("  已使用默认备用位置。")

    def haversine_distance(self, lat1, lon1, lat2, lon2):
        """计算两点间的地理距离（公里）"""
        R = 6371  # 地球半径（公里）
        
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        return R * c

    def parse_consumer_preferences(self, preference_str):
        """解析消费者偏好列表 - 与HALNS.py保持一致"""
        preferences = []
        items = preference_str.split(', ')
        
        for item in items:
            if item == "不买":
                preferences.append(("不买", None))
            else:
                parts = item.split('-')
                time = parts[0]
                price_str = parts[1].replace('元', '')
                price = int(price_str)
                preferences.append((time, price))
        
        return preferences

    def solve_ciip_for_consumer(self, consumer_preferences, available_tpcs):
        """为单个消费者求解CIIP - 与HALNS.py保持一致"""
        available_set = set(available_tpcs)
        
        # 按偏好顺序查找第一个可用的TPC
        for rank, preference_tpc in enumerate(consumer_preferences):
            if preference_tpc in available_set:
                return preference_tpc, rank + 1
        
        # 如果没有找到匹配的偏好，选择不购买
        return ("不买", None), len(consumer_preferences)

    def evaluate_pricing_scheme(self, period_price_map):
        """
        使用Gurobi模型评估一个定价方案的利润 (替代原OCDA评估器)
        """
        # 1. 将时间段定价转换为时间点定价
        time_point_prices = {}
        for time_point, period in self.time_point_to_period.items():
            if period in period_price_map:
                time_point_prices[time_point] = period_price_map[period]
            else:
                # 如果时间段被移除，则该时间段内的时间点无可用定价
                time_point_prices[time_point] = None

        # 2. 走Gurobi评估流程
        try:
            # 计算消费者选择
            consumer_choices = self.calculate_consumer_choices(time_point_prices)
            
            if not consumer_choices:
                return float('-inf'), 0, float('inf')
            
            # 计算收益
            total_revenue, _, _ = self.calculate_total_revenue(consumer_choices)
            
            # 构建空间网络
            participating_orders, orders_by_time = self.build_spatial_network(consumer_choices)
            
            # 求解配送优化
            total_cost, _, _ = self.solve_delivery_optimization(
                participating_orders, orders_by_time, self.driver_initial_positions
            )
            
            # 计算利润
            profit = total_revenue - total_cost
            
            return profit, total_revenue, total_cost

        except Exception as e:
            print(f"评估定价方案时出错: {e}")
            import traceback
            traceback.print_exc()
            return float('-inf'), 0, float('inf')

    def calculate_consumer_choices(self, time_price_scheme):
        """计算所有消费者的选择 - 实现CIIP"""
        # print("正在计算消费者选择（CIIP求解）...")
        
        # 构建可用TPC集合
        available_tpcs = []
        for time_point, price in time_price_scheme.items():
            if price is not None:
                available_tpcs.append((time_point, price))
        available_tpcs.append(("不买", None))
        
        consumer_choices = []
        purchase_count = 0
        
        for idx, row in self.preferences_df.iterrows():
            consumer_id = row['consumer_id']
            preference_str = row['preferences']
            
            # 解析消费者偏好
            consumer_preferences = self.parse_consumer_preferences(preference_str)
            
            # 求解CIIP
            optimal_choice, preference_rank = self.solve_ciip_for_consumer(
                consumer_preferences, available_tpcs)
            
            # 记录选择结果
            if optimal_choice[0] != "不买":
                consumer_choices.append({
                    'consumer_id': consumer_id,
                    'order_idx': idx,
                    'chosen_time': optimal_choice[0],
                    'chosen_price': optimal_choice[1],
                    'preference_rank': preference_rank
                })
                purchase_count += 1
        
        # print(f"✓ CIIP求解完成，{purchase_count}/{len(self.preferences_df)}个消费者选择购买")
        return consumer_choices

    def build_spatial_network(self, consumer_choices):
        """构建空间网络"""
        # print("正在构建空间网络...")
        
        # 提取参与订单的坐标信息
        participating_orders = []
        for choice in consumer_choices:
            order_idx = choice['order_idx']
            order_row = self.orders_df.iloc[order_idx]
            
            participating_orders.append({
                'consumer_id': choice['consumer_id'],
                'order_idx': order_idx,
                'chosen_time': choice['chosen_time'],
                'chosen_price': choice['chosen_price'],
                'restaurant_lat': order_row['sender_lat_decimal'],
                'restaurant_lng': order_row['sender_lng_decimal'],
                'customer_lat': order_row['recipient_lat_decimal'],
                'customer_lng': order_row['recipient_lng_decimal'],
                'order_value': order_row['price'],
                'estimated_delivery_time': order_row['estimated_delivery_time']
            })
        
        # 按时间点分组订单
        orders_by_time = {}
        for order in participating_orders:
            time_point = order['chosen_time']
            if time_point not in orders_by_time:
                orders_by_time[time_point] = []
            orders_by_time[time_point].append(order)
        
        # print(f"✓ 空间网络构建完成，共{len(participating_orders)}个订单")
        return participating_orders, orders_by_time

    def solve_delivery_optimization(self, participating_orders, orders_by_time, driver_positions):
        """求解配送优化问题 - 大规模Gurobi模型"""
        if not participating_orders:
            return 0, 0, 0
        
        # print("开始构建和求解大规模Gurobi模型...")
        
        # 创建Gurobi模型
        model = gp.Model("large_scale_delivery_optimization")
        model.setParam('OutputFlag', 0) # 在HALNS循环中关闭Gurobi日志输出
        model.setParam('TimeLimit', 180)  # 适当放宽时间限制
        model.setParam('MIPGap', 0.1)    # 10%的gap容忍度以加速
        model.setParam('Threads', 0)     # 使用所有可用线程
        
        # 按时间点顺序处理
        sorted_time_points = sorted(orders_by_time.keys(), 
                                  key=lambda x: self.time_points_minutes[x])
        
        total_travel_cost = 0
        total_penalty_cost = 0
        current_driver_positions = driver_positions.copy()
        current_driver_times = {driver_id: 0 for driver_id in range(self.num_drivers)}
        
        for time_point in sorted_time_points:
            time_orders = orders_by_time[time_point]
            if not time_orders:
                continue
            
            # print(f"  处理时间点 {time_point}: {len(time_orders)}个订单")
            
            # 为当前时间点构建子问题
            travel_cost, penalty_cost, new_positions, new_times = \
                self.solve_single_timepoint(model, time_point, time_orders, 
                                          current_driver_positions, current_driver_times)
            
            total_travel_cost += travel_cost
            total_penalty_cost += penalty_cost
            current_driver_positions = new_positions
            current_driver_times = new_times
        
        total_cost = total_travel_cost + total_penalty_cost
        # print(f"✓ 大规模优化完成")
        # print(f"  总旅行成本: {total_travel_cost:.2f}")
        # print(f"  总延迟成本: {total_penalty_cost:.2f}")
        # print(f"  总运营成本: {total_cost:.2f}")
        
        return total_cost, total_travel_cost, total_penalty_cost

    def solve_single_timepoint(self, model, time_point, time_orders, 
                             driver_positions, driver_times):
        """求解单个时间点的配送优化问题"""
        num_orders = len(time_orders)
        time_minutes = self.time_points_minutes[time_point]
        
        # 创建新的模型实例（避免变量冲突）
        sub_model = gp.Model(f"timepoint_{time_point}")
        sub_model.setParam('OutputFlag', 0)
        sub_model.setParam('TimeLimit', 60)
        sub_model.setParam('MIPGap', 0.1)
        
        # 构建节点集合
        # 取餐节点: 0 到 num_orders-1
        # 送餐节点: num_orders 到 2*num_orders-1
        # 司机起始节点: 2*num_orders 到 2*num_orders+num_drivers-1
        # 虚拟终点: 2*num_orders+num_drivers
        
        pickup_nodes = list(range(num_orders))
        delivery_nodes = list(range(num_orders, 2*num_orders))
        driver_start_nodes = list(range(2*num_orders, 2*num_orders + self.num_drivers))
        virtual_end_node = 2*num_orders + self.num_drivers
        
        all_nodes = pickup_nodes + delivery_nodes + driver_start_nodes + [virtual_end_node]
        
        # 构建节点坐标映射
        node_coords = {}
        
        # 取餐节点坐标（餐厅位置）
        for i, order in enumerate(time_orders):
            node_coords[i] = (order['restaurant_lat'], order['restaurant_lng'])
        
        # 送餐节点坐标（客户位置）
        for i, order in enumerate(time_orders):
            node_coords[num_orders + i] = (order['customer_lat'], order['customer_lng'])
        
        # 司机起始节点坐标
        for driver_id in range(self.num_drivers):
            node_idx = 2*num_orders + driver_id
            pos = driver_positions[driver_id]
            node_coords[node_idx] = (pos['lat'], pos['lng'])
        
        # 虚拟终点坐标（使用第一个司机位置）
        if self.num_drivers > 0:
            pos = driver_positions[0]
            node_coords[virtual_end_node] = (pos['lat'], pos['lng'])
        
        # 计算距离矩阵
        distances = {}
        for i in all_nodes:
            for j in all_nodes:
                if i != j:
                    coord_i = node_coords[i]
                    coord_j = node_coords[j]
                    distances[i, j] = self.haversine_distance(
                        coord_i[0], coord_i[1], coord_j[0], coord_j[1])
                else:
                    distances[i, j] = 0
        
        # 决策变量
        # x[i,j,k]: 司机k是否从节点i直接前往节点j
        x = {}
        for i in all_nodes:
            for j in all_nodes:
                if i != j:
                    for k in range(self.num_drivers):
                        x[i, j, k] = sub_model.addVar(vtype=GRB.BINARY, 
                                                    name=f"x_{i}_{j}_{k}")
        
        # a[i,k]: 司机k到达节点i的时间
        a = {}
        for i in all_nodes:
            for k in range(self.num_drivers):
                a[i, k] = sub_model.addVar(vtype=GRB.CONTINUOUS, lb=0, 
                                         name=f"a_{i}_{k}")
        
        # h[i,k]: 司机k离开节点i后累计的订单数
        h = {}
        for i in all_nodes:
            for k in range(self.num_drivers):
                h[i, k] = sub_model.addVar(vtype=GRB.CONTINUOUS, lb=0, 
                                         name=f"h_{i}_{k}")
        
        # delay[i]: 订单i的配送延迟
        delay = {}
        for i in range(num_orders):
            delay[i] = sub_model.addVar(vtype=GRB.CONTINUOUS, lb=0, 
                                      name=f"delay_{i}")
        
        # 目标函数：最小化旅行成本 + 延迟惩罚成本
        travel_expr = gp.quicksum(
            distances[i, j] * self.travel_cost_per_km * x[i, j, k]
            for i in all_nodes for j in all_nodes if i != j
            for k in range(self.num_drivers)
            if j != virtual_end_node  # 到虚拟终点的成本不计入
        )
        
        penalty_expr = gp.quicksum(
            delay[i] * self.penalty_cost_per_min
            for i in range(num_orders)
        )
        
        sub_model.setObjective(travel_expr + penalty_expr, GRB.MINIMIZE)
        
        # 约束条件
        M = 10000  # 大M常数
        
        # 约束1: 每个取餐节点必须被某个司机访问
        for i in pickup_nodes:
            sub_model.addConstr(
                gp.quicksum(x[j, i, k] for j in all_nodes if j != i 
                           for k in range(self.num_drivers)) == 1,
                name=f"visit_pickup_{i}"
            )
        
        # 约束2: 取餐和对应的送餐必须由同一司机处理（严格遵循论文公式8）
        for i in range(num_orders):
            pickup_node = i
            delivery_node = num_orders + i
            for k in range(self.num_drivers):
                sub_model.addConstr(
                    gp.quicksum(x[j, pickup_node, k] for j in all_nodes if j != pickup_node) -
                    gp.quicksum(x[j, delivery_node, k] for j in all_nodes if j != delivery_node) == 0,
                    name=f"pickup_delivery_must_be_paired_{i}_{k}"
                )
        
        # 约束3: 每个司机必须从其起始节点出发
        for k in range(self.num_drivers):
            start_node = 2*num_orders + k
            sub_model.addConstr(
                gp.quicksum(x[start_node, j, k] for j in all_nodes if j != start_node) <= 1,
                name=f"driver_start_{k}"
            )
        
        # 约束4: 最多一个司机到达虚拟终点（如果有订单分配给某司机）
        sub_model.addConstr(
            gp.quicksum(x[i, virtual_end_node, k] for i in all_nodes 
                       if i != virtual_end_node for k in range(self.num_drivers)) >= 1,
            name="at_least_one_to_end"
        )
        
        # 约束5: 流平衡约束
        for k in range(self.num_drivers):
            start_node = 2*num_orders + k
            for node in all_nodes:
                if node != start_node and node != virtual_end_node:
                    sub_model.addConstr(
                        gp.quicksum(x[i, node, k] for i in all_nodes if i != node) ==
                        gp.quicksum(x[node, j, k] for j in all_nodes if j != node),
                        name=f"flow_balance_{node}_{k}"
                    )
        
        # 约束6: 时间一致性约束
        for k in range(self.num_drivers):
            for i in all_nodes:
                for j in all_nodes:
                    if i != j:
                        travel_time = distances[i, j] / self.rider_speed_per_min
                        sub_model.addConstr(
                            a[j, k] >= a[i, k] + travel_time - M * (1 - x[i, j, k]),
                            name=f"time_consistency_{i}_{j}_{k}"
                        )
        
        # 约束7: 如果节点不被司机访问，到达时间为0
        for k in range(self.num_drivers):
            for i in pickup_nodes + delivery_nodes:
                sub_model.addConstr(
                    a[i, k] <= M * gp.quicksum(x[j, i, k] for j in all_nodes if j != i),
                    name=f"time_zero_if_not_visited_{i}_{k}"
                )
        
        # 约束8: 取餐必须在对应送餐之前
        for i in range(num_orders):
            pickup_node = i
            delivery_node = num_orders + i
            for k in range(self.num_drivers):
                 # 如果司机k服务这个订单，则取餐时间必须早于送餐时间
                sub_model.addConstr(
                    (a[pickup_node, k] <= a[delivery_node, k]),
                    name=f"pickup_before_delivery_{i}_{k}"
                )
        
        # 约束9: 累计订单数更新
        for k in range(self.num_drivers):
            for i in all_nodes:
                for j in all_nodes:
                    if i != j:
                        l_i = 1 if i in pickup_nodes else 0
                        sub_model.addConstr(
                            h[j, k] >= h[i, k] + l_i - M * (1 - x[i, j, k]),
                            name=f"order_count_update_{i}_{j}_{k}"
                        )
        
        # 约束10: 司机承接订单数限制
        for k in range(self.num_drivers):
            for i in pickup_nodes:
                sub_model.addConstr(
                    h[i, k] <= self.max_orders_per_driver,
                    name=f"max_orders_{i}_{k}"
                )
        
        # 约束11: 司机开始时间约束
        for k in range(self.num_drivers):
            start_node = 2*num_orders + k
            driver_ready_time = max(driver_times[k], time_minutes)
            sub_model.addConstr(
                a[start_node, k] >= driver_ready_time,
                name=f"driver_ready_time_{k}"
            )
        
        # 约束12: 司机起始时订单数为0
        for k in range(self.num_drivers):
            start_node = 2*num_orders + k
            sub_model.addConstr(
                h[start_node, k] == 0,
                name=f"initial_order_count_zero_{k}"
            )
        
        # 约束13: 延迟计算
        for i in range(num_orders):
            order = time_orders[i]
            delivery_node = num_orders + i
            target_delivery_time = time_minutes + order['estimated_delivery_time']
            
            for k in range(self.num_drivers):
                # 关键修改：将延迟与特定的司机k绑定
                # 只有当司机k访问了送餐节点i时，才计算其延迟
                # M * (1 - ...) 部分确保了当司机k不服务此订单时，该约束失效
                sub_model.addConstr(
                    delay[i] >= a[delivery_node, k] - target_delivery_time - M * (1 - gp.quicksum(x[j, delivery_node, k] for j in all_nodes if j != delivery_node)),
                    name=f"delay_def_{i}_{k}"
                )
        
        # 求解模型
        sub_model.optimize()
        
        if sub_model.status == GRB.OPTIMAL:
            # 计算实际成本
            actual_travel_cost = 0
            actual_penalty_cost = 0
            
            for i in all_nodes:
                for j in all_nodes:
                    if i != j and j != virtual_end_node:
                        for k in range(self.num_drivers):
                            if x[i, j, k].x > 0.5:
                                actual_travel_cost += distances[i, j] * self.travel_cost_per_km
            
            for i in range(num_orders):
                actual_penalty_cost += delay[i].x * self.penalty_cost_per_min
            
            # 更新司机位置和时间
            new_positions = driver_positions.copy()
            new_times = driver_times.copy()
            
            for k in range(self.num_drivers):
                # 找到司机的最后一个真实节点
                last_real_node = None
                last_time = driver_times[k]
                
                for i in all_nodes:
                    if i != virtual_end_node and x[i, virtual_end_node, k].x > 0.5:
                        last_real_node = i
                        last_time = a[i, k].x
                        break
                
                if last_real_node is not None:
                    coord = node_coords[last_real_node]
                    new_positions[k] = {'lat': coord[0], 'lng': coord[1]}
                    new_times[k] = last_time
            
            return actual_travel_cost, actual_penalty_cost, new_positions, new_times
        
        else:
            print(f"    警告: 时间点{time_point}的优化未找到最优解")
            return 0, 0, driver_positions, driver_times

    def calculate_total_revenue(self, consumer_choices):
        """计算总收益"""
        total_commission = 0
        total_delivery_fees = 0
        
        for choice in consumer_choices:
            order_idx = choice['order_idx']
            order_row = self.orders_df.iloc[order_idx]
            order_value = order_row['price']
            delivery_fee = choice['chosen_price']
            
            commission = order_value * self.commission_rate
            total_commission += commission
            total_delivery_fees += delivery_fee
        
        total_revenue = total_commission + total_delivery_fees
        
        return total_revenue, total_commission, total_delivery_fees

    # ===== 初始解生成方法（移植自HALNS）=====
    def generate_initial_pricing_scheme(self):
        """生成初始定价方案 - 严格按照论文Algorithm 2"""
        print("生成初始定价方案（论文Algorithm 2）...")
        
        # 生成5种候选方案并选择最佳的
        candidate_schemes = []
        
        # 方案1：峰值定价（论文Algorithm 2的主要思路）
        candidate_schemes.append(("峰值定价", self.generate_peak_pricing_scheme()))
        
        # 方案2-5：基于高峰/非高峰时段的四种组合
        peak_non_peak_schemes = self.generate_peak_non_peak_schemes()
        for i, scheme in enumerate(peak_non_peak_schemes):
            candidate_schemes.append((f"高峰非高峰组合{i+1}", scheme))
        
        # 评估所有候选方案
        best_scheme = None
        best_profit = float('-inf')
        best_name = ""

        print("正在评估候选初始方案...")
        for name, scheme in candidate_schemes:
            try:
                profit, revenue, cost = self.evaluate_pricing_scheme(scheme)
                print(f"  方案 '{name}': 利润={profit:.2f}, 收益={revenue:.2f}, 成本={cost:.2f}")
                
                if profit > best_profit:
                    best_profit = profit
                    best_scheme = scheme.copy()
                    best_name = name
                    
            except Exception as e:
                print(f"  方案 '{name}': 评估失败 - {e}")
                continue

        if best_scheme is not None:
            print(f"✓ 选择最佳初始方案: '{best_name}' (利润: {best_profit:.2f})")
        else:
            print("✗ 所有候选初始方案评估失败，使用默认峰值定价方案")
            return self.generate_peak_pricing_scheme()

        return best_scheme

    def generate_peak_pricing_scheme(self):
        """生成峰值定价方案 - 按照订单量多少分配高低价"""
        sorted_periods = sorted(self.period_orders.items(), key=lambda x: x[1], reverse=True)
        
        initial_pricing = {}
        price_set_desc = sorted(self.delivery_fees, reverse=True)
        
        for i, (period, _) in enumerate(sorted_periods):
            if i < len(price_set_desc):
                initial_pricing[period] = price_set_desc[i]
            else:
                initial_pricing[period] = price_set_desc[-1]
        
        return initial_pricing

    def generate_peak_non_peak_schemes(self):
        """生成基于高峰/非高峰时段的四种定价方案 - 按照论文Algorithm 2扩展"""
        # 计算平均订单数量
        avg_orders = np.mean(list(self.period_orders.values()))
        
        # 划分高峰和非高峰时间段
        peak_periods = [period for period, count in self.period_orders.items() if count >= avg_orders]
        non_peak_periods = [period for period, count in self.period_orders.items() if count < avg_orders]
        
        if not peak_periods: peak_periods = non_peak_periods
        if not non_peak_periods: non_peak_periods = peak_periods

        # 计算价格选项
        price_set_sorted = sorted(self.delivery_fees)
        delta = 1/4
        
        q1_idx = int(delta * len(price_set_sorted))
        q2_idx = q1_idx + 1
        q3_idx = int(3 * delta * len(price_set_sorted))
        q4_idx = q3_idx + 1
        
        non_peak_price_options = [
            price_set_sorted[min(q1_idx, len(price_set_sorted)-1)], 
            price_set_sorted[min(q2_idx, len(price_set_sorted)-1)]
        ]
        
        peak_price_options = [
            price_set_sorted[min(q3_idx, len(price_set_sorted)-1)], 
            price_set_sorted[min(q4_idx, len(price_set_sorted)-1)]
        ]
        
        # 生成四种组合方案
        schemes = []
        for peak_price in peak_price_options:
            for non_peak_price in non_peak_price_options:
                scheme = {}
                for period in peak_periods:
                    scheme[period] = peak_price
                for period in non_peak_periods:
                    scheme[period] = non_peak_price
                schemes.append(scheme)
        
        return schemes

    # ===== 破坏算子 (移植自HALNS) =====
    def random_removal(self, period_price_map, period_orders):
        result = period_price_map.copy()
        if not result: return result, []
        selected_period = random.choice(list(result.keys()))
        del result[selected_period]
        return result, [selected_period]

    def maximum_deviation_removal(self, period_price_map, period_orders):
        result = period_price_map.copy()
        avg_orders = np.mean(list(period_orders.values()))
        max_deviation = -1
        max_dev_period = None
        
        for period, orders in period_orders.items():
            if period in result:
                deviation = abs(orders - avg_orders)
                if deviation > max_deviation:
                    max_deviation = deviation
                    max_dev_period = period
        
        if max_dev_period:
            del result[max_dev_period]
            return result, [max_dev_period]
        return self.random_removal(period_price_map, period_orders)

    def random_pair_removal(self, period_price_map, period_orders):
        result = period_price_map.copy()
        periods = list(result.keys())
        if len(periods) < 2:
            return self.random_removal(period_price_map, period_orders)
        
        selected_pair = random.sample(periods, 2)
        for period in selected_pair:
            del result[period]
        
        return result, selected_pair

    def max_min_removal(self, period_price_map, period_orders):
        result = period_price_map.copy()
        
        valid_periods = {p: o for p, o in period_orders.items() if p in result}
        if len(valid_periods) < 2:
            return self.random_pair_removal(period_price_map, period_orders)
            
        sorted_periods = sorted(valid_periods.items(), key=lambda x: x[1])
        
        min_period = sorted_periods[0][0]
        max_period = sorted_periods[-1][0]
        
        del result[min_period]
        if min_period != max_period and max_period in result:
             del result[max_period]
        
        return result, [min_period, max_period]

    def differentiated_period_removal(self, period_price_map, period_orders):
        result = period_price_map.copy()
        
        avg_orders = np.mean(list(period_orders.values()))
        
        peak_periods = [p for p, c in period_orders.items() if c >= avg_orders and p in result]
        non_peak_periods = [p for p, c in period_orders.items() if c < avg_orders and p in result]
        
        if not peak_periods or not non_peak_periods:
            return self.random_pair_removal(period_price_map, period_orders)
        
        peak_period = random.choice(peak_periods)
        non_peak_period = random.choice(non_peak_periods)
        
        if peak_period in result: del result[peak_period]
        if non_peak_period in result: del result[non_peak_period]
        
        return result, [peak_period, non_peak_period]

    # ===== 修复算子 (移植自HALNS) =====
    def calculate_optional_price_set(self, target_period, current_pricing, period_orders):
        optional_prices = set()
        periods = self.time_periods
        target_idx = periods.index(target_period)
        
        prev_price = next((current_pricing[p] for p in reversed(periods[:target_idx]) if p in current_pricing), None)
        next_price = next((current_pricing[p] for p in periods[target_idx+1:] if p in current_pricing), None)
        
        if prev_price is not None and next_price is not None:
            min_p, max_p = min(prev_price, next_price), max(prev_price, next_price)
            optional_prices.update(p for p in self.delivery_fees if min_p <= p <= max_p)
        
        if not optional_prices:
            optional_prices = set(self.delivery_fees)
        
        return sorted(list(optional_prices))
    
    def greedy_assignment_repair(self, destroyed_solution, removed_periods, period_orders, max_candidates=5):
        candidate_solutions = []
        
        if len(removed_periods) == 1:
            period = removed_periods[0]
            optional_prices = self.calculate_optional_price_set(period, destroyed_solution, period_orders)
            
            price_profits = []
            for price in optional_prices:
                test_solution = destroyed_solution.copy()
                test_solution[period] = price
                profit, _, _ = self.evaluate_pricing_scheme(test_solution)
                if profit > float('-inf'):
                    price_profits.append((test_solution, profit))
            
            price_profits.sort(key=lambda x: x[1], reverse=True)
            candidate_solutions = price_profits[:max_candidates]
            
        else: # Multiple periods removed
            period_optional_prices = {p: self.calculate_optional_price_set(p, destroyed_solution, period_orders)[:3] for p in removed_periods}
            
            combinations = list(product(*[period_optional_prices[p] for p in removed_periods]))
            if len(combinations) > max_candidates * 2:
                combinations = random.sample(combinations, max_candidates * 2)

            combination_profits = []
            for combo in combinations:
                test_solution = destroyed_solution.copy()
                for i, period in enumerate(removed_periods):
                    test_solution[period] = combo[i]
                profit, _, _ = self.evaluate_pricing_scheme(test_solution)
                if profit > float('-inf'):
                    combination_profits.append((test_solution, profit))
            
            combination_profits.sort(key=lambda x: x[1], reverse=True)
            candidate_solutions = combination_profits[:max_candidates]

        if not candidate_solutions:
            backup_solution = destroyed_solution.copy()
            median_price = self.delivery_fees[len(self.delivery_fees) // 2]
            for period in removed_periods:
                backup_solution[period] = median_price
            
            backup_profit, _, _ = self.evaluate_pricing_scheme(backup_solution)
            if backup_profit > float('-inf'):
                candidate_solutions = [(backup_solution, backup_profit)]
        
        return candidate_solutions

    def admissible_neighborhood_repair(self, destroyed_solution, removed_periods, period_orders, max_candidates=5):
        candidate_solutions = []
        
        if len(removed_periods) == 1:
            period = removed_periods[0]
            optional_prices = self.calculate_optional_price_set(period, destroyed_solution, period_orders)
            
            admissible_prices = optional_prices if len(optional_prices) <= self.delta_hat else random.sample(optional_prices, self.delta_hat)
            
            price_profits = []
            for price in admissible_prices:
                test_solution = destroyed_solution.copy()
                test_solution[period] = price
                profit, _, _ = self.evaluate_pricing_scheme(test_solution)
                if profit > float('-inf'):
                    price_profits.append((test_solution, profit))
            
            price_profits.sort(key=lambda x: x[1], reverse=True)
            candidate_solutions = price_profits[:max_candidates]
        else:
            return self.greedy_assignment_repair(destroyed_solution, removed_periods, period_orders, max_candidates)

        if not candidate_solutions:
            return self.greedy_assignment_repair(destroyed_solution, removed_periods, period_orders, max_candidates)
        
        return candidate_solutions

    # ===== HALNS核心方法 (移植自HALNS) =====
    def roulette_wheel_selection(self, weights):
        total_weight = sum(weights)
        if total_weight == 0:
            return random.randint(0, len(weights) - 1)
        
        r = random.uniform(0, total_weight)
        cumulative = 0
        for i, weight in enumerate(weights):
            cumulative += weight
            if r <= cumulative:
                return i
        return len(weights) - 1

    def initialize_temperature(self, initial_profit):
        if initial_profit == float('-inf') or initial_profit == 0:
            self.initial_temperature = 1000
        else:
            delta = abs(initial_profit * self.w_hat)
            self.initial_temperature = delta / abs(math.log(0.4)) if delta > 0 else 1000
        
        self.temperature = self.initial_temperature

    def accept_solution(self, new_profit, current_profit):
        if new_profit >= current_profit:
            return True
        
        delta = current_profit - new_profit
        probability = math.exp(-delta / self.temperature)
        return random.random() < probability

    def update_scores(self, destroy_idx, repair_idx, score_type):
        score_values = {'best': self.sigma_1, 'better': self.sigma_2, 'accepted': self.sigma_3}
        score = score_values.get(score_type, 0)
        self.destroy_scores[destroy_idx] += score
        self.repair_scores[repair_idx] += score

    def update_weights(self):
        for i in range(len(self.destroy_operators)):
            if self.destroy_uses[i] > 0:
                self.destroy_weights[i] = (1 - self.r) * self.destroy_weights[i] + self.r * (self.destroy_scores[i] / self.destroy_uses[i])
        
        for i in range(len(self.repair_operators)):
            if self.repair_uses[i] > 0:
                self.repair_weights[i] = (1 - self.r) * self.repair_weights[i] + self.r * (self.repair_scores[i] / self.repair_uses[i])

    def reset_segment_stats(self):
        self.destroy_scores = [0.0] * len(self.destroy_operators)
        self.repair_scores = [0.0] * len(self.repair_operators)
        self.destroy_uses = [0] * len(self.destroy_operators)
        self.repair_uses = [0] * len(self.repair_operators)

    def run_halns(self, max_iterations=200):
        """运行HALNS算法主循环"""
        start_time = time.time()
        
        # 1. 生成初始解
        initial_solution = self.generate_initial_pricing_scheme()
        initial_profit, initial_revenue, initial_cost = self.evaluate_pricing_scheme(initial_solution)
        
        if initial_profit == float('-inf'):
            print("✗ 初始解评估失败，无法启动HALNS。")
            return None

        # 2. 初始化
        self.current_solution = initial_solution.copy()
        self.current_profit = initial_profit
        self.best_solution = initial_solution.copy()
        self.best_profit = initial_profit
        
        # 3. 初始化温度
        self.initialize_temperature(initial_profit)
        
        period_orders = self.period_orders
        
        # 4. 主循环
        iteration = 0
        best_found_iteration = 0
        
        print(f"\n{'='*60}")
        print("开始运行HALNS算法...")
        print(f"初始最佳利润: {self.best_profit:.2f}")
        print(f"{'='*60}")

        while iteration < max_iterations:
            iteration += 1
            
            if (iteration - 1) % self.eta == 0 and iteration > 1:
                self.update_weights()
                self.reset_segment_stats()
            
            # 选择算子
            destroy_idx = self.roulette_wheel_selection(self.destroy_weights)
            repair_idx = self.roulette_wheel_selection(self.repair_weights)
            destroy_op = self.destroy_operators[destroy_idx][1]
            repair_op = self.repair_operators[repair_idx][1]
            
            self.destroy_uses[destroy_idx] += 1
            self.repair_uses[repair_idx] += 1
            
            try:
                destroyed_solution, removed_periods = destroy_op(self.current_solution, period_orders)
                if not removed_periods: continue

                candidate_solutions = repair_op(destroyed_solution, removed_periods, period_orders)
                if not candidate_solutions: continue
                
                best_candidate_solution, best_candidate_profit = max(candidate_solutions, key=lambda x: x[1])
                
                # 接受准则和评分更新
                score_type = None
                if best_candidate_profit > self.best_profit:
                    self.best_solution, self.best_profit = best_candidate_solution.copy(), best_candidate_profit
                    self.current_solution, self.current_profit = best_candidate_solution.copy(), best_candidate_profit
                    score_type, best_found_iteration = 'best', iteration
                elif best_candidate_profit > self.current_profit:
                    self.current_solution, self.current_profit = best_candidate_solution.copy(), best_candidate_profit
                    score_type = 'better'
                elif self.accept_solution(best_candidate_profit, self.current_profit):
                    self.current_solution, self.current_profit = best_candidate_solution.copy(), best_candidate_profit
                    score_type = 'accepted'
                
                if score_type:
                    self.update_scores(destroy_idx, repair_idx, score_type)
                    
            except Exception as e:
                print(f"迭代 {iteration} 失败: {e}")
                continue
            
            # 更新温度
            self.temperature *= self.alpha_cooling
            
            if iteration % 20 == 0:
                print(f"迭代 {iteration}/{max_iterations}: 当前利润={self.current_profit:.2f}, "
                      f"最佳利润={self.best_profit:.2f}, 温度={self.temperature:.2f}")

        total_time = time.time() - start_time
        print(f"\n{'='*60}")
        print("HALNS算法完成！")
        print(f"总运行时间: {total_time:.1f}秒")
        print(f"找到最佳解的迭代: {best_found_iteration}")
        print(f"{'='*60}")

        # 最终详细评估最佳解
        self.print_final_results(self.best_solution)

    def print_final_results(self, best_pricing_scheme):
        """打印最终最优解的详细结果"""
        print("\n" + "="*80)
        print("最优定价方案详细分析")
        print("="*80)

        if best_pricing_scheme is None:
            print("未找到有效方案。")
            return

        try:
            # 重新评估以获取详细的成本和收益分类
            profit, total_revenue, total_cost = self.evaluate_pricing_scheme(best_pricing_scheme)
            
            # 需要再次运行以获取明细
            time_point_prices = {tp: best_pricing_scheme.get(self.time_point_to_period[tp]) for tp in self.time_points}
            consumer_choices = self.calculate_consumer_choices(time_point_prices)
            if not consumer_choices:
                print("最优方案在最终评估中无消费者购买。")
                return

            _, total_commission, total_delivery_fees = self.calculate_total_revenue(consumer_choices)
            participating_orders, orders_by_time = self.build_spatial_network(consumer_choices)
            _, travel_cost, penalty_cost = self.solve_delivery_optimization(participating_orders, orders_by_time, self.driver_initial_positions)

            print(f"购买人数: {len(consumer_choices)} / {len(self.preferences_df)}")
            print(f"")
            print(f"收益构成:")
            print(f"  - 平台佣金: {total_commission:.2f}")
            print(f"  - 配送费收入: {total_delivery_fees:.2f}")
            print(f"  - 总收益: {total_revenue:.2f}")
            print(f"")
            print(f"成本构成:")
            print(f"  - 旅行成本: {travel_cost:.2f}")
            print(f"  - 延迟惩罚: {penalty_cost:.2f}")
            print(f"  - 总成本: {total_cost:.2f}")
            print(f"")
            print(f"最终利润: {profit:.2f}")
            
            if total_revenue > 0:
                profit_margin = (profit / total_revenue) * 100
                print(f"利润率: {profit_margin:.2f}%")
            
            print(f"\n最优定价方案详情:")
            for period in self.time_periods:
                 price = best_pricing_scheme.get(period, 'N/A')
                 print(f"  {period}: {price}元")

        except Exception as e:
            print(f"打印最终结果时出错: {e}")


def main():
    """主函数"""
    print("="*80)
    print("大规模外卖配送定价与调度集成优化系统")
    print("基于HALNS框架与Gurobi精确求解器")
    print("="*80)
    
    try:
        # 创建优化器, 可设置debug_max_consumers进行快速调试
        # 例如: optimizer = LargeScaleFoodDeliveryOptimizer(debug_max_consumers=50)
        optimizer = LargeScaleFoodDeliveryOptimizer(debug_max_consumers=5)
        
        # 运行HALNS优化
        optimizer.run_halns(max_iterations=100) # 论文中设置为200次迭代, 为测试方便减少
        
        print(f"\n{'='*80}")
        print("大规模优化完成！")
        print("本实现结合了HALNS元启发式框架进行定价方案搜索")
        print("并使用Gurobi对每个候选方案进行精确的配送成本求解")
        print("实现了定价与调度问题的深度集成优化")
        print(f"{'='*80}")
        
    except Exception as e:
        print(f"程序执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 确保结果可复现
    random.seed(42)
    np.random.seed(42)
    
    main()