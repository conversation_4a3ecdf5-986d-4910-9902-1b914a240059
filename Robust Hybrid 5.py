import math
import copy
from typing import List, Dict, Tuple, Any, Set
import numpy as np
import gurobipy as gp
from gurobipy import GRB

# --- (新增) 客户选择和动态定价相关函数 ---
def _calculate_delivery_fee(order_id, base_price, potential_orders_data, delivery_distances, k_dist, k_val):
    """根据订单距离和价值计算动态配送费"""
    distance = delivery_distances[order_id]
    value = potential_orders_data[order_id]['value']
    fee = base_price + k_dist * distance + k_val * value
    return fee

def _get_customer_choice(customer_id, customer_type, preferences, available_choices):
    """根据偏好获取单个顾客的选择 (直接查找，无插值)"""
    customer_prefs = preferences[customer_type]
    
    best_choice = 'no_purchase'
    min_rank = customer_prefs.get('no_purchase', float('inf'))

    for choice in available_choices:
        rank = customer_prefs.get(choice, float('inf'))
        if rank < min_rank:
            min_rank = rank
            best_choice = choice
            
    return None if best_choice == 'no_purchase' else best_choice

def get_all_customer_choices(pricing_scheme, num_customers, customer_types, customer_preferences, stp_interval):
    """获取所有顾客在给定价格下的选择"""
    price_slot1, price_slot2 = pricing_scheme
    
    stps = list(range(6))
    slot1_stps = [i for i, stp_time in enumerate(stps) if stp_time * stp_interval < 30]
    slot2_stps = [i for i, stp_time in enumerate(stps) if stp_time * stp_interval >= 30]

    available_choices = []
    available_choices.extend([(stp_idx, price_slot1) for stp_idx in slot1_stps])
    available_choices.extend([(stp_idx, price_slot2) for stp_idx in slot2_stps])

    choices = []
    customer_type_mapping = {cid: type_id for type_id, cids in customer_types.items() for cid in cids}

    for cid in range(num_customers):
        ctype = customer_type_mapping[cid]
        choice = _get_customer_choice(cid, ctype, customer_preferences, available_choices)
        choices.append(choice)
    
    return choices


# === RG5/RS5 等价鲁棒成本计算函数 ===
# 该函数基于 Robust SOCDA 5.py 的精确实现，用于评估单个序列在最坏情况下的旅行与延迟成本。
# 它使用基于权重的方法选择关键弧进行扰动，以计算严格的鲁棒目标值。
def compute_rg2_robust_cost(sequence, start_pos, start_time, stp,
                             restaurant_coords, customer_coords,
                             driver_speed,
                             theta_t, deviation_ratio,
                             travel_cost_per_unit, penalty_cost_per_unit,
                             orders_data, stp_interval,
                             override_first_arc_time: float = None,
                             forced_gamma: int = None): # 新增forced_gamma
    """(源自 RS5.py, 已修改为DP评估并考虑备餐等待时间) 基于动态规划(DP)精确实现单条路径的鲁棒成本。
    返回 (robust_travel_cost, robust_penalty_cost, total_cost, final_pos, final_time)"""
    if not sequence:
        return 0.0, 0.0, 0.0, start_pos, start_time

    # 1) 构建节点、坐标和弧，并记录与送货弧关联的订单
    nodes_in_sequence = ['start']
    coords_in_sequence = [start_pos]
    arc_deliveries: Dict[int, List[int]] = {}

    for node_str in sequence:
        if node_str.startswith("pickup_"):
            oid = int(node_str.split("_")[1])
            rest_id = orders_data[oid]['restaurant_id']
            nodes_in_sequence.append(node_str)
            coords_in_sequence.append(restaurant_coords[rest_id])
        elif node_str.startswith("delivery_"):
            oid = int(node_str.split("_")[1])
            nodes_in_sequence.append(node_str)
            coords_in_sequence.append(orders_data[oid]['customer_coord'])
    
    arcs = []
    for i in range(len(coords_in_sequence) - 1):
        arcs.append((coords_in_sequence[i], coords_in_sequence[i+1]))
        if nodes_in_sequence[i+1].startswith("delivery_"):
            oid = int(nodes_in_sequence[i+1].split("_")[1])
            arc_deliveries.setdefault(i, []).append(oid)
    
    # 2) 计算名义时间与偏差
    nominal_times = []
    deviations = []
    
    is_overridden = False
    if override_first_arc_time is not None and arcs:
        is_overridden = True
        first_arc_nom_time = override_first_arc_time
        nominal_times.append(first_arc_nom_time)
        deviations.append(first_arc_nom_time * deviation_ratio)

    for i, (a, b) in enumerate(arcs):
        if is_overridden and i == 0:
            continue
        dist = math.hypot(a[0] - b[0], a[1] - b[1])
        t_nom = dist / driver_speed
        nominal_times.append(t_nom)
        deviations.append(t_nom * deviation_ratio)
    
    # 3) Gamma预算
    if forced_gamma is not None:
        gamma = forced_gamma
    else:
        gamma = math.ceil(theta_t * len(arcs)) if arcs else 0

    # 4) 动态规划: dp[i][g] = 到达节点i(完成i-1条弧)用掉g个预算的最坏到达时间。
    dp = [[-1.0] * (gamma + 1) for _ in range(len(arcs) + 1)]
    dp[0][0] = start_time

    for i in range(len(arcs)):  # 对于弧 i (从节点 i 到节点 i+1)
        t_nom, dev = nominal_times[i], deviations[i]
        
        node_type = nodes_in_sequence[i] # 弧i的起点是节点i
        food_ready_time = -1.0
        if node_type.startswith("pickup_"):
            oid = int(node_type.split("_")[1])
            order_stp = orders_data[oid]['stp']
            prep_time = orders_data[oid]['preparation_time']
            food_ready_time = order_stp * stp_interval + prep_time
        
        for g in range(gamma + 1):
            if dp[i][g] == -1.0:
                continue
            
            # 计算从节点 i 的出发时间（考虑等待）
            current_departure = max(dp[i][g], food_ready_time) if food_ready_time != -1.0 else dp[i][g]

            # 更新到达节点 i+1 的时间
            # 情况1: 当前弧不使用偏差
            next_arrival_no_dev = current_departure + t_nom
            if dp[i+1][g] == -1.0 or next_arrival_no_dev > dp[i+1][g]:
                dp[i+1][g] = next_arrival_no_dev
            
            # 情况2: 当前弧使用偏差
            if g < gamma:
                next_arrival_with_dev = current_departure + t_nom + dev
                if dp[i+1][g+1] == -1.0 or next_arrival_with_dev > dp[i+1][g+1]:
                    dp[i+1][g+1] = next_arrival_with_dev

    # 5) 基于DP结果计算成本
    # 步骤 1: 计算旅行成本。
    worst_finish_time = -1.0
    if dp and dp[-1]:
        try:
            worst_finish_time = max(val for val in dp[-1] if val != -1.0)
        except ValueError:
            worst_finish_time = start_time

    total_travel_time = worst_finish_time - start_time
    robust_travel_cost = total_travel_time * travel_cost_per_unit

    # 步骤 2: 计算延迟惩罚成本
    robust_penalty_cost = 0.0
    current_stp_time = stp * stp_interval
    est_delivery_duration = {oid: data['est_delivery'] for oid, data in orders_data.items()}

    for i in range(len(arcs)):
        if i in arc_deliveries:
            # 节点 i+1 是交付点
            worst_arrival_at_delivery_node = -1.0
            if dp[i+1]:
                try:
                    worst_arrival_at_delivery_node = max(val for val in dp[i+1] if val != -1.0)
                except ValueError:
                    worst_arrival_at_delivery_node = -1.0
            
            if worst_arrival_at_delivery_node != -1.0:
                for oid in arc_deliveries[i]:
                    target_time = current_stp_time + est_delivery_duration[oid]
                    delay = max(worst_arrival_at_delivery_node - target_time, 0)
                    robust_penalty_cost += delay * penalty_cost_per_unit

    total_cost = robust_travel_cost + robust_penalty_cost
    final_service_pos = coords_in_sequence[-1] if arcs else start_pos
    
    return robust_travel_cost, robust_penalty_cost, total_cost, final_service_pos, worst_finish_time

class OrderGroup:
    """订单组类，包含订单序列和相关信息"""
    def __init__(self, orders: List[int], sequence: List[int], starting_order: int):
        self.orders = orders  # 订单ID列表
        self.sequence = sequence  # 访问序列（包含取餐点和送餐点）
        self.starting_order = starting_order  # 起始订单ID
        self.robust_cost = 0.0 # 鲁棒成本

    def __len__(self):
        return len(self.orders)
        
    def contains_order(self, order_id: int) -> bool:
        return order_id in self.orders
    
    def get_orders_set(self) -> Set[int]:
        """返回订单集合"""
        return set(self.orders)

class OGGM:
    """订单组生成方法 (Order Group Generation Method) - 修正版，适配多司机"""
    
    def __init__(self, food_delivery_optimizer):
        """
        初始化OGGM
        
        Args:
            food_delivery_optimizer: FoodDeliveryOptimizer实例，提供所有必要参数
        """
        self.optimizer = food_delivery_optimizer
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.max_orders_per_driver = food_delivery_optimizer.max_orders_per_driver
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.orders_data = food_delivery_optimizer.orders_data
        
        # OGGM特定参数
        self.theta_t = food_delivery_optimizer.theta_t
        self.travel_time_deviation_ratio = food_delivery_optimizer.travel_time_deviation_ratio
        self.delay_threshold = 1.0  # 延迟度阈值μ
        self.delta_for_virtual_driver = 1 # 虚拟司机考虑的最近司机数量
        self.stp_interval = food_delivery_optimizer.stp_interval
        self.driver_capacity = food_delivery_optimizer.driver_capacity

        # 新增：代理Gamma，用于OGGM内部决策
        max_arcs_per_route = self.max_orders_per_driver * 2
        self.gamma_proxy = math.ceil(self.theta_t * max_arcs_per_route)
        print(f"OGGM (多司机版) 初始化完成，使用代理Gamma: {self.gamma_proxy}")

    def _is_sequence_capacity_feasible(self, sequence: List[str]) -> bool:
        """检查给定序列是否在全程满足容量约束"""
        if not sequence:
            return True
        current_load = 0
        for node in sequence:
            if node.startswith("pickup_"):
                oid = int(node.split("_")[1])
                current_load += self.orders_data[oid]['volume']
                if current_load > self.driver_capacity:
                    return False
            elif node.startswith("delivery_"):
                oid = int(node.split("_")[1])
                current_load -= self.orders_data[oid]['volume']
        return True

    def calculate_distance(self, coord1: Tuple[float, float], coord2: Tuple[float, float]) -> float:
        """
        计算两点间的欧几里得距离
        """
        return math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)
    
    def get_node_pos(self, node_str: str) -> Tuple[float, float]:
        """从节点字符串获取坐标"""
        parts = node_str.split('_')
        order_id = int(parts[1])
        if parts[0] == "pickup":
            restaurant_id = self.orders_data[order_id]['restaurant_id']
            return self.restaurant_coords[restaurant_id]
        else: # delivery
            return self.orders_data[order_id]['customer_coord']

    def calculate_sequence_robust_cost(self, sequence, start_pos, start_time, stp, override_first_arc_time: float = None) -> float:
        # 在OGGM内部，使用代理Gamma进行成本评估
        return compute_rg2_robust_cost(
            sequence, start_pos, start_time, stp,
            self.restaurant_coords, None,
            self.driver_speed,
            self.theta_t, self.travel_time_deviation_ratio,
            self.travel_cost_per_unit, self.penalty_cost_per_unit,
            self.orders_data, self.stp_interval,
            override_first_arc_time,
            forced_gamma=self.gamma_proxy # 使用代理Gamma
        )[2]

    def cheapest_insertion_single_order_robust(self, sequence: List[str], new_order_id: int,
                                               current_start_pos: Tuple[float, float],
                                               current_start_time: float, stp: int,
                                               override_first_arc_time: float = None) -> Tuple[List[str], float]:
        if not sequence:
            new_sequence = [f"pickup_{new_order_id}", f"delivery_{new_order_id}"]
            if not self._is_sequence_capacity_feasible(new_sequence):
                return new_sequence, float('inf')
            new_cost = self.calculate_sequence_robust_cost(
                new_sequence, current_start_pos, current_start_time, stp,
                override_first_arc_time=override_first_arc_time)
            return new_sequence, new_cost
        
        best_sequence = None
        best_cost = float('inf')
        
        pickup_node = f"pickup_{new_order_id}"
        delivery_node = f"delivery_{new_order_id}"
        
        for pickup_pos in range(len(sequence) + 1):
            for delivery_pos in range(pickup_pos + 1, len(sequence) + 2):
                temp_sequence = sequence[:]
                temp_sequence.insert(pickup_pos, pickup_node)
                temp_sequence.insert(delivery_pos, delivery_node)
                
                if not self._is_sequence_capacity_feasible(temp_sequence):
                    continue

                cost = self.calculate_sequence_robust_cost(
                    temp_sequence, current_start_pos, current_start_time, stp,
                    override_first_arc_time=override_first_arc_time)
                
                if cost < best_cost:
                    best_cost = cost
                    best_sequence = temp_sequence
        
        if not best_sequence:
            return [], float('inf')
            
        base_cost = self.calculate_sequence_robust_cost(
            sequence, current_start_pos, current_start_time, stp,
            override_first_arc_time=override_first_arc_time)
        
        return best_sequence, best_cost - base_cost

    def calculate_max_delay_in_sequence(self, sequence: List[str], start_time: float, 
                                        start_pos: Tuple[float, float], stp: int,
                                        override_first_arc_time: float = None) -> float:
        """(已修正)计算给定序列在鲁棒场景下的最大订单延迟度，考虑备餐时间。"""
        if not sequence: return 0.0

        current_stp_time = stp * self.stp_interval
        
        # --- 使用与主成本函数相同的DP逻辑来计算最坏到达时间 ---
        # 1) 构建节点和弧
        nodes_in_sequence = ['start']
        coords_in_sequence = [start_pos]
        arc_deliveries: Dict[int, List[int]] = {}

        for node_str in sequence:
            if node_str.startswith("pickup_"):
                oid = int(node_str.split("_")[1])
                rest_id = self.orders_data[oid]['restaurant_id']
                nodes_in_sequence.append(node_str)
                coords_in_sequence.append(self.restaurant_coords[rest_id])
            elif node_str.startswith("delivery_"):
                oid = int(node_str.split("_")[1])
                nodes_in_sequence.append(node_str)
                coords_in_sequence.append(self.orders_data[oid]['customer_coord'])
        
        arcs = []
        for i in range(len(coords_in_sequence) - 1):
            arcs.append((coords_in_sequence[i], coords_in_sequence[i+1]))
            if nodes_in_sequence[i+1].startswith("delivery_"):
                oid = int(nodes_in_sequence[i+1].split("_")[1])
                arc_deliveries.setdefault(i, []).append(oid)
        
        # 2) 计算名义时间和偏差
        nominal_times, deviations = [], []
        is_overridden = False
        if override_first_arc_time is not None and arcs:
            is_overridden = True
            nominal_times.append(override_first_arc_time)
            deviations.append(override_first_arc_time * self.travel_time_deviation_ratio)

        for i, (a, b) in enumerate(arcs):
            if is_overridden and i == 0: continue
            dist = self.calculate_distance(a,b)
            t_nom = dist / self.driver_speed
            nominal_times.append(t_nom)
            deviations.append(t_nom * self.travel_time_deviation_ratio)

        if not arcs: return 0.0

        # 3) DP计算
        gamma = math.ceil(self.theta_t * len(arcs))
        dp = [[-1.0] * (gamma + 1) for _ in range(len(arcs) + 1)]
        dp[0][0] = start_time
        
        for i in range(len(arcs)):
            t_nom, dev = nominal_times[i], deviations[i]
            node_type = nodes_in_sequence[i]
            food_ready_time = -1.0
            if node_type.startswith("pickup_"):
                oid = int(node_type.split("_")[1])
                order_stp = self.orders_data[oid]['stp']
                prep_time = self.orders_data[oid]['preparation_time']
                food_ready_time = order_stp * self.stp_interval + prep_time
            
            for g in range(gamma + 1):
                if dp[i][g] == -1.0: continue
                
                current_departure = max(dp[i][g], food_ready_time) if food_ready_time != -1.0 else dp[i][g]

                # No dev
                next_arrival_no_dev = current_departure + t_nom
                if dp[i+1][g] == -1.0 or next_arrival_no_dev > dp[i+1][g]:
                    dp[i+1][g] = next_arrival_no_dev
                
                # With dev
                if g < gamma:
                    next_arrival_with_dev = current_departure + t_nom + dev
                    if dp[i+1][g+1] == -1.0 or next_arrival_with_dev > dp[i+1][g+1]:
                        dp[i+1][g+1] = next_arrival_with_dev
            
        # 4) 计算最大延迟度
        max_delay_ratio = 0.0
        for i in range(len(arcs)):
            if i in arc_deliveries:
                worst_arrival = -1.0
                if dp[i+1]:
                    try:
                        worst_arrival = max(val for val in dp[i+1] if val != -1.0)
                    except ValueError: pass
                
                if worst_arrival != -1.0:
                    for oid in arc_deliveries[i]:
                        target_time = current_stp_time + self.orders_data[oid]['est_delivery']
                        delay = max(worst_arrival - target_time, 0)
                        est_dur = self.orders_data[oid]['est_delivery']
                        if est_dur > 0:
                            delay_ratio = delay / est_dur
                            if delay_ratio > max_delay_ratio:
                                max_delay_ratio = delay_ratio
        
        return max_delay_ratio

    def generate_order_groups_for_stp(self, orders_in_stp: List[int], stp: int,
                                      driver_states: Dict[int, Dict]) -> List[OrderGroup]:
        """为指定STP使用多司机状态生成订单组"""
        print(f"\n=== 开始为STP {stp}生成订单组 (多司机版) ===")
        print(f"STP {stp}订单: {orders_in_stp}")

        if not orders_in_stp:
            return []
        
        pickup_coords_cache = {
            oid: self.restaurant_coords[self.orders_data[oid]['restaurant_id']]
            for oid in orders_in_stp
        }

        # Step 2a 预计算每个订单自身的虚拟司机信息 (δ 最近司机)
        order_virtual_info: Dict[int, Dict[str, float]] = {}
        for oid in orders_in_stp:
            coord = pickup_coords_cache[oid]
            # 计算 δ 个最近司机
            dist_list = []
            for did, state in driver_states.items():
                dist_val = self.calculate_distance(state['pos'], coord)
                dist_list.append({'dist': dist_val, 'time': state['time']})
            dist_list.sort(key=lambda x: x['dist'])
            closest_list = dist_list[:self.delta_for_virtual_driver]
            # 平均距离与时间
            if closest_list:
                avg_dist = sum(d['dist'] for d in closest_list) / len(closest_list)
                avg_time = sum(d['time'] for d in closest_list) / len(closest_list)
                # avg_time = min(d['time'] for d in closest_list)
            else:
                avg_dist = 0.0
                avg_time = 0.0
            order_virtual_info[oid] = {
                'avg_dist': avg_dist,
                'avg_time': avg_time,
                'override_time': avg_dist / self.driver_speed
            }

        all_order_groups = []
        for starting_order in orders_in_stp:
            # 取出起始订单自身的虚拟司机统计量
            v_info_start = order_virtual_info[starting_order]
            pick_coord = pickup_coords_cache[starting_order]
            v_start_time = v_info_start['avg_time']
            override_time = v_info_start['override_time']

            current_order_cluster = {starting_order}
            current_sequence = [f"pickup_{starting_order}", f"delivery_{starting_order}"]
            
            current_cost = self.calculate_sequence_robust_cost(
                current_sequence, pick_coord, v_start_time, stp, override_first_arc_time=override_time
            )
            initial_og = OrderGroup([starting_order], current_sequence[:], starting_order)
            initial_og.robust_cost = current_cost
            all_order_groups.append(initial_og)
            
            remaining_orders = set(orders_in_stp) - {starting_order}
            
            while remaining_orders and len(current_order_cluster) < self.max_orders_per_driver:
                # --- 1. Calculate composite fit for all remaining orders ---
                order_matching_scores = []
                w_cost, w_spatial, w_temporal = 1.0, 0.5, 0.3 # 权重可调

                for order_id in remaining_orders:
                    # Cost saving calculation
                    v_info_o = order_virtual_info[order_id]
                    seq_o = [f"pickup_{order_id}", f"delivery_{order_id}"]
                    cost_o = self.calculate_sequence_robust_cost(
                        seq_o, pickup_coords_cache[order_id], v_info_o['avg_time'], stp,
                        override_first_arc_time=v_info_o['override_time'])
                    _, cost_increase = self.cheapest_insertion_single_order_robust(
                        current_sequence, order_id, pick_coord, v_start_time, stp, override_first_arc_time=override_time)
                    cost_combined = current_cost + cost_increase
                    cost_saving = current_cost + cost_o - cost_combined
                    
                    # Spatial and temporal bonus calculation
                    temp_cluster_orders = current_order_cluster | {order_id}
                    all_nodes_coords = [pickup_coords_cache[oid] for oid in temp_cluster_orders] + \
                                       [self.orders_data[oid]['customer_coord'] for oid in temp_cluster_orders]
                    spatial_bonus = 1.0 / (1.0 + np.mean([self.calculate_distance(coord, np.mean(all_nodes_coords, axis=0)) for coord in all_nodes_coords])) if all_nodes_coords else 0
                    
                    target_times = [stp * self.stp_interval + self.orders_data[oid]['est_delivery'] for oid in temp_cluster_orders]
                    temporal_bonus = 1.0 / (1.0 + np.var(target_times)) if len(target_times) > 1 else 0

                    fit = w_cost * cost_saving + w_spatial * spatial_bonus + w_temporal * temporal_bonus
                    order_matching_scores.append((order_id, fit))

                order_matching_scores.sort(key=lambda x: x[1], reverse=True)
                
                if not order_matching_scores or order_matching_scores[0][1] <= 0:
                    break
                
                # --- 2. Look-ahead Heuristic ---
                K_LOOKAHEAD = 3
                W_LOOKAHEAD = 0.5 #向前看收益的折扣权重
                best_order_to_add = None
                best_new_sequence = None
                
                candidate_evaluations = []
                # 评估前K个候选
                for order_id, initial_fit in order_matching_scores[:K_LOOKAHEAD]:
                    temp_sequence, cost_increase = self.cheapest_insertion_single_order_robust(
                        current_sequence, order_id, pick_coord, v_start_time, stp, override_first_arc_time=override_time)
                    
                    if not temp_sequence or cost_increase == float('inf'):
                        continue

                    max_delay_ratio = self.calculate_max_delay_in_sequence(
                        temp_sequence, v_start_time, pick_coord, stp, override_first_arc_time=override_time)
                    
                    if max_delay_ratio > self.delay_threshold:
                        continue # 跳过不满足延迟约束的候选
                        
                    # 执行向前看
                    cost_after_insertion = current_cost + cost_increase
                    lookahead_remaining = remaining_orders - {order_id}
                    max_next_fit = 0.0
                    if lookahead_remaining:
                        next_fits = []
                        for next_order_id in lookahead_remaining:
                            # 仅基于成本节省来计算下一步的fit，简化计算
                            v_info_next = order_virtual_info[next_order_id]
                            cost_next = self.calculate_sequence_robust_cost(
                                [f"pickup_{next_order_id}", f"delivery_{next_order_id}"], pickup_coords_cache[next_order_id], v_info_next['avg_time'], stp,
                                override_first_arc_time=v_info_next['override_time'])
                            
                            _, cost_increase_next = self.cheapest_insertion_single_order_robust(
                                temp_sequence, next_order_id, pick_coord, v_start_time, stp, override_first_arc_time=override_time)
                            cost_combined_next = cost_after_insertion + cost_increase_next
                            next_fit_saving = cost_after_insertion + cost_next - cost_combined_next
                            next_fits.append(next_fit_saving)
                        if next_fits:
                            max_next_fit = max(0, max(next_fits))
                            
                    combined_score = initial_fit + W_LOOKAHEAD * max_next_fit
                    candidate_evaluations.append({'order_id': order_id, 'score': combined_score, 'sequence': temp_sequence})
                
                # 如果有通过向前看评估的候选，则选择最优的那个
                if candidate_evaluations:
                    best_candidate = max(candidate_evaluations, key=lambda x: x['score'])
                    best_order_to_add = best_candidate['order_id']
                    best_new_sequence = best_candidate['sequence']
                else:
                    # 回退机制：如果前K个都不可行，则从剩余候选中找第一个可行的
                    for order_id, fit in order_matching_scores[K_LOOKAHEAD:]:
                        temp_sequence, _ = self.cheapest_insertion_single_order_robust(
                            current_sequence, order_id, pick_coord, v_start_time, stp, override_first_arc_time=override_time)
                        max_delay_ratio = self.calculate_max_delay_in_sequence(
                            temp_sequence, v_start_time, pick_coord, stp, override_first_arc_time=override_time)
                        if max_delay_ratio <= self.delay_threshold:
                            best_order_to_add = order_id
                            best_new_sequence = temp_sequence
                            break
                
                # --- 3. Update state with the chosen order ---
                if best_order_to_add is None:
                    break

                current_order_cluster.add(best_order_to_add)
                current_sequence = best_new_sequence
                remaining_orders.remove(best_order_to_add)
                
                current_cost = self.calculate_sequence_robust_cost(
                    current_sequence, pick_coord, v_start_time, stp, override_first_arc_time=override_time
                )
                new_og = OrderGroup(list(current_order_cluster), current_sequence[:], starting_order)
                new_og.robust_cost = current_cost
                all_order_groups.append(new_og)
        
        print(f"STP {stp}生成了{len(all_order_groups)}个订单组")
        unique_order_groups = []
        seen_order_sets = set()
        for og in sorted(all_order_groups, key=lambda x: (len(x.orders), tuple(sorted(x.orders)))):
            orders_tuple = tuple(sorted(og.orders))
            if orders_tuple not in seen_order_sets:
                unique_order_groups.append(og)
                seen_order_sets.add(orders_tuple)
        
        print(f"去重后剩余 {len(unique_order_groups)} 个订单组。")
        return unique_order_groups

class Driver:
    """司机状态类"""
    def __init__(self, driver_id: int, start_pos: Tuple[float, float], start_time: float, capacity: int):
        self.driver_id = driver_id
        self.current_pos = start_pos
        self.current_time = start_time
        self.capacity = capacity
        self.assigned_orders = []
        self.total_cost = 0.0
        self.route = [] # 存储分配的订单组序列

class GurobiSelectorAssigner:
    """
    使用Gurobi精确求解器实现多司机场景下订单组的最优选择和分配。
    """
    def __init__(self, food_delivery_optimizer):
        self.optimizer = food_delivery_optimizer
        # 共享参数
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.max_orders_per_driver = food_delivery_optimizer.max_orders_per_driver
        self.driver_capacity = food_delivery_optimizer.driver_capacity
        self.num_drivers = food_delivery_optimizer.num_drivers
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.orders_data = food_delivery_optimizer.orders_data
        
        # 鲁棒参数
        self.theta_t = food_delivery_optimizer.theta_t
        self.travel_time_deviation_ratio = food_delivery_optimizer.travel_time_deviation_ratio
        self.stp_interval = food_delivery_optimizer.stp_interval

        print("GurobiSelectorAssigner (多司机版) 初始化完成")

    def _is_driver_route_capacity_feasible(self, driver_route: List[OrderGroup]) -> bool:
        """检查一个由订单组构成的完整司机路径是否满足容量约束"""
        if not driver_route:
            return True
        
        full_sequence = []
        for og in driver_route:
            full_sequence.extend(og.sequence)

        current_load = 0
        for node in full_sequence:
            if node.startswith("pickup_"):
                oid = int(node.split("_")[1])
                current_load += self.orders_data[oid]['volume']
                if current_load > self.optimizer.driver_capacity:
                    return False
            elif node.startswith("delivery_"):
                oid = int(node.split("_")[1])
                current_load -= self.orders_data[oid]['volume']
        return True

    def calculate_distance(self, coord1: Tuple[float, float], coord2: Tuple[float, float]) -> float:
        """计算两点间的欧几里得距离"""
        return math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)
    
    def get_node_pos(self, node_str: str) -> Tuple[float, float]:
        """从节点字符串获取坐标"""
        parts = node_str.split('_')
        order_id = int(parts[1])
        if parts[0] == "pickup":
            restaurant_id = self.orders_data[order_id]['restaurant_id']
            return self.restaurant_coords[restaurant_id]
        else: # delivery
            return self.orders_data[order_id]['customer_coord']

    def calculate_robust_details_for_sequence(self, sequence: List[str], start_pos: Tuple[float, float], 
                                               start_time: float, stp: int) -> Dict:
        """为单个序列计算完整的鲁棒细节"""
        robust_travel, robust_penalty, robust_total, final_pos, final_time = compute_rg2_robust_cost(
            sequence, start_pos, start_time, stp,
            self.restaurant_coords, None, self.driver_speed,
            self.theta_t, self.travel_time_deviation_ratio,
            self.travel_cost_per_unit, self.penalty_cost_per_unit,
            self.orders_data, self.stp_interval)

        return {
            'travel_cost': robust_travel, 'penalty_cost': robust_penalty, 'total_cost': robust_total,
            'final_pos': final_pos, 'final_time': final_time
        }

    def solve(self, order_groups: List[OrderGroup], orders_in_stp: List[int], stp: int, 
              initial_driver_states: Dict[int, Dict]) -> Tuple[float, List['Driver'], Dict]:
        """
        使用Gurobi构建并求解多司机VRP-SP模型
        """
        print(f"\n--- GurobiSelectorAssigner开始为STP {stp}求解 ---")
        if not order_groups or not orders_in_stp:
            drivers = [Driver(did, s['pos'], max(s['time'], stp * self.stp_interval), self.max_orders_per_driver) for did, s in initial_driver_states.items()]
            return 0.0, drivers, {'assignments': [], 'message': 'No orders or groups'}

        # =====================================================================
        # 核心修改：采用 求解-验证-修复 的迭代方法处理容量约束
        # =====================================================================

        model = gp.Model(f"MD_VRP_SP_STP_{stp}")
        model.setParam('OutputFlag', 0)
        model.setParam('TimeLimit', 180)

        # --- 1. 预计算成本和变量定义 ---
        num_groups = len(order_groups)
        depot_idx = num_groups
        group_details = {}
        for i, og in enumerate(order_groups):
            details = self.calculate_robust_details_for_sequence(og.sequence, self.get_node_pos(og.sequence[0]), 0, stp)
            group_details[i] = {
                'internal_cost': details['total_cost'],
                'internal_duration': details['final_time'],
                'start_pos': self.get_node_pos(og.sequence[0]),
                'end_pos': details['final_pos'],
                'num_orders': len(og.orders),
                'volume': sum(self.orders_data[oid]['volume'] for oid in og.orders)
            }
        
        x = model.addVars(num_groups + 1, num_groups + 1, self.num_drivers, vtype=GRB.BINARY, name="x")
        y = model.addVars(num_groups, vtype=GRB.BINARY, name="y")
        a = model.addVars(num_groups, self.num_drivers, vtype=GRB.CONTINUOUS, lb=0, name="a")
        
        # --- 2. 目标函数 (与之前相同) ---
        total_cost_expr = gp.LinExpr()
        for k in range(self.num_drivers):
            driver_state = initial_driver_states[k]
            for j in range(num_groups):
                dist = self.calculate_distance(driver_state['pos'], group_details[j]['start_pos'])
                robust_travel_time = (dist / self.driver_speed) * (1 + self.travel_time_deviation_ratio)
                total_cost_expr += robust_travel_time * self.travel_cost_per_unit * x[depot_idx, j, k]
        total_cost_expr += gp.quicksum(
            self.calculate_distance(group_details[i]['end_pos'], group_details[j]['start_pos']) /
            self.driver_speed * (1 + self.travel_time_deviation_ratio) * self.travel_cost_per_unit * x.sum(i, j, '*')
            for i in range(num_groups) for j in range(num_groups) if i != j)
        total_cost_expr += gp.quicksum(group_details[i]['internal_cost'] * y[i] for i in range(num_groups))
        model.setObjective(total_cost_expr, GRB.MINIMIZE)
        
        # --- 3. 基本VRP约束 ---
        for order_id in orders_in_stp:
            model.addConstr(gp.quicksum(y[i] for i, og in enumerate(order_groups) if order_id in og.orders) == 1, f"cover_{order_id}")
        for i in range(num_groups):
            model.addConstr(y[i] == x.sum(i, '*', '*'), f"link_y_out_{i}")
            model.addConstr(y[i] == x.sum('*', i, '*'), f"link_y_in_{i}")
        for k in range(self.num_drivers):
            model.addConstr(x.sum(depot_idx, '*', k) <= 1, f"leave_depot_{k}")
            model.addConstr(x.sum('*', depot_idx, k) <= 1, f"enter_depot_{k}")
            model.addConstr(x.sum(depot_idx, '*', k) == x.sum('*', depot_idx, k), f"depot_balance_{k}")
            for i in range(num_groups):
                model.addConstr(x.sum(i, '*', k) == x.sum('*', i, k), f"flow_balance_{i}_{k}")
                model.addConstr(x[i, i, k] == 0, f"no_self_loop_{i}_{k}")
        for k in range(self.num_drivers):
            model.addConstr(gp.quicksum(group_details[i]['num_orders'] * x.sum(i, '*', k) for i in range(num_groups)) <= self.max_orders_per_driver, f"order_count_capacity_{k}")
        M = 10000 
        for k in range(self.num_drivers):
            driver_state = initial_driver_states[k]
            start_time_k = max(driver_state['time'], stp * self.stp_interval)
            for i in range(num_groups):
                travel_time_from_depot = self.calculate_distance(driver_state['pos'], group_details[i]['start_pos']) / self.driver_speed
                robust_tt_from_depot = travel_time_from_depot * (1 + self.travel_time_deviation_ratio)
                model.addConstr(a[i, k] >= start_time_k + robust_tt_from_depot - M * (1 - x[depot_idx, i, k]), f"time_from_depot_{i}_{k}")
                for j in range(num_groups):
                    if i != j:
                        travel_time_ij = self.calculate_distance(group_details[i]['end_pos'], group_details[j]['start_pos']) / self.driver_speed
                        robust_tt_ij = travel_time_ij * (1 + self.travel_time_deviation_ratio)
                        model.addConstr(a[j, k] >= a[i, k] + group_details[i]['internal_duration'] + robust_tt_ij - M * (1 - x[i, j, k]), f"time_between_{i}_{j}_{k}")

        # --- 4. 迭代求解与修复循环 ---
        while True:
            model.optimize()

            if model.status not in (GRB.OPTIMAL, GRB.SUBOPTIMAL):
                print("警告: Gurobi未能找到可行解。")
                if model.status == GRB.INFEASIBLE:
                    print("模型不可行。请检查约束。")
                    model.computeIIS()
                    model.write(f"infeasible_model_stp_{stp}.ilp")
                drivers = [Driver(did, s['pos'], s['time'], self.max_orders_per_driver) for did, s in initial_driver_states.items()]
                return 0.0, drivers, {'assignments': [], 'message': f'Gurobi failed with status {model.status}'}

            # 解析当前解，检查容量可行性
            all_routes_feasible = True
            routes_to_cut = []

            for k in range(self.num_drivers):
                # 检查司机是否有任务
                if x.sum(depot_idx, '*', k).getValue() < 0.5:
                    continue
                
                # 重建路径
                path_indices = []
                curr_node_idx = depot_idx
                while True:
                    found_next = False
                    for j in range(num_groups):
                        if x[curr_node_idx, j, k].X > 0.5:
                            path_indices.append(j)
                            curr_node_idx = j
                            found_next = True
                            break
                    if not found_next:
                        break
                
                driver_route_groups = [order_groups[i] for i in path_indices]

                # 验证路径容量
                if not self._is_driver_route_capacity_feasible(driver_route_groups):
                    all_routes_feasible = False
                    # 记录需要添加切割的路径
                    routes_to_cut.append((k, path_indices))
            
            # 如果所有路径都可行，则找到了最终解
            if all_routes_feasible:
                print("Gurobi找到一个满足所有约束的可行解。")
                break
            
            # 如果存在不可行路径，添加惰性约束并重新求解
            print("Gurobi解不满足实时容量约束，添加惰性约束并重新求解...")
            for k_cut, path_to_cut in routes_to_cut:
                # 禁止这些订单组被分配给这个司机
                # model.addConstr(gp.quicksum(y[i] for i in path_to_cut) <= len(path_to_cut) - 1)
                model.addConstr(gp.quicksum(x.sum('*', i, k_cut) for i in path_to_cut) <= len(path_to_cut) - 1)


        # --- 5. 解析最终结果 (与之前类似) ---
        final_drivers = {did: Driver(did, s['pos'], max(s['time'], stp * self.stp_interval), self.max_orders_per_driver) for did, s in initial_driver_states.items()}
        
        for k in range(self.num_drivers):
            path_indices = []
            curr_node_idx = depot_idx
            if x.sum(depot_idx, '*', k).getValue() > 0.5:
                while True:
                    found_next = False
                    for j in range(num_groups):
                        if x[curr_node_idx, j, k].X > 0.5:
                            path_indices.append(j)
                            curr_node_idx = j
                            found_next = True
                            break
                    if not found_next:
                        break
            final_drivers[k].route = [order_groups[i] for i in path_indices]

        total_travel_cost, total_penalty_cost = 0, 0
        assignment_details = {'assignments': []}

        for k, driver in final_drivers.items():
            if not driver.route: continue
            
            full_sequence = []
            for og in driver.route:
                full_sequence.extend(og.sequence)
                driver.assigned_orders.extend(og.orders)

            details = self.calculate_robust_details_for_sequence(full_sequence, driver.current_pos, driver.current_time, stp)
            
            driver.current_pos = details['final_pos']
            driver.current_time = details['final_time']
            driver.total_cost = details['total_cost']
            
            total_travel_cost += details['travel_cost']
            total_penalty_cost += details['penalty_cost']
            
            assignment_details['assignments'].append({
                'driver_id': k,
                'route': [og.orders for og in driver.route],
                'cost': details['total_cost']
            })
        
        total_cost = total_travel_cost + total_penalty_cost
        assignment_details['total_travel_cost'] = total_travel_cost
        assignment_details['total_penalty_cost'] = total_penalty_cost
        
        print(f"Gurobi求解成功，总成本: {total_cost:.3f}")

        return total_cost, list(final_drivers.values()), assignment_details

class HybridSolver:
    """
    结合OGGM和Gurobi的多司机、多STP混合调度算法
    """
    def __init__(self, food_delivery_optimizer):
        self.optimizer = food_delivery_optimizer
        self.oggm = OGGM(food_delivery_optimizer)
        self.gurobi_assigner = GurobiSelectorAssigner(food_delivery_optimizer)
        print("HybridSolver (多司机版) 初始化完成")
    
    def solve_single_stp_dispatch(self, orders_in_stp: List[int], stp: int, 
                                  initial_driver_states: Dict[int, Dict]) -> Tuple[float, float, List[Driver], Dict]:
        """
        求解单个STP的调度问题
        """
        print(f"\n{'='*60}")
        print(f"HybridSolver求解STP {stp}调度问题")
        print(f"订单: {orders_in_stp}")
        
        # 更新司机时间到当前STP开始时间
        active_driver_states = copy.deepcopy(initial_driver_states)
        for k in active_driver_states:
            active_driver_states[k]['time'] = max(active_driver_states[k]['time'], stp * self.optimizer.stp_interval)

        if not orders_in_stp:
            drivers = [Driver(did, s['pos'], s['time'], self.optimizer.max_orders_per_driver) for did, s in active_driver_states.items()]
            return 0.0, 0.0, drivers, {'message': 'No orders in STP'}

        try:
            order_groups = self.oggm.generate_order_groups_for_stp(
                orders_in_stp, stp, active_driver_states
            )
            
            if not order_groups:
                print("警告：OGGM未生成任何订单组")
                drivers = [Driver(did, s['pos'], s['time'], self.optimizer.max_orders_per_driver) for did, s in active_driver_states.items()]
                return 0.0, 0.0, drivers, {'assignments': [], 'message': 'No order groups generated'}
            
            total_cost, drivers_final_state, assignment_details = self.gurobi_assigner.solve(
                order_groups, orders_in_stp, stp, active_driver_states
            )
            
            travel_cost = assignment_details.get('total_travel_cost', 0)
            penalty_cost = assignment_details.get('total_penalty_cost', 0)
            
            print(f"\n--- HybridSolver STP {stp} 求解完成 ---")
            print(f"旅行成本: {travel_cost:.3f}, 延迟惩罚: {penalty_cost:.3f}, 总成本: {total_cost:.3f}")
            
            return travel_cost, penalty_cost, drivers_final_state, assignment_details
            
        except Exception as e:
            print(f"HybridSolver求解STP {stp}出错: {str(e)}")
            import traceback
            traceback.print_exc()
            drivers = [Driver(did, s['pos'], s['time'], self.optimizer.max_orders_per_driver) for did, s in active_driver_states.items()]
            return 0.0, 0.0, drivers, {'error': str(e)}

def compute_exact_robust_cost_for_route(full_sequence, start_pos, start_time, stp, optimizer):
    """
    (第四次修正) 使用精确的"全局Gamma"DP逻辑，重新计算给定路径的旅行成本和延迟成本，并考虑备餐时间。
    该方法模拟了Robust Gurobi 7.py的底层成本计算逻辑，解决了成本计算不一致的问题。
    """
    if not full_sequence:
        return 0.0, 0.0, 0.0

    # 1. 提取参数与构建基础信息
    orders_data = optimizer.orders_data
    restaurant_coords = optimizer.restaurant_coords
    driver_speed = optimizer.driver_speed
    deviation_ratio = optimizer.travel_time_deviation_ratio
    theta_t = optimizer.theta_t
    travel_cost_per_unit = optimizer.travel_cost_per_unit
    penalty_cost_per_unit = optimizer.penalty_cost_per_unit
    current_stp_time = stp * optimizer.stp_interval
    stp_interval = optimizer.stp_interval

    nodes_in_sequence = ['start']
    arcs, delivery_arc_indices = [], {}
    cur = start_pos
    for i, node in enumerate(full_sequence):
        if node.startswith("pickup_"):
            oid = int(node.split("_")[1])
            nxt = restaurant_coords[orders_data[oid]['restaurant_id']]
            nodes_in_sequence.append(node)
        elif node.startswith("delivery_"):
            oid = int(node.split("_")[1])
            nxt = orders_data[oid]['customer_coord']
            delivery_arc_indices.setdefault(i, []).append(oid)
            nodes_in_sequence.append(node)
        else: continue
        arcs.append((cur, nxt))
        cur = nxt

    if not arcs: return 0.0, 0.0, 0.0

    num_arcs = len(arcs)
    nominal_times = [math.hypot(a[0] - b[0], a[1] - b[1]) / driver_speed for a, b in arcs]
    deviations = [t * deviation_ratio for t in nominal_times]
    global_gamma = math.ceil(theta_t * num_arcs)

    # 内部辅助函数：在给定的(子)路径上运行DP，返回最坏情况的结束时间
    def _get_worst_arrival_time(path_arcs, path_nom_times, path_devs, sub_start_time, budget, path_node_types):
        n = len(path_arcs)
        if n == 0: return sub_start_time
        
        effective_budget = min(budget, n)
        
        dp = [[-1.0] * (effective_budget + 1) for _ in range(n + 1)]
        dp[0][0] = sub_start_time

        for i in range(n):
            t_nom, dev = path_nom_times[i], path_devs[i]

            node_type = path_node_types[i]
            food_ready_time = -1.0
            if node_type.startswith("pickup_"):
                oid = int(node_type.split("_")[1])
                order_stp = orders_data[oid]['stp']
                prep_time = orders_data[oid]['preparation_time']
                food_ready_time = order_stp * stp_interval + prep_time
            
            for g in range(effective_budget + 1):
                if dp[i][g] == -1.0:
                    continue
                
                current_departure = max(dp[i][g], food_ready_time) if food_ready_time != -1.0 else dp[i][g]

                # No dev
                next_arrival_no_dev = current_departure + t_nom
                if dp[i+1][g] == -1.0 or next_arrival_no_dev > dp[i+1][g]:
                    dp[i+1][g] = next_arrival_no_dev
                
                # With dev
                if g < effective_budget:
                    next_arrival_with_dev = current_departure + t_nom + dev
                    if dp[i+1][g+1] == -1.0 or next_arrival_with_dev > dp[i+1][g+1]:
                        dp[i+1][g+1] = next_arrival_with_dev
        
        try:
            return max(val for val in dp[n] if val != -1.0)
        except ValueError:
            return sub_start_time

    # 2. 精确计算旅行成本 (基于整条路径的DP)
    worst_finish_time = _get_worst_arrival_time(arcs, nominal_times, deviations, start_time, global_gamma, nodes_in_sequence)
    exact_travel_cost = (worst_finish_time - start_time) * travel_cost_per_unit

    # 3. 精确计算延迟成本 (对每个交付点，使用全局Gamma在其子路径上运行DP)
    exact_penalty_cost = 0.0
    for i in range(num_arcs):
        if i in delivery_arc_indices:
            # 提取到达此点的子路径信息
            sub_path_arcs = arcs[:i+1]
            sub_path_nom_times = nominal_times[:i+1]
            sub_path_devs = deviations[:i+1]
            sub_path_node_types = nodes_in_sequence[:i+2] # 包括起点和到i+1的所有节点
            
            # 使用全局Gamma预算计算此点的最坏到达时间
            worst_arrival_at_i = _get_worst_arrival_time(sub_path_arcs, sub_path_nom_times, sub_path_devs, start_time, global_gamma, sub_path_node_types)
            
            # 计算并累加惩罚
            for oid in delivery_arc_indices[i]:
                target = current_stp_time + orders_data[oid]['est_delivery']
                delay = max(0, worst_arrival_at_i - target)
                exact_penalty_cost += delay * penalty_cost_per_unit
    
    exact_total_cost = exact_travel_cost + exact_penalty_cost
    return exact_travel_cost, exact_penalty_cost, exact_total_cost

def run_robust_hybrid_analysis():
    """完整测试HybridSolver在多司机、多STP场景下的表现"""
    print("="*80)
    print("HybridSolver完整算法测试 - 鲁棒多司机场景")
    print("="*80)
    
    class MockFoodDeliveryOptimizer:
        def __init__(self):
            self.travel_cost_per_unit = 0.2
            self.penalty_cost_per_unit = 1.0
            self.driver_speed = 1.0
            self.max_orders_per_driver = 5
            self.driver_capacity = 30
            self.num_drivers = 2
            self.stps = list(range(6))
            self.stp_interval = 10
            self.commission_rate = 0.18
            self.theta_t = 0.3
            self.travel_time_deviation_ratio = 0.2
            
            # --- 新增：客户选择与动态定价参数 ---
            self.k_dist = 0.1
            self.k_val = 0.01
            self.num_customers = 18
            
            # 客户类型分配 (每类3人, 共6类)
            self.customer_types = {
                0: list(range(0, 3)),   # 价格至上型
                1: list(range(3, 6)),   # 时间敏感型 (早期)
                2: list(range(6, 9)),   # 时间敏感型 (中期)
                3: list(range(9, 12)),  # 时间敏感型 (晚期)
                4: list(range(12, 15)), # 务实平衡型
                5: list(range(15, 18))  # 品质无忧型
            }
            
            # 客户偏好列表 (rank越小越好) - 重新设计以分散订单
            self.customer_preferences = {
                # Type 0: 价格至上，便宜就行，对时间无所谓
                0: {
                    (0, 3): 1, (1, 3): 2, (2, 3): 3, (3, 3): 4, (4, 3): 5, (5, 3): 6,
                    'no_purchase': 7,
                    (0, 6): 8, (1, 6): 9, (2, 6): 10, (3, 6): 11, (4, 6): 12, (5, 6): 13
                },
                # Type 1: 时间敏感 (早期)，必须在STP 0或1下单
                1: {
                    (0, 3): 1, (0, 6): 2, (1, 3): 3, (1, 6): 4,
                    'no_purchase': 5,
                    (2, 3): 6, (2, 6): 7, (3, 3): 8, (3, 6): 9, (4, 3): 10, (4, 6): 11, (5, 3): 12, (5, 6): 13
                },
                # Type 2: 时间敏感 (中期)，必须在STP 2或3下单
                2: {
                    (2, 3): 1, (2, 6): 2, (3, 3): 3, (3, 6): 4,
                    'no_purchase': 5,
                    (0, 3): 6, (0, 6): 7, (1, 3): 8, (1, 6): 9, (4, 3): 10, (4, 6): 11, (5, 3): 12, (5, 6): 13
                },
                # Type 3: 时间敏感 (晚期)，必须在STP 4或5下单
                3: {
                    (4, 3): 1, (4, 6): 2, (5, 3): 3, (5, 6): 4,
                    'no_purchase': 5,
                    (0, 3): 6, (0, 6): 7, (1, 3): 8, (1, 6): 9, (2, 3): 10, (2, 6): 11, (3, 3): 12, (3, 6): 13
                },
                # Type 4: 务实平衡型，偏好早和便宜，但有弹性
                4: {
                    (0, 3): 1, (1, 3): 2, (0, 6): 3, (2, 3): 4,
                    'no_purchase': 5,
                    (1, 6): 6, (3, 3): 7, (2, 6): 8, (4, 3): 9, (3, 6): 10, (5, 3): 11, (4, 6): 12, (5, 6): 13
                },
                # Type 5: 品质无忧型，不在乎价格，偏好中期，基本不下单
                5: {
                    (2, 6): 1, (3, 6): 2, (2, 3): 3, (3, 3): 4, (1, 6): 5, (4, 6): 6, (1, 3): 7, (4, 3): 8,
                    (0, 6): 9, (5, 6): 10, (0, 3): 11, (5, 3): 12,
                    'no_purchase': 13
                }
            }
            
            self.restaurant_coords = {0: (2, 2), 1: (8, 2)}
            self.driver_start_coords = {0: (5, 1), 1: (6, 1)}
            
            self.potential_orders_data = {
                0: {'value': 25, 'restaurant_id': 0, 'customer_coord': (1, 5), 'est_delivery': 15, 'volume': 5, 'preparation_time': 5.0},
                1: {'value': 30, 'restaurant_id': 1, 'customer_coord': (9, 4), 'est_delivery': 12, 'volume': 6, 'preparation_time': 6.0},
                2: {'value': 28, 'restaurant_id': 0, 'customer_coord': (3, 6), 'est_delivery': 18, 'volume': 6, 'preparation_time': 5.6},
                3: {'value': 32, 'restaurant_id': 1, 'customer_coord': (7, 5), 'est_delivery': 14, 'volume': 7, 'preparation_time': 6.4},
                4: {'value': 26, 'restaurant_id': 0, 'customer_coord': (2, 7), 'est_delivery': 16, 'volume': 6, 'preparation_time': 5.2},
                5: {'value': 35, 'restaurant_id': 1, 'customer_coord': (8, 6), 'est_delivery': 13, 'volume': 7, 'preparation_time': 7.0},
                6: {'value': 29, 'restaurant_id': 0, 'customer_coord': (4, 5), 'est_delivery': 15, 'volume': 6, 'preparation_time': 5.8},
                7: {'value': 33, 'restaurant_id': 1, 'customer_coord': (6, 7), 'est_delivery': 17, 'volume': 7, 'preparation_time': 6.6},
                8: {'value': 27, 'restaurant_id': 0, 'customer_coord': (1, 8), 'est_delivery': 20, 'volume': 6, 'preparation_time': 5.4},
                9: {'value': 31, 'restaurant_id': 1, 'customer_coord': (9, 7), 'est_delivery': 16, 'volume': 7, 'preparation_time': 6.2},
                10: {'value': 24, 'restaurant_id': 0, 'customer_coord': (3, 4), 'est_delivery': 14, 'volume': 5, 'preparation_time': 4.8},
                11: {'value': 36, 'restaurant_id': 1, 'customer_coord': (7, 8), 'est_delivery': 18, 'volume': 8, 'preparation_time': 7.2},
                12: {'value': 28, 'restaurant_id': 0, 'customer_coord': (2, 6), 'est_delivery': 15, 'volume': 6, 'preparation_time': 5.6},
                13: {'value': 34, 'restaurant_id': 1, 'customer_coord': (8, 5), 'est_delivery': 13, 'volume': 7, 'preparation_time': 6.8},
                14: {'value': 30, 'restaurant_id': 0, 'customer_coord': (4, 7), 'est_delivery': 17, 'volume': 6, 'preparation_time': 6.0},
                15: {'value': 32, 'restaurant_id': 1, 'customer_coord': (6, 8), 'est_delivery': 16, 'volume': 7, 'preparation_time': 6.4},
                16: {'value': 26, 'restaurant_id': 0, 'customer_coord': (1, 6), 'est_delivery': 18, 'volume': 6, 'preparation_time': 5.2},
                17: {'value': 37, 'restaurant_id': 1, 'customer_coord': (9, 6), 'est_delivery': 14, 'volume': 8, 'preparation_time': 7.4}
            }
            self.orders_data = self.potential_orders_data

            # 预计算配送距离
            self.delivery_distances = {}
            for i in range(self.num_customers):
                order_info = self.potential_orders_data[i]
                rest_id = order_info['restaurant_id']
                rest_coord = self.restaurant_coords[rest_id]
                cust_coord = order_info['customer_coord']
                self.delivery_distances[i] = math.hypot(rest_coord[0] - cust_coord[0], rest_coord[1] - cust_coord[1])

    mock_optimizer = MockFoodDeliveryOptimizer()
    hybrid_solver = HybridSolver(mock_optimizer)
        
    pricing_schemes = [(3, 3), (3, 6), (6, 3), (6, 6)]
    all_results = []
        
    for price_slot1, price_slot2 in pricing_schemes:
        print(f"\n{'='*70}\n测试定价方案: (时段1基础价={price_slot1}, 时段2基础价={price_slot2})\n{'='*70}")
        
        # --- 1. 客户选择 ---
        customer_choices = get_all_customer_choices(
            (price_slot1, price_slot2),
            mock_optimizer.num_customers,
            mock_optimizer.customer_types,
            mock_optimizer.customer_preferences,
            mock_optimizer.stp_interval
        )
        orders_by_stp = {stp: [] for stp in mock_optimizer.stps}
        
        # --- 2. 收入计算 ---
        total_order_value = 0
        total_delivery_fee = 0
        
        print("客户选择结果与动态费用:")
        for cid, choice in enumerate(customer_choices):
            if choice:
                stp, base_price = choice
                orders_by_stp[stp].append(cid)
                
                order_value = mock_optimizer.potential_orders_data[cid]['value']
                dynamic_fee = _calculate_delivery_fee(
                    cid, base_price,
                    mock_optimizer.potential_orders_data,
                    mock_optimizer.delivery_distances,
                    mock_optimizer.k_dist, mock_optimizer.k_val
                )
                total_order_value += order_value
                total_delivery_fee += dynamic_fee
                print(f"  客户 {cid}: 选择STP {stp}, 基础价 {base_price}, 实际支付 ¥{dynamic_fee:.2f}")
            else:
                print(f"  客户 {cid}: 选择不购买")

        commission = total_order_value * mock_optimizer.commission_rate
        revenue = commission + total_delivery_fee
        
        # --- 3. 逐STP进行路径规划 ---
        driver_states = {
            k: {'pos': mock_optimizer.driver_start_coords[k], 'time': 0.0}
            for k in range(mock_optimizer.num_drivers)
        }
        
        total_travel_cost = 0.0
        total_penalty_cost = 0.0

        for stp in mock_optimizer.stps:
            stp_orders = orders_by_stp[stp]

            # 动态更新优化器中的订单数据
            mock_optimizer.orders_data = {oid: mock_optimizer.potential_orders_data[oid] for oid in stp_orders}
            for oid in stp_orders:
                 mock_optimizer.orders_data[oid]['stp'] = stp
            
            hybrid_solver.oggm.orders_data = mock_optimizer.orders_data
            hybrid_solver.gurobi_assigner.orders_data = mock_optimizer.orders_data

            travel, penalty, final_drivers, _ = hybrid_solver.solve_single_stp_dispatch(
                stp_orders, stp, driver_states)
            
            total_travel_cost += travel
            total_penalty_cost += penalty
            
            for driver in final_drivers:
                driver_states[driver.driver_id] = {'pos': driver.current_pos, 'time': driver.current_time}
        
        # 恢复完整的订单数据
        mock_optimizer.orders_data = mock_optimizer.potential_orders_data
        
        total_cost = total_travel_cost + total_penalty_cost
        profit = revenue - total_cost
            
        result = {
            'scheme': (price_slot1, price_slot2), 'revenue': revenue,
            'travel_cost': total_travel_cost, 'penalty_cost': total_penalty_cost,
            'total_cost': total_cost, 'profit': profit,
            'customer_choices': customer_choices,
            'initial_driver_states_for_recalc': { k: {'pos': mock_optimizer.driver_start_coords[k], 'time': 0.0} for k in range(mock_optimizer.num_drivers)}
        }
        all_results.append(result)
            
        print(f"\n--- 方案({price_slot1}, {price_slot2})汇总 ---")
        print(f"总收入: {revenue:.2f}, 总运营成本: {total_cost:.2f}, 总利润: {profit:.2f}")

    if not all_results:
        print("所有方案均未成功运行。")
        return

    # --- (新增) 后处理：使用精确DP重新计算所有方案的成本 ---
    print(f"\n{'='*80}")
    print("后处理：使用精确DP逻辑重新计算最终成本")
    print(f"{'='*80}")
    
    recalculated_results = []
    for result in all_results:
        print(f"\n--- 重新计算方案: {result['scheme']} ---")
        
        # 重新模拟整个过程以获取每一步的路径
        driver_states = copy.deepcopy(result['initial_driver_states_for_recalc'])
        customer_choices = result['customer_choices']
        orders_by_stp = {stp: [] for stp in mock_optimizer.stps}
        for cid, choice in enumerate(customer_choices):
            if choice:
                stp, _ = choice
                orders_by_stp[stp].append(cid)
        
        exact_total_travel = 0
        exact_total_penalty = 0

        for stp in mock_optimizer.stps:
            stp_orders = orders_by_stp[stp]
            
            # 动态更新优化器中的订单数据
            mock_optimizer.orders_data = {oid: mock_optimizer.potential_orders_data[oid] for oid in stp_orders}
            for oid in stp_orders:
                 mock_optimizer.orders_data[oid]['stp'] = stp
            hybrid_solver.oggm.orders_data = mock_optimizer.orders_data
            hybrid_solver.gurobi_assigner.orders_data = mock_optimizer.orders_data

            # 必须调用solver来获取路径分配
            _, _, final_drivers, assignment_details = hybrid_solver.solve_single_stp_dispatch(
                stp_orders, stp, driver_states
            )
            
            if not assignment_details.get('assignments'):
                # 更新司机状态以继续模拟
                for driver in final_drivers:
                    driver_states[driver.driver_id] = {'pos': driver.current_pos, 'time': driver.current_time}
                continue

            print(f"  STP {stp}:")
            # 对每个有任务的司机，获取其完整路径并用精确DP重算
            for driver in final_drivers:
                if not driver.route: continue
                
                full_sequence = []
                for og in driver.route:
                    full_sequence.extend(og.sequence)
                
                # 获取该司机在STP开始时的状态
                initial_driver_state = driver_states[driver.driver_id]
                start_pos = initial_driver_state['pos']
                start_time = max(initial_driver_state['time'], stp * mock_optimizer.stp_interval)

                # 使用精确DP函数计算
                exact_travel, exact_penalty, exact_total = compute_exact_robust_cost_for_route(
                    full_sequence, start_pos, start_time, stp, mock_optimizer
                )
                
                print(f"    - 司机 {driver.driver_id}: 精确旅行成本={exact_travel:.2f}, 精确延迟成本={exact_penalty:.2f}")
                exact_total_travel += exact_travel
                exact_total_penalty += exact_penalty

            # 更新司机状态以继续模拟
            for driver in final_drivers:
                driver_states[driver.driver_id] = {'pos': driver.current_pos, 'time': driver.current_time}
        
        exact_total_cost = exact_total_travel + exact_total_penalty
        exact_profit = result['revenue'] - exact_total_cost

        recalculated_results.append({
            'scheme': result['scheme'],
            'revenue': result['revenue'],
            'travel_cost': exact_total_travel,
            'penalty_cost': exact_total_penalty,
            'total_cost': exact_total_cost,
            'profit': exact_profit
        })

    best_scheme = max(all_results, key=lambda x: x['profit'])
    
    print(f"\n{'='*80}")
    print("所有定价方案结果汇总对比")
    print(f"{'='*80}")
    print(f"{'方案':<12} {'收入':<10} {'旅行成本':<12} {'延迟成本':<12} {'总成本':<12} {'利润':<10} {'最优':<6}")
    print("-" * 85)
    
    for result in all_results:
        scheme_str = f"({result['scheme'][0]}, {result['scheme'][1]})"
        is_best = "★" if result == best_scheme else ""
        print(f"{scheme_str:<12} {result['revenue']:<10.2f} {result['travel_cost']:<12.2f} "
              f"{result['penalty_cost']:<12.2f} {result['total_cost']:<12.2f} "
              f"{result['profit']:<10.2f} {is_best:<6}")
    
    # --- (新增) 打印精确重算后的结果 ---
    print(f"\n\n{'='*80}")
    print("精确重算后的鲁棒成本与利润对比")
    print(f"{'='*80}")
    print(f"{'方案':<12} {'收入':<10} {'精确旅行':<12} {'精确延迟':<12} {'精确总成本':<14} {'精确利润':<12} {'最优':<6}")
    print("-" * 95)
    
    if recalculated_results:
        best_recalc_scheme = max(recalculated_results, key=lambda x: x['profit'])
        for result in recalculated_results:
            scheme_str = f"({result['scheme'][0]}, {result['scheme'][1]})"
            is_best = "★" if result == best_recalc_scheme else ""
            print(f"{scheme_str:<12} {result['revenue']:<10.2f} {result['travel_cost']:<12.2f} "
                  f"{result['penalty_cost']:<12.2f} {result['total_cost']:<14.2f} "
                  f"{result['profit']:<12.2f} {is_best:<6}")

    return hybrid_solver, all_results

if __name__ == "__main__":
    # 运行完整测试
    hybrid_solver_instance, test_results = run_robust_hybrid_analysis()