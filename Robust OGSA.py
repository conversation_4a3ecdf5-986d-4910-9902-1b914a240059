import json
import pandas as pd
import numpy as np
from math import radians, cos, sin, asin, sqrt, ceil
import tempfile
import os
import math

class Order:
    """订单类 - 与OGGM保持一致的结构"""
    def __init__(self, order_id, restaurant_lng, restaurant_lat, customer_lng, customer_lat, 
                 chosen_time, chosen_price, order_value, original_order_id, estimated_delivery_time,
                 preparation_time, volume):
        self.order_id = order_id  # consumer_id作为标识
        self.restaurant_lng = restaurant_lng
        self.restaurant_lat = restaurant_lat
        self.customer_lng = customer_lng
        self.customer_lat = customer_lat
        self.chosen_time = chosen_time
        self.chosen_price = chosen_price
        self.order_value = order_value
        self.original_order_id = original_order_id  # 平台原始订单ID
        self.estimated_delivery_time = estimated_delivery_time  # 预期配送时长
        self.preparation_time = preparation_time
        self.volume = volume

class OGSAAlgorithm:
    """订单组选择算法（OGSA）"""
    
    def __init__(self, stp_choices_path, oggm_results_path, 
                 orders_data_path='meituan_orders_with_delivery_time.csv',
                 theta_t=0.3, travel_time_deviation_ratio=0.2, num_drivers=30):
        """
        初始化OGSA算法
        
        Args:
            stp_choices_path: 消费者STP选择结果文件路径
            oggm_results_path: OGGM算法结果文件路径
            orders_data_path: 订单数据文件路径
            theta_t: 鲁棒优化预算 gamma
            travel_time_deviation_ratio: 旅行时间最大偏差率
            num_drivers: 司机数量
        """
        self.orders_data_path = orders_data_path
        self.stp_choices_path = stp_choices_path
        self.oggm_results_path = oggm_results_path
        
        # 核心数据
        self.order_positions = {}
        self.oggm_results = {}
        self.driver_positions = []
        self.reconstructed_order_groups = {}
        
        # 算法参数
        self.delta_max = 3      # 最大delta值
        self.epsilon = 1.6      # 大小偏好参数
        self.alpha = 0.2        # 旅行成本系数 (RMB/km)
        self.beta = 1           # 延迟惩罚系数 (RMB/min)
        self.delta_bar = 2      # 虚拟司机计算参数
        self.rider_speed_kmh = 20  # 骑手速度
        
        # 新增：司机数量
        self.num_drivers = num_drivers
        
        # === Robust uncertainty parameters ===
        self.theta_t = theta_t
        self.travel_time_deviation_ratio = travel_time_deviation_ratio
        
        # 初始化数据
        self._load_all_data()
    
    def _load_all_data(self):
        """加载所有必要数据"""
        
        # 加载订单位置数据
        self._load_order_position_data()
        
        # 加载OGGM结果
        self._load_oggm_results()
        
        # 加载司机位置
        self._load_driver_positions()
        
        # 重构订单组
        self._reconstruct_order_groups()
    
    def _load_order_position_data(self):
        """从CSV文件和STP选择结果加载订单位置数据"""
        
        # 读取订单数据（包含位置信息和预期配送时长）
        orders_df = pd.read_csv(self.orders_data_path)
        
        # 读取消费者STP选择结果  
        stp_df = pd.read_csv(self.stp_choices_path)
        stp_df = stp_df[stp_df['choice_type'] == 'purchase']
        
        
        # 建立consumer_id到订单位置的映射
        for _, stp_row in stp_df.iterrows():
            consumer_id = stp_row['consumer_id']
            
            # consumer_id从1开始，对应orders_df的索引从0开始
            if consumer_id <= len(orders_df):
                order_row = orders_df.iloc[consumer_id - 1]
                
                # 检查必要字段是否存在
                if (pd.notna(order_row['sender_lng_decimal']) and pd.notna(order_row['sender_lat_decimal']) and
                    pd.notna(order_row['recipient_lng_decimal']) and pd.notna(order_row['recipient_lat_decimal'])):
                    
                    # 直接读取预期配送时长（已经是分钟为单位的时间段）
                    estimated_delivery_time_value = order_row.get('estimated_delivery_time', None)
                    
                    if pd.notna(estimated_delivery_time_value) and estimated_delivery_time_value > 0:
                        estimated_delivery_time = float(estimated_delivery_time_value)
                    else:
                        continue
                 
                 
                    preparation_time = order_row.get('preparation_time', 0)
                    volume = order_row.get('volume', 1)  # 默认为1

                    self.order_positions[str(consumer_id)] = {
                                'restaurant_lng': order_row['sender_lng_decimal'],
                                'restaurant_lat': order_row['sender_lat_decimal'],
                                'customer_lng': order_row['recipient_lng_decimal'],
                                'customer_lat': order_row['recipient_lat_decimal'],
                                'chosen_time': stp_row['chosen_time'],
                                'chosen_price': stp_row['chosen_price'],
                                'order_value': order_row['price'],
                                'order_id': order_row['order_id'],
                                'estimated_delivery_time': estimated_delivery_time,  # 使用计算出的预期配送时长
                                'preparation_time': preparation_time,
                                'volume': volume
                            } 


        
    
    def _load_oggm_results(self):
        """加载OGGM算法结果"""
        
        with open(self.oggm_results_path, 'r', encoding='utf-8') as f:
            self.oggm_results = json.load(f)
        
    
    def _load_driver_positions(self):
        """加载司机位置数据 - 保持原有设置30个司机"""
        
        df = pd.read_csv(self.orders_data_path)
        grab_positions = df[['grab_lng_decimal', 'grab_lat_decimal']].dropna()
        grab_positions = grab_positions[(grab_positions['grab_lng_decimal'] != 0) & 
                                       (grab_positions['grab_lat_decimal'] != 0)]
        
        # 保持原有设置：30个司机
        if len(grab_positions) < self.num_drivers:
            self.driver_positions = grab_positions.values.tolist()
        else:
            selected_positions = grab_positions.sample(n=self.num_drivers, random_state=42)
            self.driver_positions = selected_positions.values.tolist()
        
    
    def _reconstruct_order_groups(self):
        """重构订单组，补充位置信息和预期配送时长"""
        
        for stp, stp_data in self.oggm_results.items():
            reconstructed_groups = []
            
            for group_detail in stp_data['groups_detail']:
                order_ids = group_detail['orders']  # 这些是consumer_id
                
                # 重构订单组，添加位置信息和预期配送时长
                reconstructed_orders = []
                valid_orders = 0
                
                for consumer_id in order_ids:
                    if consumer_id in self.order_positions:
                        pos_info = self.order_positions[consumer_id]
                        
                        order = Order(
                            order_id=consumer_id,
                            restaurant_lng=pos_info['restaurant_lng'],
                            restaurant_lat=pos_info['restaurant_lat'],
                            customer_lng=pos_info['customer_lng'],
                            customer_lat=pos_info['customer_lat'],
                            chosen_time=pos_info['chosen_time'],
                            chosen_price=pos_info['chosen_price'],
                            order_value=pos_info['order_value'],
                            original_order_id=pos_info['order_id'],
                            estimated_delivery_time=pos_info['estimated_delivery_time'],
                            preparation_time=pos_info['preparation_time'],
                            volume=pos_info['volume']
                        )
                        reconstructed_orders.append(order)
                        valid_orders += 1
                
                if reconstructed_orders:  # 只保留有效的订单组
                     reconstructed_groups.append({
                         'group_id': group_detail['group_id'],
                         'orders': reconstructed_orders,
                         'size': len(reconstructed_orders),
                         'starting_order': group_detail['starting_order'],
                         'sequence': group_detail.get('sequence', [])  # 保留OGGM生成的序列
                     })
            
            self.reconstructed_order_groups[stp] = {
                'total_groups': len(reconstructed_groups),
                'groups_detail': reconstructed_groups
            }
    
    @staticmethod
    def haversine(lon1, lat1, lon2, lat2):
        """计算两点间的地理距离（单位：公里）"""
        lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
        dlon = lon2 - lon1 
        dlat = lat2 - lat1 
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a)) 
        r = 6371
        return c * r
    
    @staticmethod
    def time_to_minutes(time_str):
        """将时间字符串转换为从午夜开始的分钟数"""
        if isinstance(time_str, str):
            hour, minute = map(int, time_str.split(':'))
            return hour * 60 + minute
    
    def _get_sequence_from_order_group(self, order_group, start_pos=None):
        """从订单组信息中提取或获取配送序列 - OGSA不生成新序列，使用OGGM的结果"""
        # OGSA算法不重新生成序列，而是直接使用OGGM已经生成的序列
        # 这与Robust SOCDA 7.py中的逻辑一致
        if not order_group:
            return []
        
        # 如果订单组中包含序列信息，直接使用
        if hasattr(order_group, 'sequence') and order_group.sequence:
            return order_group.sequence
        
        # 如果是字典格式且包含序列
        if isinstance(order_group, dict) and 'sequence' in order_group:
            return order_group['sequence']
        
        # 如果没有序列信息，生成简单的序列（备用方案）
        if isinstance(order_group, dict) and 'orders' in order_group:
            orders = order_group['orders']
        else:
            orders = order_group
        
        # 生成简单序列：先取完所有货，再按最近距离送
        sequence = []
        for order in orders:
            order_id = order.order_id if hasattr(order, 'order_id') else order['order_id']
            sequence.append(f'r_{order_id}')
        
        # 添加送货节点（按最近距离排序）
        remaining_orders = list(orders)
        if remaining_orders:
            # 从最后一个取货点开始
            last_order = remaining_orders[-1]
            if hasattr(last_order, 'restaurant_lng'):
                current_pos = (last_order.restaurant_lng, last_order.restaurant_lat)
            else:
                current_pos = (last_order['restaurant_lng'], last_order['restaurant_lat'])
            
            while remaining_orders:
                min_distance = float('inf')
                nearest_order = None
                
                for order in remaining_orders:
                    if hasattr(order, 'customer_lng'):
                        customer_pos = (order.customer_lng, order.customer_lat)
                        order_id = order.order_id
                    else:
                        customer_pos = (order['customer_lng'], order['customer_lat'])
                        order_id = order['order_id']
                    
                    distance = self.haversine(current_pos[0], current_pos[1], customer_pos[0], customer_pos[1])
                    if distance < min_distance:
                        min_distance = distance
                        nearest_order = order
                
                if nearest_order:
                    if hasattr(nearest_order, 'order_id'):
                        sequence.append(f'c_{nearest_order.order_id}')
                        current_pos = (nearest_order.customer_lng, nearest_order.customer_lat)
                    else:
                        sequence.append(f'c_{nearest_order["order_id"]}')
                        current_pos = (nearest_order['customer_lng'], nearest_order['customer_lat'])
                    remaining_orders.remove(nearest_order)
        
        return sequence
    
    def calculate_dispatching_cost(self, order_group, virtual_driver_lng, virtual_driver_lat, stp_time_minutes):
        """
        计算订单组的配送成本 (旅行成本 + 鲁棒延迟惩罚成本)
        此函数已被重写，以精确匹配鲁棒成本的计算逻辑。
        """
        if not order_group:
            return float('inf'), 0, 0

        # 1. 为订单组生成配送序列
        start_pos = (virtual_driver_lng, virtual_driver_lat)
        sequence = self._get_sequence_from_order_group(order_group, start_pos)
        # 修复：确保 'orders' 键存在
        orders_in_group = order_group['orders'] if isinstance(order_group, dict) and 'orders' in order_group else order_group
        orders_map = {str(order.order_id): order for order in orders_in_group}

        # 2. 构建节点坐标、弧、名义时间和偏差
        nodes_in_sequence = ['start'] + sequence
        coords_in_sequence = [start_pos]
        
        for node_str in sequence:
            node_type, order_id = node_str.split('_', 1)
            order = orders_map[order_id]
            coords_in_sequence.append((order.restaurant_lng, order.restaurant_lat) if node_type == 'r' else (order.customer_lng, order.customer_lat))

        arcs = [(coords_in_sequence[i], coords_in_sequence[i + 1]) for i in range(len(coords_in_sequence) - 1)]
        nominal_times = [((self.haversine(s[0], s[1], e[0], e[1]) / self.rider_speed_kmh) * 60) for s, e in arcs]
        deviations = [t * self.travel_time_deviation_ratio for t in nominal_times]

        # 3. 初始化DP表和不确定性预算
        gamma = math.ceil(self.theta_t * len(arcs)) if arcs else 0
        dp = [[-1.0] * (gamma + 1) for _ in range(len(nodes_in_sequence))]
        dp[0][0] = stp_time_minutes

        # 4. DP状态转移
        for i in range(len(arcs)): # 遍历每条弧段, i 是出发节点索引
            t_nom, dev = nominal_times[i], deviations[i]

            # 检查当前节点i是否为餐厅，并计算备餐完成时间
            node_i_str = nodes_in_sequence[i]
            is_restaurant_pickup = (i > 0 and (node_i_str.startswith('r_') or node_i_str.startswith('pickup_')))
            food_ready_time = -1.0
            if is_restaurant_pickup:
                _, order_id_str = node_i_str.split('_', 1)
                order = orders_map[order_id_str]
                # 使用订单自己的chosen_time计算备餐完成时间
                food_ready_time = self.time_to_minutes(order.chosen_time) + order.preparation_time / 2
            
            for g in range(gamma + 1):
                if dp[i][g] == -1.0: continue
                
                arrival_at_i = dp[i][g]
                departure_at_i = max(arrival_at_i, food_ready_time) if is_restaurant_pickup else arrival_at_i

                # 使用当前弧段的不确定性预算
                if g < gamma:
                    next_arrival_with_dev = departure_at_i + t_nom + dev
                    if dp[i+1][g+1] == -1.0 or next_arrival_with_dev < dp[i+1][g+1]:
                        dp[i+1][g+1] = next_arrival_with_dev

                # 不使用当前弧段的不确定性预算
                next_arrival_no_dev = departure_at_i + t_nom
                if dp[i+1][g] == -1.0 or next_arrival_no_dev < dp[i+1][g]:
                    dp[i+1][g] = next_arrival_no_dev

        # 5. 计算鲁棒旅行成本和惩罚
        worst_finish_time = max((v for v in dp[-1] if v != -1.0), default=stp_time_minutes)
        robust_travel_time = worst_finish_time - stp_time_minutes
        travel_cost = robust_travel_time * self.alpha
        
        penalty_cost = 0.0
        for i, node_str in enumerate(nodes_in_sequence):
            if i == 0: continue
            if node_str.startswith("c_") or node_str.startswith("delivery_"):
                _, order_id = node_str.split('_', 1)
                order = orders_map[order_id]
                deadline = self.time_to_minutes(order.chosen_time) + order.estimated_delivery_time
                
                worst_arrival_at_node = max((v for v in dp[i] if v != -1.0), default=-1.0)
                if worst_arrival_at_node != -1.0:
                    delay = max(worst_arrival_at_node - deadline, 0)
                    penalty_cost += delay * self.beta
        
        total_cost = travel_cost + penalty_cost
        return total_cost, travel_cost, penalty_cost
    
    def get_virtual_driver_info(self, order_group, delta):
        """
        获取订单组的虚拟司机信息
        与OGGM.py中的逻辑完全一致
        """
        if not order_group['orders']:
            return {'lng': 0, 'lat': 0, 'start_time': 0}
        
        # 获取第一个订单的餐厅位置作为参考点（与OGGM.py一致）
        first_order = order_group['orders'][0]
        ref_lng = first_order.restaurant_lng
        ref_lat = first_order.restaurant_lat
        
        # 计算到所有司机位置的距离
        distances = []
        for driver_lng, driver_lat in self.driver_positions:
            dist = self.haversine(driver_lng, driver_lat, ref_lng, ref_lat)
            distances.append((driver_lng, driver_lat, dist))
        
        # 按距离排序，取前delta个最近的司机
        distances.sort(key=lambda x: x[2])
        closest_drivers = distances[:delta]
        
        # 计算平均位置
        avg_lng = np.mean([d[0] for d in closest_drivers])
        avg_lat = np.mean([d[1] for d in closest_drivers])
        
        # 获取STP时间作为起始时间
        stp_time_minutes = self.time_to_minutes(first_order.chosen_time)
        
        return {
            'lng': avg_lng,
            'lat': avg_lat,
            'start_time': stp_time_minutes
        }
    
    def ogsa_algorithm_for_stp(self, stp):
        """为指定STP运行订单组选择算法（OGSA）"""
        
        
        if stp not in self.reconstructed_order_groups:
            return {}
        
        stp_data = self.reconstructed_order_groups[stp]
        all_order_groups = stp_data['groups_detail']
        
        if not all_order_groups:
            return {}
        
        
        # 存储每个δ值对应的选择结果
        delta_results = {}
        
        # Step 2: δ从1到δmax的迭代
        for delta in range(1, self.delta_max + 1):
            
            # Step 1: 初始化
            selected_order_groups = []  # Fδ
            remaining_order_groups = all_order_groups.copy()  # B̄
            covered_orders = set()
            
            # Step 3 & 4: 为每个订单组计算虚拟司机信息和配送成本
            order_group_evaluations = {}
            
            
            # 成本统计
            total_travel_costs = []
            total_penalty_costs = []
            zero_delay_count = 0
            positive_delay_count = 0
            
            for i, og in enumerate(remaining_order_groups):
                # Step 3: 获取虚拟司机信息，使用delta个最近司机
                virtual_driver_info = self.get_virtual_driver_info(og, delta)
                
                # Step 4: 计算配送成本（旅行成本 + 延迟惩罚成本）
                total_cost, travel_cost, penalty_cost = self.calculate_dispatching_cost(
                    og['orders'], 
                    virtual_driver_info['lng'], 
                    virtual_driver_info['lat'],
                    virtual_driver_info['start_time']
                )
                
                total_travel_costs.append(travel_cost)
                total_penalty_costs.append(penalty_cost)
                
                if penalty_cost == 0:
                    zero_delay_count += 1
                else:
                    positive_delay_count += 1
                
                # 计算f值（根据公式24）
                og_size = len(og['orders'])
                f_value = total_cost / (og_size ** self.epsilon)
                
                order_group_evaluations[i] = {
                    'order_group': og,
                    'cost': total_cost,
                    'travel_cost': travel_cost,
                    'penalty_cost': penalty_cost,
                    'size': og_size,
                    'f_value': f_value,
                    'virtual_driver_info': virtual_driver_info
                }
                
            
            # 输出成本统计
            avg_travel_cost = np.mean(total_travel_costs)
            avg_penalty_cost = np.mean(total_penalty_costs)
            max_penalty_cost = np.max(total_penalty_costs)
            
            
            # Step 5-7: 集合覆盖迭代
            iteration = 0
            while remaining_order_groups and order_group_evaluations:
                iteration += 1
                # Step 6: 找到f值最小的订单组
                best_og_idx = min(order_group_evaluations.keys(), 
                                key=lambda x: order_group_evaluations[x]['f_value'])
                
                best_evaluation = order_group_evaluations[best_og_idx]
                selected_og = best_evaluation['order_group']
                
                
                # 添加到已选择集合
                selected_order_groups.append({
                    'order_group': selected_og,
                    'evaluation': best_evaluation
                })
                
                # 记录覆盖的订单
                selected_order_ids = set(order.order_id for order in selected_og['orders'])
                covered_orders.update(selected_order_ids)
                
                
                # Step 7: 移除有重叠的订单组
                indices_to_remove = []
                
                for idx, evaluation in order_group_evaluations.items():
                    og_order_ids = set(order.order_id for order in evaluation['order_group']['orders'])
                    if og_order_ids & selected_order_ids:  # 有交集
                        indices_to_remove.append(idx)
                
                # 移除重叠的订单组
                for idx in indices_to_remove:
                    del order_group_evaluations[idx]
                
                # 更新剩余订单组列表
                remaining_order_groups = [evaluation['order_group'] 
                                        for evaluation in order_group_evaluations.values()]
                
                
                # Step 5: 检查终止条件
                if not remaining_order_groups:
                    break
            
            # 计算当前δ值的结果
            total_cost = sum(item['evaluation']['cost'] for item in selected_order_groups)
            total_travel_cost = sum(item['evaluation']['travel_cost'] for item in selected_order_groups)
            total_penalty_cost = sum(item['evaluation']['penalty_cost'] for item in selected_order_groups)
            total_covered_orders = len(covered_orders)
            
            delta_results[delta] = {
                'selected_order_groups': selected_order_groups,
                'covered_orders': covered_orders,
                'total_cost': total_cost,
                'total_travel_cost': total_travel_cost,
                'total_penalty_cost': total_penalty_cost,
                'total_covered_orders': total_covered_orders
            }
            
            
            # 验证结果合理性
            if total_covered_orders > 0 and total_penalty_cost > 0:
                delay_percentage = (total_penalty_cost / total_cost) * 100
        # Step 2: 选择最优的δ值结果（总成本最低）
        best_delta = min(delta_results.keys(), 
                        key=lambda d: delta_results[d]['total_cost'])
        
        best_result = delta_results[best_delta]
        
        
        return {
            'stp': stp,
            'best_delta': best_delta,
            'all_delta_results': delta_results,
            'best_result': best_result
        }
    
    def run_ogsa_for_all_stps(self, output_filename):
        """为所有STP运行OGSA算法"""
        
        # 为每个STP运行OGSA（完整模式：处理所有STP）
        all_ogsa_results = {}
        processed_count = 0
        
        for stp in sorted(self.reconstructed_order_groups.keys()):
            if self.reconstructed_order_groups[stp]['total_groups'] > 0:
                processed_count += 1
                
                ogsa_result = self.ogsa_algorithm_for_stp(stp)
                all_ogsa_results[stp] = ogsa_result
        
        # 保存结果
        temp_filename = self._save_results(all_ogsa_results, output_filename)
        
        return all_ogsa_results, temp_filename
    
    def _convert_to_json_serializable(self, obj):
        """统一的JSON序列化转换函数"""
        import numpy as np
        
        # 处理numpy数值类型
        if isinstance(obj, (np.integer, np.int64, np.int32, np.int16, np.int8)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64, np.float32, np.float16)):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, set):
            return list(obj)
        elif isinstance(obj, dict):
            return {k: self._convert_to_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_to_json_serializable(item) for item in obj]
        elif isinstance(obj, tuple):
            return [self._convert_to_json_serializable(item) for item in obj]
        elif isinstance(obj, Order):
            return {
                'order_id': self._convert_to_json_serializable(obj.order_id),
                'restaurant_lng': self._convert_to_json_serializable(obj.restaurant_lng),
                'restaurant_lat': self._convert_to_json_serializable(obj.restaurant_lat),
                'customer_lng': self._convert_to_json_serializable(obj.customer_lng),
                'customer_lat': self._convert_to_json_serializable(obj.customer_lat),
                'chosen_time': self._convert_to_json_serializable(obj.chosen_time),
                'chosen_price': self._convert_to_json_serializable(obj.chosen_price),
                'order_value': self._convert_to_json_serializable(obj.order_value),
                'original_order_id': self._convert_to_json_serializable(obj.original_order_id),
                'estimated_delivery_time': self._convert_to_json_serializable(obj.estimated_delivery_time)
            }
        # 处理pandas和numpy的特殊数值类型
        elif hasattr(obj, 'dtype') and hasattr(obj, 'item'):
            # pandas Series中的numpy标量
            return obj.item()
        elif str(type(obj)).startswith("<class 'numpy."):
            # 其他numpy类型，尝试转换为Python原生类型
            return obj.item()
        else:
            return obj

    def _save_results(self, all_ogsa_results, filename=None):
        """保存OGSA算法结果到文件"""
        
        # 序列化结果，处理集合类型和numpy类型
        def serialize_ogsa_results(results):
            serialized = {}
            for stp, stp_result in results.items():
                serialized_stp = {
                    'stp': self._convert_to_json_serializable(stp_result['stp']),
                    'best_delta': self._convert_to_json_serializable(stp_result['best_delta']),
                    'best_result': {
                        'total_cost': self._convert_to_json_serializable(stp_result['best_result']['total_cost']),
                        'total_travel_cost': self._convert_to_json_serializable(stp_result['best_result']['total_travel_cost']),
                        'total_penalty_cost': self._convert_to_json_serializable(stp_result['best_result']['total_penalty_cost']),
                        'total_covered_orders': self._convert_to_json_serializable(stp_result['best_result']['total_covered_orders']),
                        'covered_orders': self._convert_to_json_serializable(list(stp_result['best_result']['covered_orders'])),
                        'selected_order_groups': []
                    },
                    'all_delta_results': {}
                }
                
                # 处理选中的订单组
                for group_info in stp_result['best_result']['selected_order_groups']:
                    group_data = {
                        'group_id': self._convert_to_json_serializable(group_info['order_group']['group_id']),
                        'size': self._convert_to_json_serializable(group_info['order_group']['size']),
                        'starting_order': self._convert_to_json_serializable(group_info['order_group']['starting_order']),
                        'orders': self._convert_to_json_serializable(group_info['order_group']['orders']),
                        'evaluation': {
                            'cost': self._convert_to_json_serializable(group_info['evaluation']['cost']),
                            'travel_cost': self._convert_to_json_serializable(group_info['evaluation']['travel_cost']),
                            'penalty_cost': self._convert_to_json_serializable(group_info['evaluation']['penalty_cost']),
                            'f_value': self._convert_to_json_serializable(group_info['evaluation']['f_value']),
                            'virtual_driver_info': self._convert_to_json_serializable(group_info['evaluation']['virtual_driver_info'])
                        }
                    }
                    serialized_stp['best_result']['selected_order_groups'].append(group_data)
                
                # 处理所有delta结果
                for delta, delta_result in stp_result['all_delta_results'].items():
                    serialized_stp['all_delta_results'][str(delta)] = {
                        'total_cost': self._convert_to_json_serializable(delta_result['total_cost']),
                        'total_travel_cost': self._convert_to_json_serializable(delta_result['total_travel_cost']),
                        'total_penalty_cost': self._convert_to_json_serializable(delta_result['total_penalty_cost']),
                        'total_covered_orders': self._convert_to_json_serializable(delta_result['total_covered_orders']),
                        'covered_orders': self._convert_to_json_serializable(list(delta_result['covered_orders'])),
                        'selected_groups_count': self._convert_to_json_serializable(len(delta_result['selected_order_groups']))
                    }
                
                serialized[stp] = serialized_stp
            
            return serialized
        
        # 序列化结果
        serialized_results = serialize_ogsa_results(all_ogsa_results)
        
        # 保存详细结果
        if filename:
            # 如果提供了文件名，则使用它
            output_path = filename
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(serialized_results, f, ensure_ascii=False, indent=2)
        else:
            # 否则，创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json', encoding='utf-8') as f:
                output_path = f.name
                json.dump(serialized_results, f, ensure_ascii=False, indent=2)

        return output_path
        
    
    def get_algorithms_data(self):
        """获取算法所需的基础数据，供其他算法使用"""
        return {
            'order_positions': self.order_positions,
            'oggm_results': self.oggm_results,
            'driver_positions': self.driver_positions,
            'reconstructed_order_groups': self.reconstructed_order_groups
        }
