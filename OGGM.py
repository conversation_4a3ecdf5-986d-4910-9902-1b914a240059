import pandas as pd
import numpy as np
import random
import json
from math import radians, cos, sin, asin, sqrt

class Order:
    """订单类"""
    def __init__(self, order_id, restaurant_lng, restaurant_lat, customer_lng, customer_lat, 
                 stp_time, estimated_delivery_time, order_value, platform_order_time, preparation_time, volume):
        self.order_id = order_id
        self.restaurant_lng = restaurant_lng
        self.restaurant_lat = restaurant_lat
        self.customer_lng = customer_lng
        self.customer_lat = customer_lat
        self.stp_time = stp_time
        self.estimated_delivery_time = estimated_delivery_time  # 预期配送时长（关键字段）
        self.order_value = order_value
        self.platform_order_time = platform_order_time
        self.preparation_time = preparation_time  # 餐厅准备时间（分钟）
        self.volume = volume  # 订单体积

class OGGMAlgorithm:
    """订单组生成算法（OGGM）"""
    
    def __init__(self, stp_choices_path, orders_data_path='meituan_orders_with_delivery_time.csv', num_drivers=30, driver_capacity=50):
        """
        初始化OGGM算法
        
        Args:
            stp_choices_path: 消费者STP选择结果文件路径
            orders_data_path: 订单数据文件路径
            num_drivers: 司机数量
            driver_capacity: 车辆容量
        """
        self.orders_data_path = orders_data_path
        self.stp_choices_path = stp_choices_path
        self.driver_positions = None
        self.order_data = None
        self.stp_data = None
        self.merged_orders = None
        
        # 算法参数
        self.delta_bar = 2         # 用于计算虚拟骑手的骑手数量
        self.mu = 0.2              # 订单延迟度阈值
        self.driver_capacity = driver_capacity # 每个骑手的最大容量
        self.rider_speed_kmh = 20  # 骑手速度
        
        self.num_drivers = num_drivers
        
        # 初始化数据
        self._load_data()
        
    def _load_data(self):
        """加载并预处理数据"""
        
        try:
            # 1. 读取订单数据
            self.order_data = pd.read_csv(self.orders_data_path)
            
            # 2. 读取STP选择数据
            self.stp_data = pd.read_csv(self.stp_choices_path)
            self.stp_data = self.stp_data[self.stp_data['choice_type'] == 'purchase']
            
            # 3. 生成司机初始位置
            self._generate_driver_positions()
            
            # 4. 合并数据并创建订单对象
            self._merge_and_create_orders()
            
        except Exception as e:
            raise
    
    def _generate_driver_positions(self):
        """生成司机初始位置 - 从grab位置中随机选择指定数量"""
        grab_positions = self.order_data[['grab_lng_decimal', 'grab_lat_decimal']].dropna()
        grab_positions = grab_positions[(grab_positions['grab_lng_decimal'] != 0) & 
                                       (grab_positions['grab_lat_decimal'] != 0)]
        
        if len(grab_positions) < self.num_drivers:
            self.driver_positions = grab_positions.values.tolist()
        else:
            selected_positions = grab_positions.sample(n=self.num_drivers, random_state=42)
            self.driver_positions = selected_positions.values.tolist()
        
    
    def _merge_and_create_orders(self):
        """合并数据并创建订单对象"""
        merged_data = []
        skipped_count = 0
        
        for _, stp_row in self.stp_data.iterrows():
            consumer_id = stp_row['consumer_id']
            
            # consumer_id对应订单数据的行号（从1开始）
            if consumer_id <= len(self.order_data):
                order_row = self.order_data.iloc[consumer_id - 1]
                
                # 检查必要字段
                if (pd.isna(order_row['sender_lng_decimal']) or pd.isna(order_row['sender_lat_decimal']) or
                    pd.isna(order_row['recipient_lng_decimal']) or pd.isna(order_row['recipient_lat_decimal'])):
                    skipped_count += 1
                    continue
                
                # 直接读取预期配送时长（已经是分钟为单位的时间段）
                estimated_delivery_time_value = order_row.get('estimated_delivery_time', None)
                
                if pd.notna(estimated_delivery_time_value) and estimated_delivery_time_value > 0:
                    estimated_delivery_time = float(estimated_delivery_time_value)
                else:
                    continue
                
                preparation_time = order_row.get('preparation_time', 0)  # 数据单位已经是分钟
                volume = order_row.get('volume', 1)

                order = Order(
                    order_id=str(consumer_id),
                    restaurant_lng=order_row['sender_lng_decimal'],
                    restaurant_lat=order_row['sender_lat_decimal'],
                    customer_lng=order_row['recipient_lng_decimal'],
                    customer_lat=order_row['recipient_lat_decimal'],
                    stp_time=stp_row['chosen_time'],
                    estimated_delivery_time=estimated_delivery_time,  # 使用正确的预期配送时长
                    order_value=order_row['price'],
                    platform_order_time=order_row['platform_order_time'],
                    preparation_time=preparation_time,
                    volume=volume
                )
                merged_data.append(order)
        
        self.merged_orders = merged_data
    
    @staticmethod
    def haversine(lon1, lat1, lon2, lat2):
        """计算两点间的地理距离（单位：公里）"""
        lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
        dlon = lon2 - lon1 
        dlat = lat2 - lat1 
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a)) 
        r = 6371  # 地球平均半径，单位为公里
        return c * r
    
    @staticmethod
    def time_to_minutes(time_str):
        """将时间字符串转换为从午夜开始的分钟数"""
        if isinstance(time_str, str):
            hour, minute = map(int, time_str.split(':'))
            return hour * 60 + minute
    
    def calculate_single_order_delivery_time(self, order, driver_lng, driver_lat):
        """计算单个订单的理论配送时长（分钟）- 仅用于匹配度计算"""
        # 骑手到餐厅距离
        to_restaurant = self.haversine(driver_lng, driver_lat, order.restaurant_lng, order.restaurant_lat)
        # 餐厅到客户距离
        to_customer = self.haversine(order.restaurant_lng, order.restaurant_lat, 
                                   order.customer_lng, order.customer_lat)
        
        # 总距离转换为时间
        total_distance = to_restaurant + to_customer
        travel_time = (total_distance / self.rider_speed_kmh) * 60  # 转换为分钟
        
        return travel_time
    
    def simulate_delivery_sequence(self, order_group, virtual_driver_lng, virtual_driver_lat, start_time):
        """模拟订单组的配送过程，计算每个订单的实际送达时间 - 使用灵活的pickup/delivery序列和绝对时间"""
        if not order_group:
            return {}
        
        optimal_sequence = self.generate_optimal_delivery_sequence(order_group, virtual_driver_lng, virtual_driver_lat)
        if not optimal_sequence:
            # 如果无法生成可行序列（如超容），返回空字典
            return {}

        current_lng, current_lat = virtual_driver_lng, virtual_driver_lat
        current_time = start_time # 使用绝对开始时间
        delivery_times = {}
        orders_map = {o.order_id: o for o in order_group}

        for node in optimal_sequence:
            node_type, order_id = node.split('_', 1)
            order = orders_map[order_id]
            
            if node_type == 'pickup':
                target_lng, target_lat = order.restaurant_lng, order.restaurant_lat
                travel_time = (self.haversine(current_lng, current_lat, target_lng, target_lat) / self.rider_speed_kmh) * 60
                
                arrival_at_restaurant = current_time + travel_time
                food_ready_time = self.time_to_minutes(order.stp_time) + order.preparation_time / 2
                
                # 正确的等待逻辑：出发时间 = max(到达时间, 备好时间)
                current_time = max(arrival_at_restaurant, food_ready_time)
                current_lng, current_lat = target_lng, target_lat
                
            elif node_type == 'delivery':
                target_lng, target_lat = order.customer_lng, order.customer_lat
                travel_time = (self.haversine(current_lng, current_lat, target_lng, target_lat) / self.rider_speed_kmh) * 60
                
                current_time += travel_time
                delivery_times[order.order_id] = current_time # 记录绝对送达时间
                current_lng, current_lat = target_lng, target_lat
                
        return delivery_times
    
    def generate_optimal_delivery_sequence(self, order_group, start_lng, start_lat):
        """
        使用最便宜插入法生成最优的配送序列。
        如果因容量等约束无法生成包含所有订单的序列，则返回 None。
        """
        if not order_group:
            return []
        
        # 检查单个订单容量
        if len(order_group) == 1:
            order = order_group[0]
            if getattr(order, 'volume', 1) > self.driver_capacity:
                return None
            return [f'pickup_{order.order_id}', f'delivery_{order.order_id}']
        
        # 从第一个订单开始
        first_order = order_group[0]
        if getattr(first_order, 'volume', 1) > self.driver_capacity:
            return None

        sequence = [f'pickup_{first_order.order_id}', f'delivery_{first_order.order_id}']
        
        # 逐个插入剩余订单
        for i, order in enumerate(order_group[1:]):
            current_order_group_in_sequence = order_group[:i+1]
            new_sequence = self.cheapest_insertion(sequence, order, start_lng, start_lat, current_order_group_in_sequence)
            
            if new_sequence is None:
                # 如果插入失败（例如，由于容量限制），则无法为此订单组生成可行序列
                return None
            
            sequence = new_sequence
        
        return sequence

    def cheapest_insertion(self, sequence, new_order, start_lng, start_lat, order_group):
        """将新订单插入到现有序列中的最优位置, 如果不可行则返回 None"""
        pickup_node = f'pickup_{new_order.order_id}'
        delivery_node = f'delivery_{new_order.order_id}'
        
        best_sequence = None
        best_cost = float('inf')

        # 传入的order_group已经包含了new_order的前一个状态，这里要用完整的组进行检查
        full_order_group_for_check = order_group + [new_order]
        
        # 尝试所有可能的插入位置
        for pickup_pos in range(len(sequence) + 1):
            for delivery_pos in range(pickup_pos + 1, len(sequence) + 2):
                temp_sequence = sequence[:]
                temp_sequence.insert(pickup_pos, pickup_node)
                temp_sequence.insert(delivery_pos, delivery_node)
                
                # 使用修正后的容量检查逻辑
                if self.is_sequence_capacity_feasible(temp_sequence, full_order_group_for_check):
                    cost = self.calculate_sequence_cost(temp_sequence, start_lng, start_lat, full_order_group_for_check)
                    if cost < best_cost:
                        best_cost = cost
                        best_sequence = temp_sequence
        
        return best_sequence # 如果没有找到可行插入，将返回None

    def is_sequence_capacity_feasible(self, sequence, order_group):
        """检查配送序列是否满足容量约束（使用订单实际体积）"""
        current_volume = 0
        max_volume = 0
        order_map = {order.order_id: order for order in order_group}
        
        for node in sequence:
            node_type, order_id = node.split('_', 1)
            
            if order_id in order_map:
                order = order_map[order_id]
                if node_type == 'pickup':
                    current_volume += getattr(order, 'volume', 1)
                elif node_type == 'delivery':
                    current_volume -= getattr(order, 'volume', 1)
                max_volume = max(max_volume, current_volume)
        
        return max_volume <= self.driver_capacity

    def calculate_sequence_cost(self, sequence, start_lng, start_lat, order_group):
        """计算配送序列的总成本（距离）"""
        if not sequence:
            return 0
        
        # 如果没有提供订单组，使用merged_orders
        if order_group is None:
            order_group = self.merged_orders
        
        # 创建订单ID到订单对象的映射
        order_map = {order.order_id: order for order in order_group}
        
        total_distance = 0
        current_lng = start_lng
        current_lat = start_lat
        
        for node in sequence:
            node_type, order_id = node.split('_')
            
            # 查找对应的订单
            if order_id not in order_map:
                continue
                
            order = order_map[order_id]
            
            # 根据节点类型确定目标位置
            if node_type == 'pickup':
                target_lng = order.restaurant_lng
                target_lat = order.restaurant_lat
            else:  # delivery
                target_lng = order.customer_lng
                target_lat = order.customer_lat
            
            distance = self.haversine(current_lng, current_lat, target_lng, target_lat)
            total_distance += distance
            current_lng, current_lat = target_lng, target_lat
        
        return total_distance
    
    def calculate_individual_distance(self, order, driver_lng, driver_lat):
        """计算单独配送一个订单的总距离"""
        # 骑手到餐厅的距离
        to_restaurant = self.haversine(driver_lng, driver_lat, 
                                     order.restaurant_lng, order.restaurant_lat)
        # 餐厅到客户的距离  
        to_customer = self.haversine(order.restaurant_lng, order.restaurant_lat,
                                   order.customer_lng, order.customer_lat)
        return to_restaurant + to_customer
    
    def calculate_consolidation_distance(self, order1, order2, driver_lng, driver_lat):
        """计算两个订单合并配送的最短距离"""
        # 三种可能的配送序列
        # 路径1: 骑手 -> 餐厅1 -> 餐厅2 -> 客户2 -> 客户1
        path1 = (self.haversine(driver_lng, driver_lat, order1.restaurant_lng, order1.restaurant_lat) +
                 self.haversine(order1.restaurant_lng, order1.restaurant_lat, order2.restaurant_lng, order2.restaurant_lat) +
                 self.haversine(order2.restaurant_lng, order2.restaurant_lat, order2.customer_lng, order2.customer_lat) +
                 self.haversine(order2.customer_lng, order2.customer_lat, order1.customer_lng, order1.customer_lat))
        
        # 路径2: 骑手 -> 餐厅1 -> 餐厅2 -> 客户1 -> 客户2
        path2 = (self.haversine(driver_lng, driver_lat, order1.restaurant_lng, order1.restaurant_lat) +
                 self.haversine(order1.restaurant_lng, order1.restaurant_lat, order2.restaurant_lng, order2.restaurant_lat) +
                 self.haversine(order2.restaurant_lng, order2.restaurant_lat, order1.customer_lng, order1.customer_lat) +
                 self.haversine(order1.customer_lng, order1.customer_lat, order2.customer_lng, order2.customer_lat))
        
        # 路径3: 骑手 -> 餐厅1 -> 客户1 -> 餐厅2 -> 客户2
        path3 = (self.haversine(driver_lng, driver_lat, order1.restaurant_lng, order1.restaurant_lat) +
                 self.haversine(order1.restaurant_lng, order1.restaurant_lat, order1.customer_lng, order1.customer_lat) +
                 self.haversine(order1.customer_lng, order1.customer_lat, order2.restaurant_lng, order2.restaurant_lat) +
                 self.haversine(order2.restaurant_lng, order2.restaurant_lat, order2.customer_lng, order2.customer_lat))
        
        return min(path1, path2, path3)
    
    def calculate_order_matching_degree(self, order1, order2, vd1_lng, vd1_lat, vd2_lng, vd2_lat):
        """计算两个订单的匹配度"""
        # 计算分别配送的总距离
        individual_dist1 = self.calculate_individual_distance(order1, vd1_lng, vd1_lat)
        individual_dist2 = self.calculate_individual_distance(order2, vd2_lng, vd2_lat)
        total_individual = individual_dist1 + individual_dist2
        
        # 计算以order1为起始的合并距离
        consolidation_dist1 = self.calculate_consolidation_distance(order1, order2, vd1_lng, vd1_lat)
        fit_o1_o2 = total_individual - consolidation_dist1
        
        # 计算以order2为起始的合并距离  
        consolidation_dist2 = self.calculate_consolidation_distance(order2, order1, vd2_lng, vd2_lat)
        fit_o2_o1 = total_individual - consolidation_dist2
        
        # 返回更好的匹配度
        return max(fit_o1_o2, fit_o2_o1)
    
    def calculate_og_order_matching_degree(self, order_group_orders, new_order, virtual_driver_lng, virtual_driver_lat):
        """计算订单组与单个订单的匹配度"""
        total_matching = 0
        for existing_order in order_group_orders:
            matching = self.calculate_order_matching_degree(
                existing_order, new_order, 
                virtual_driver_lng, virtual_driver_lat,
                virtual_driver_lng, virtual_driver_lat
            )
            total_matching += matching
        
        return total_matching / len(order_group_orders)
    
    def _get_sequence_from_order_group(self, order_group, virtual_driver_lng, virtual_driver_lat):
        """使用最便宜插入法生成灵活的配送序列（支持pickup/delivery混合）"""
        if not order_group:
            return []

        # 使用新的序列生成方法
        optimal_sequence = self.generate_optimal_delivery_sequence(order_group, virtual_driver_lng, virtual_driver_lat)
        
        if optimal_sequence is None:
            return None
        
        # 转换格式以保持兼容性（pickup_X -> r_X, delivery_X -> c_X）
        converted_sequence = []
        for node in optimal_sequence:
            if node.startswith('pickup_'):
                order_id = node.split('_')[1]
                converted_sequence.append(f'r_{order_id}')
            elif node.startswith('delivery_'):
                order_id = node.split('_')[1]
                converted_sequence.append(f'c_{order_id}')
        
        return converted_sequence

    def _get_node_coords(self, node_str, orders_map):
        """辅助函数：从节点字符串获取坐标"""
        node_type, order_id = node_str.split('_')
        order = orders_map[order_id]
        if node_type == 'r':
            return order.restaurant_lng, order.restaurant_lat
        else:
            return order.customer_lng, order.customer_lat

    def _calculate_sequence_distance(self, sequence, start_pos, orders_map):
        """辅助函数：计算一个给定序列的总距离"""
        if not sequence:
            return 0.0
        
        total_distance = 0.0
        current_pos = start_pos
        
        for node_str in sequence:
            next_pos = self._get_node_coords(node_str, orders_map)
            total_distance += self.haversine(current_pos[0], current_pos[1], next_pos[0], next_pos[1])
            current_pos = next_pos
            
        return total_distance

    def _calculate_cheapest_insertion_j4(self, base_sequence, new_order, start_pos, orders_map):
        """
        根据论文J.4的最便宜插入法，计算新序列和距离 (d_star)
        规则: 先用最便宜插入法插入餐厅点，然后将顾客点插入到紧随其后的位置
        """
        pickup_node = f'r_{new_order.order_id}'
        delivery_node = f'c_{new_order.order_id}'
        
        best_sequence = []
        min_dist = float('inf')

        # 遍历所有可能的取餐点插入位置
        for i in range(len(base_sequence) + 1):
            # 插入取餐点，然后紧接着插入送餐点
            temp_sequence = base_sequence[:i] + [pickup_node, delivery_node] + base_sequence[i:]
            
            # 计算新序列的总距离
            dist = self._calculate_sequence_distance(temp_sequence, start_pos, orders_map)
            
            if dist < min_dist:
                min_dist = dist
                best_sequence = temp_sequence
        
        return best_sequence, min_dist

    def calculate_order_group_matching_degree_j4(self, order_group, new_order, virtual_driver_pos, orders_map):
        """
        根据论文公式J.4计算订单组与新订单的匹配度
        fit(B_j, o_i) = d_bar(B_j) + d_hat(o_i) - d_star(B_j, o_i)
        """
        # d_hat(o_i): 单独配送新订单的距离
        d_hat = self.calculate_individual_distance(new_order, virtual_driver_pos[0], virtual_driver_pos[1])

        # 获取当前订单组的路线序列
        base_sequence = self._get_sequence_from_order_group(order_group, virtual_driver_pos[0], virtual_driver_pos[1])
        if base_sequence is None: return -float('inf')

        # d_bar(B_j): 单独配送当前订单组的距离
        d_bar = self._calculate_sequence_distance(base_sequence, virtual_driver_pos, orders_map)
        
        # d_star(B_j, o_i): 合并后的最短距离
        merged_order_group = order_group + [new_order]
        merged_orders_map = orders_map.copy()
        merged_orders_map[new_order.order_id] = new_order
        
        merged_sequence = self._get_sequence_from_order_group(merged_order_group, virtual_driver_pos[0], virtual_driver_pos[1])
        if merged_sequence is None: return -float('inf')

        d_star = self._calculate_sequence_distance(merged_sequence, virtual_driver_pos, merged_orders_map)
        
        # fit是节约的距离
        fit = d_bar + d_hat - d_star
        return fit

    
    def calculate_max_delay_degree_for_group(self, order_group, virtual_driver_lng, virtual_driver_lat, start_time):
        """计算订单组中所有订单的最大延迟度"""
        max_delay_degree = 0
        
        # 通过一次模拟获得所有订单的送达时间
        delivery_times = self.simulate_delivery_sequence(order_group, virtual_driver_lng, virtual_driver_lat, start_time)
        
        # 如果模拟失败（例如，不可行），则认为延迟度无限大
        if not delivery_times:
            return float('inf')

        for order in order_group:
            actual_delivery_time = delivery_times.get(order.order_id)
            if actual_delivery_time is None: continue

            # 从STP开始计算的实际配送用时
            actual_duration = actual_delivery_time - self.time_to_minutes(order.stp_time)
            
            expected_duration = order.estimated_delivery_time
            
            if expected_duration > 0:
                delay = max(actual_duration - expected_duration, 0)
                delay_degree = delay / expected_duration
                max_delay_degree = max(max_delay_degree, delay_degree)
        
        return max_delay_degree

    def _is_sequence_capacity_feasible(self, sequence, orders_map):
        """检查一个给定的配送序列是否满足车辆容量约束"""
        current_volume = 0
        max_volume = 0
        for node_str in sequence:
            node_type, order_id = node_str.split('_', 1)
            if order_id in orders_map:
                order = orders_map[order_id]
                if node_type == 'r':
                    current_volume += getattr(order, 'volume', 1)
                else: # 'c'
                    current_volume -= getattr(order, 'volume', 1)
                max_volume = max(max_volume, current_volume)
        
        return max_volume <= self.driver_capacity

    def create_virtual_drivers(self, orders_at_stp):
        """为每个订单创建虚拟骑手"""
        virtual_drivers_info = {}
        
        for order in orders_at_stp:
            # 计算到所有骑手位置的距离
            distances = []
            for driver_lng, driver_lat in self.driver_positions:
                dist = self.haversine(driver_lng, driver_lat, order.restaurant_lng, order.restaurant_lat)
                distances.append((driver_lng, driver_lat, dist))
            
            # 按距离排序，取前delta_bar个
            distances.sort(key=lambda x: x[2])
            closest_drivers = distances[:self.delta_bar]
            
            # 计算平均位置
            avg_lng = np.mean([d[0] for d in closest_drivers])
            avg_lat = np.mean([d[1] for d in closest_drivers])
            
            # 设置虚拟骑手的起始时间（当前STP时间）
            stp_time_minutes = self.time_to_minutes(order.stp_time)
            
            virtual_drivers_info[order.order_id] = (avg_lng, avg_lat, stp_time_minutes)
        
        return virtual_drivers_info
    
    def oggm_algorithm(self, orders_at_stp):
        """订单组生成算法（OGGM）核心实现"""
        
        if not orders_at_stp:
            return []
        
        
        # Step 1: 初始化
        all_order_groups = []
        num_orders = len(orders_at_stp)
        
        # 将订单列表转换为ID映射，方便查询
        orders_map = {order.order_id: order for order in orders_at_stp}
        
        # Step 2: 为每个订单确定相关虚拟骑手
        virtual_drivers_info = self.create_virtual_drivers(orders_at_stp)
        
        # Step 3: 计算订单匹配度矩阵
        matching_matrix = {}
        for i in range(num_orders):
            for j in range(num_orders):
                if i != j:
                    order1 = orders_at_stp[i]
                    order2 = orders_at_stp[j]
                    vd1_lng, vd1_lat, _ = virtual_drivers_info[order1.order_id]
                    vd2_lng, vd2_lat, _ = virtual_drivers_info[order2.order_id]
                    
                    try:
                        matching_degree = self.calculate_order_matching_degree(
                            order1, order2, vd1_lng, vd1_lat, vd2_lng, vd2_lat
                        )
                        matching_matrix[(i, j)] = matching_degree
                    except Exception as e:
                        raise

        
        
        # Step 4-8: 对每个订单作为起始订单生成订单组
        for start_idx, starting_order in enumerate(orders_at_stp):
            
            # 关键修复：检查单个订单是否就超过容量
            if getattr(starting_order, 'volume', 1) > self.driver_capacity:
                continue

            # 初始化当前订单簇和订单组集合
            current_cluster = [starting_order]
            order_groups_for_start = []
            
            # 添加初始订单组（只包含起始订单）
            order_groups_for_start.append(current_cluster.copy())
            
            # 剩余可选订单索引
            remaining_indices = [i for i in range(num_orders) if i != start_idx]
            
            # 获取虚拟骑手信息 (使用起始订单的虚拟骑手作为该组的代表)
            vd_lng, vd_lat, vd_start_time = virtual_drivers_info[starting_order.order_id]
            virtual_driver_pos = (vd_lng, vd_lat)
            
            # Step 6: 检查是否达到容量上限或没有剩余订单
            iteration = 0
            while remaining_indices:
                iteration += 1
                
                # Step 5: 计算与剩余订单的匹配度并排序 (使用新的J.4逻辑)
                order_priorities = []
                
                for remain_idx in remaining_indices:
                    remain_order = orders_at_stp[remain_idx]
                    # 计算与当前订单组的匹配度
                    matching_degree = self.calculate_order_group_matching_degree_j4(
                        current_cluster, remain_order, virtual_driver_pos, orders_map
                    )
                    order_priorities.append((remain_idx, remain_order, matching_degree))
                
                # 按匹配度降序排序
                order_priorities.sort(key=lambda x: x[2], reverse=True)
                
                
                # Step 7: 检查终止条件
                if not order_priorities or order_priorities[0][2] <= 0:
                    break
                
                # 选择匹配度最高的订单
                best_idx, best_order, best_matching = order_priorities[0]
                
                # 检查延迟度约束
                test_cluster = current_cluster + [best_order]
                
                # 关键修复：检查容量约束和路径生成
                test_sequence = self._get_sequence_from_order_group(test_cluster, vd_lng, vd_lat)
                if not test_sequence:
                    # 如果无法生成可行序列（通常是容量问题），则跳过
                    remaining_indices.remove(best_idx)
                    continue

                max_delay_degree = self.calculate_max_delay_degree_for_group(test_cluster, vd_lng, vd_lat, vd_start_time)
                
                if max_delay_degree > self.mu:
                    # 如果延迟超限，则不再尝试将此订单加入，并从候选中移除
                    remaining_indices.remove(best_idx)
                    continue
                
                # Step 8: 合并订单，更新相关数据结构
                current_cluster.append(best_order)
                remaining_indices.remove(best_idx)
                
                # 添加新的订单组
                order_groups_for_start.append(current_cluster.copy())
                
                
            
            # 将所有生成的订单组添加到最终结果中
            all_order_groups.extend(order_groups_for_start)
        
        size_distribution = {}
        for og in all_order_groups:
            size = len(og)
            size_distribution[size] = size_distribution.get(size, 0) + 1
        return all_order_groups
    
    def run_oggm_for_stp(self, stp_time):
        """为指定STP运行OGGM算法"""
        
        # 筛选指定STP的订单
        orders_at_stp = [order for order in self.merged_orders if order.stp_time == stp_time]
        
        
        # 显示订单的预期配送时长分布
        delivery_times = []
        for order in orders_at_stp:
            # 确保是数字类型
            delivery_time = float(order.estimated_delivery_time)
            delivery_times.append(delivery_time)
        
        
        # 运行OGGM算法
        order_groups = self.oggm_algorithm(orders_at_stp)
        
        return order_groups
    
    def run_oggm_for_all_stps(self):
        """为所有STP运行OGGM算法"""
        
        # 按STP分组订单
        stp_groups = {}
        for order in self.merged_orders:
            stp = order.stp_time
            if stp not in stp_groups:
                stp_groups[stp] = []
            stp_groups[stp].append(order)
        
        for stp, orders_list in sorted(stp_groups.items()):
            # 安全地计算平均配送时长
            delivery_times = []
            for o in orders_list:
                delivery_time = float(o.estimated_delivery_time)
                delivery_times.append(delivery_time)
            
            avg_delivery_time = np.mean(delivery_times) if delivery_times else 0
        
        # 对每个STP运行OGGM算法
        all_results = {}
        
        for stp in sorted(stp_groups.keys()):
            try:
                order_groups = self.run_oggm_for_stp(stp)
                all_results[stp] = order_groups
                
                # 输出结果摘要
                
                # 统计订单组大小分布
                size_stats = {}
                for og in order_groups:
                    size = len(og)
                    size_stats[size] = size_stats.get(size, 0) + 1
                
                    
                # 显示前几个多订单组的详情
                multi_order_groups = [og for og in order_groups if len(og) > 1]
                for i, og in enumerate(multi_order_groups[:3]):  # 只显示前3个
                    delivery_times = [o.estimated_delivery_time for o in og]
                    
            except Exception as e:
                import traceback
                traceback.print_exc()
                raise
        
        return all_results, stp_groups
    
    def save_results(self, all_results, stp_groups, filename="oggm_results.json"):
        """将OGGM算法的运行结果保存到JSON文件"""
        
        results_summary = {}
        for stp, order_groups in all_results.items():
            # stp_groups[stp] 是该stp的订单列表
            num_input_orders = len(stp_groups.get(stp, []))
            
            # 计算预期配送时长统计
            input_orders = stp_groups.get(stp, [])
            delivery_times = []
            if input_orders:
                for o in input_orders:
                    delivery_time = float(o.estimated_delivery_time)
                    delivery_times.append(delivery_time)
            
            avg_delivery_time = np.mean(delivery_times) if delivery_times else 0
            min_delivery_time = min(delivery_times) if delivery_times else 0
            max_delivery_time = max(delivery_times) if delivery_times else 0
            
            results_summary[stp] = {
                'total_groups': len(order_groups),
                'input_orders': num_input_orders,
                'expected_delivery_time_stats': {
                    'average': round(avg_delivery_time, 2),
                    'min': round(min_delivery_time, 2),
                    'max': round(max_delivery_time, 2)
                },
                'size_distribution': {},
                'groups_detail': []
            }
            
            for i, og in enumerate(order_groups): # og 是 Order 对象的列表
                # 收集订单组中各订单的预期配送时长
                order_delivery_times = []
                for order in og:
                    delivery_time = float(order.estimated_delivery_time)
                    order_delivery_times.append(delivery_time)
                
                # 为订单组生成最优配送序列
                start_pos = (og[0].restaurant_lng, og[0].restaurant_lat)
                sequence = self._get_sequence_from_order_group(og, start_pos[0], start_pos[1])
                
                group_info = {
                    'group_id': i,
                    'orders': [order.order_id for order in og],
                    'size': len(og),
                    'starting_order': og[0].order_id,
                    'expected_delivery_times': order_delivery_times,
                    'avg_expected_delivery_time': round(np.mean(order_delivery_times), 2) if order_delivery_times else 0,
                    'sequence': sequence  # 保存OGGM生成的最优序列
                }
                results_summary[stp]['groups_detail'].append(group_info)
                
                size = len(og)
                if size not in results_summary[stp]['size_distribution']:
                    results_summary[stp]['size_distribution'][size] = 0
                results_summary[stp]['size_distribution'][size] += 1
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results_summary, f, ensure_ascii=False, indent=2)

    def get_algorithms_data(self):
        """获取算法所需的基础数据，供其他算法使用"""
        return {
            'orders': self.merged_orders,
            'driver_positions': self.driver_positions,
            'order_data': self.order_data,
            'stp_data': self.stp_data
        }
