import pandas as pd
import numpy as np
import random
import json
from math import radians, cos, sin, asin, sqrt
import math

class Order:
    """订单类"""
    def __init__(self, order_id, restaurant_lng, restaurant_lat, customer_lng, customer_lat, 
                 stp_time, estimated_delivery_time, order_value, platform_order_time,
                 preparation_time, volume):
        self.order_id = order_id
        self.restaurant_lng = restaurant_lng
        self.restaurant_lat = restaurant_lat
        self.customer_lng = customer_lng
        self.customer_lat = customer_lat
        self.stp_time = stp_time
        self.estimated_delivery_time = estimated_delivery_time
        self.order_value = order_value
        self.platform_order_time = platform_order_time
        self.preparation_time = preparation_time
        self.volume = volume

class OGGMAlgorithm:
    """订单组生成算法（OGGM）"""
    
    def __init__(self, stp_choices_path, orders_data_path='meituan_orders_with_delivery_time.csv',
                 theta_t=0.3, travel_time_deviation_ratio=0.2, driver_capacity=50, num_drivers=30):
        """
        初始化OGGM算法
        
        Args:
            stp_choices_path: 消费者STP选择结果文件路径
            orders_data_path: 订单数据文件路径
            theta_t: 鲁棒优化预算 gamma
            travel_time_deviation_ratio: 旅行时间最大偏差率
            driver_capacity: 车辆容量
        """
        self.orders_data_path = orders_data_path
        self.stp_choices_path = stp_choices_path
        self.driver_positions = None
        self.order_data = None
        self.stp_data = None
        self.merged_orders = None
        
        # 算法参数
        self.delta_bar = 2         # 用于计算虚拟骑手的骑手数量
        self.mu = 0.2              # 订单延迟度阈值
        self.U = 10                # 每个骑手的最大订单数
        self.rider_speed_kmh = 20  # 骑手速度
        self.driver_capacity = driver_capacity # 车辆容量
        
        # 新增：司机数量
        self.num_drivers = num_drivers
        
        # === Robust travel-time uncertainty parameters ===
        self.theta_t = theta_t
        self.travel_time_deviation_ratio = travel_time_deviation_ratio
        
        # 初始化数据
        self._load_data()
        
    def _load_data(self):
        """加载并预处理数据"""
        
        try:
            # 1. 读取订单数据
            self.order_data = pd.read_csv(self.orders_data_path)
            
            # 2. 读取STP选择数据
            self.stp_data = pd.read_csv(self.stp_choices_path)
            self.stp_data = self.stp_data[self.stp_data['choice_type'] == 'purchase']
            
            # 3. 生成司机初始位置
            self._generate_driver_positions()
            
            # 4. 合并数据并创建订单对象
            self._merge_and_create_orders()
            
        except Exception as e:
            raise
    
    def _generate_driver_positions(self):
        """生成司机初始位置 - 从grab位置中随机选择30个"""
        grab_positions = self.order_data[['grab_lng_decimal', 'grab_lat_decimal']].dropna()
        grab_positions = grab_positions[(grab_positions['grab_lng_decimal'] != 0) & 
                                       (grab_positions['grab_lat_decimal'] != 0)]
        
        if len(grab_positions) < self.num_drivers:
            self.driver_positions = grab_positions.values.tolist()
        else:
            selected_positions = grab_positions.sample(n=self.num_drivers, random_state=42)
            self.driver_positions = selected_positions.values.tolist()
        
    
    def _merge_and_create_orders(self):
        """合并数据并创建订单对象"""
        merged_data = []
        skipped_count = 0
        
        for _, stp_row in self.stp_data.iterrows():
            consumer_id = stp_row['consumer_id']
            
            if consumer_id <= len(self.order_data):
                order_row = self.order_data.iloc[consumer_id - 1]
                
                if (pd.isna(order_row['sender_lng_decimal']) or pd.isna(order_row['sender_lat_decimal']) or
                    pd.isna(order_row['recipient_lng_decimal']) or pd.isna(order_row['recipient_lat_decimal'])):
                    skipped_count += 1
                    continue
                
                estimated_delivery_time_value = order_row.get('estimated_delivery_time', None)
                
                if pd.notna(estimated_delivery_time_value) and estimated_delivery_time_value > 0:
                    estimated_delivery_time = float(estimated_delivery_time_value)
                else:
                    continue
                
                order = Order(
                    order_id=str(consumer_id),
                    restaurant_lng=order_row['sender_lng_decimal'],
                    restaurant_lat=order_row['sender_lat_decimal'],
                    customer_lng=order_row['recipient_lng_decimal'],
                    customer_lat=order_row['recipient_lat_decimal'],
                    stp_time=stp_row['chosen_time'],
                    estimated_delivery_time=estimated_delivery_time,
                    order_value=order_row['price'],
                    platform_order_time=order_row['platform_order_time'],
                    preparation_time=order_row.get('preparation_time', 0),
                    volume=order_row.get('volume', 1) # 默认为1
                )
                merged_data.append(order)
        
        self.merged_orders = merged_data
    
    @staticmethod
    def haversine(lon1, lat1, lon2, lat2):
        """计算两点间的地理距离（单位：公里）"""
        lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
        dlon = lon2 - lon1 
        dlat = lat2 - lat1 
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a)) 
        r = 6371
        return c * r
    
    @staticmethod
    def time_to_minutes(time_str):
        """将时间字符串转换为从午夜开始的分钟数"""
        if isinstance(time_str, str):
            hour, minute = map(int, time_str.split(':'))
            return hour * 60 + minute
    
    def _get_sequence_from_order_group(self, order_group, start_pos=None):
        """使用最便宜插入法生成灵活的配送序列（支持pickup/delivery混合）"""
        if not order_group:
            return []

        # 如果没有提供起始位置，使用第一个订单的餐厅位置作为默认
        if start_pos is None:
            start_pos = (order_group[0].restaurant_lng, order_group[0].restaurant_lat)

        # 使用最便宜插入法生成最优序列
        optimal_sequence = self.generate_optimal_delivery_sequence(order_group, start_pos)
        
        if optimal_sequence is None:
            return None

        # 转换格式以保持兼容性（pickup_X -> r_X, delivery_X -> c_X）
        converted_sequence = []
        for node in optimal_sequence:
            if node.startswith('pickup_'):
                order_id = node.split('_')[1]
                converted_sequence.append(f'r_{order_id}')
            elif node.startswith('delivery_'):
                order_id = node.split('_')[1]
                converted_sequence.append(f'c_{order_id}')
        
        return converted_sequence
    
    def generate_optimal_delivery_sequence(self, order_group, start_pos):
        """
        使用最便宜插入法生成最优的配送序列。
        如果因容量等约束无法生成包含所有订单的序列，则返回 None。
        """
        if not order_group:
            return []
        
        if len(order_group) == 1:
            # 单个订单的序列总是可行的（假设单个订单体积不超过车辆容量）
            order = order_group[0]
            if getattr(order, 'volume', 1) > self.driver_capacity:
                return None # 单个订单超容
            return [f'pickup_{order.order_id}', f'delivery_{order.order_id}']
        
        # 从第一个订单开始
        first_order = order_group[0]
        if getattr(first_order, 'volume', 1) > self.driver_capacity:
            return None # 第一个订单就超容

        sequence = [f'pickup_{first_order.order_id}', f'delivery_{first_order.order_id}']
        
        # 逐个插入剩余订单
        for i, order in enumerate(order_group[1:]):
            # 传递当前已在序列中的订单组
            current_order_group_in_sequence = order_group[:i+1]
            
            new_sequence = self.cheapest_insertion_robust(
                sequence, order, start_pos, current_order_group_in_sequence
            )
            
            if new_sequence is None:
                # 如果插入失败（例如，由于容量限制），则无法为此订单组生成可行序列
                return None
            
            sequence = new_sequence
        
        return sequence
    
    def cheapest_insertion_robust(self, sequence, new_order, start_pos, order_group):
        """将新订单插入到现有序列中的最优位置（鲁棒版本）"""
        pickup_node = f'pickup_{new_order.order_id}'
        delivery_node = f'delivery_{new_order.order_id}'
        
        best_sequence = None
        best_cost = float('inf')
        
        # The full order group for capacity check should include the new order
        full_order_group_for_check = order_group + [new_order]

        # 尝试所有可能的插入位置
        for pickup_pos in range(len(sequence) + 1):
            for delivery_pos in range(pickup_pos + 1, len(sequence) + 2):
                # 创建临时序列
                temp_sequence = sequence[:]
                temp_sequence.insert(pickup_pos, pickup_node)
                temp_sequence.insert(delivery_pos, delivery_node)
                
                # 检查容量约束
                if self.is_sequence_capacity_feasible(temp_sequence, full_order_group_for_check):
                    # 计算鲁棒成本
                    cost = self.calculate_sequence_robust_cost(temp_sequence, start_pos, full_order_group_for_check)
                    if cost < best_cost:
                        best_cost = cost
                        best_sequence = temp_sequence
        
        return best_sequence if best_sequence else sequence
    
    def is_sequence_capacity_feasible(self, sequence, order_group):
        """检查配送序列是否满足容量约束"""
        current_volume = 0
        max_volume = 0
        # Combine the new order with the existing group to check capacity correctly
        # The 'order_group' passed here might not contain the 'new_order' from the insertion method
        all_orders_in_sequence = []
        all_order_ids = set(node.split('_')[1] for node in sequence)
        
        # Create a map from order_id to the actual Order object
        order_map = {o.order_id: o for o in order_group}
        
        # Check if all orders in the sequence are in the map, if not, something is wrong
        for oid in all_order_ids:
            if oid not in order_map:
                # This case should be handled, maybe by passing the new_order explicitly
                # For now, let's be safe and assume it's not feasible if we can't find an order
                return False
        
        for node in sequence:
            # The sequence from cheapest_insertion_robust uses 'pickup_...' and 'delivery_...'
            node_type, order_id = node.split('_', 1)
            
            if order_id in order_map:
                order = order_map[order_id]
                if node_type == 'pickup':
                    current_volume += getattr(order, 'volume', 1)
                elif node_type == 'delivery':
                    current_volume -= getattr(order, 'volume', 1)
                
                # Must track the maximum volume held by the driver during the sequence
                max_volume = max(max_volume, current_volume)
        
        return max_volume <= self.driver_capacity
    
    def calculate_sequence_robust_cost(self, sequence, start_pos, order_group):
        """计算配送序列的鲁棒成本"""
        if not sequence:
            return 0
        
        # 创建订单映射
        orders_map = {order.order_id: order for order in order_group}
        
        # 使用现有的鲁棒成本计算方法
        try:
            travel_cost, penalty_cost, total_cost = self._calculate_robust_cost_for_sequence(
                sequence, start_pos, 0, orders_map)
            return total_cost
        except:
            # 如果鲁棒成本计算失败，回退到简单距离计算
            return self.calculate_simple_sequence_distance(sequence, start_pos, order_group)
    
    def calculate_simple_sequence_distance(self, sequence, start_pos, order_group):
        """计算序列的简单距离成本（备用方法）"""
        if not sequence:
            return 0
        
        order_map = {order.order_id: order for order in order_group}
        total_distance = 0
        current_pos = start_pos
        
        for node in sequence:
            node_type, order_id = node.split('_')
            
            if order_id in order_map:
                order = order_map[order_id]
                if node_type == 'pickup':
                    target_pos = (order.restaurant_lng, order.restaurant_lat)
                else:  # delivery
                    target_pos = (order.customer_lng, order.customer_lat)
                
                distance = self.haversine(current_pos[0], current_pos[1], target_pos[0], target_pos[1])
                total_distance += distance
                current_pos = target_pos
        
        return total_distance

    def _calculate_robust_cost_for_sequence(self, sequence, start_pos, start_time, orders_map):
        """
        (移植自 OGAH 并适配)
        精确计算给定配送序列的鲁棒成本（旅行时间+延迟时间，无货币系数），并考虑备餐时间。
        采用动态规划实现。
        """
        if not sequence:
            return 0.0, 0.0, 0.0

        nodes_in_sequence = ['start'] + sequence
        start_pos_tuple = (start_pos[0], start_pos[1])
        
        # 1. 构建坐标列表和弧段列表
        coords = [start_pos_tuple]
        for node_str in sequence:
            node_type, order_id_str = node_str.split('_', 1)
            order = orders_map[order_id_str]
            coords.append((order.restaurant_lng, order.restaurant_lat) if node_type == 'r' else (order.customer_lng, order.customer_lat))
        
        arcs = [(coords[i], coords[i+1]) for i in range(len(coords)-1)]
        nominal_times = [(self.haversine(s[0], s[1], e[0], e[1]) / self.rider_speed_kmh * 60) for s,e in arcs]
        deviations = [t * self.travel_time_deviation_ratio for t in nominal_times]

        # 2. 初始化DP表和不确定性预算
        gamma = math.ceil(self.theta_t * len(arcs)) if arcs else 0
        dp = [[-1.0] * (gamma + 1) for _ in range(len(nodes_in_sequence))]
        dp[0][0] = start_time

        # 3. DP状态转移
        for i in range(len(arcs)):  # 遍历每条弧段, i 是出发节点索引
            t_nom, dev = nominal_times[i], deviations[i]
            
            # 检查当前节点i是否为餐厅，并计算备餐完成时间
            node_i_str = nodes_in_sequence[i]
            is_restaurant_pickup = (i > 0 and node_i_str.startswith('r'))
            food_ready_time = -1.0
            if is_restaurant_pickup:
                _, order_id_str = node_i_str.split('_', 1)
                order = orders_map[order_id_str]
                stp_time_minutes = self.time_to_minutes(order.stp_time)
                food_ready_time = stp_time_minutes + order.preparation_time / 2
            
            for g in range(gamma + 1):
                if dp[i][g] == -1.0: continue
                
                arrival_at_i = dp[i][g]
                
                # 骑手从节点i的出发时间 = max(到达时间, 备餐完成时间)
                departure_at_i = max(arrival_at_i, food_ready_time) if is_restaurant_pickup else arrival_at_i

                # 使用当前弧段的不确定性预算
                if g < gamma:
                    next_arrival_with_dev = departure_at_i + t_nom + dev
                    if dp[i+1][g+1] == -1.0 or next_arrival_with_dev < dp[i+1][g+1]:
                        dp[i+1][g+1] = next_arrival_with_dev

                # 不使用当前弧段的不确定性预算
                next_arrival_no_dev = departure_at_i + t_nom
                if dp[i+1][g] == -1.0 or next_arrival_no_dev < dp[i+1][g]:
                    dp[i+1][g] = next_arrival_no_dev

        # 4. 计算鲁棒旅行时间和惩罚
        worst_finish_time = max((v for v in dp[-1] if v != -1.0), default=start_time)
        robust_travel_time = worst_finish_time - start_time

        penalty_time = 0.0
        for i, node_str in enumerate(nodes_in_sequence):
            if i == 0: continue
            if node_str.startswith("c_"):
                _, order_id_str = node_str.split('_', 1)
                order = orders_map[order_id_str]
                deadline = self.time_to_minutes(order.stp_time) + order.estimated_delivery_time
                
                worst_arrival_at_node = max((v for v in dp[i] if v != -1.0), default=-1.0)
                
                if worst_arrival_at_node != -1.0:
                    delay = max(worst_arrival_at_node - deadline, 0)
                    penalty_time += delay

        total_cost_time = robust_travel_time + penalty_time
        return total_cost_time, robust_travel_time, penalty_time

    def _is_sequence_capacity_feasible(self, sequence, orders_map):
        """检查给定序列是否在全程满足容量约束"""
        if not sequence:
            return True
        
        current_load = 0
        max_load = 0
        for node in sequence:
            # The sequence from _get_sequence_from_order_group uses 'r_...' and 'c_...'
            node_type, order_id_str = node.split('_', 1)
            if order_id_str in orders_map:
                order = orders_map[order_id_str]
                if node_type == 'r':  # Pickup
                    current_load += getattr(order, 'volume', 1)
                elif node_type == 'c':  # Delivery
                    current_load -= getattr(order, 'volume', 1)
                
                # Must track the maximum load held by the driver during the sequence
                max_load = max(max_load, current_load)
        
        return max_load <= self.driver_capacity
    
    def calculate_order_group_matching_degree_j4(self, order_group, new_order, virtual_driver_pos, orders_map):
        """
        根据鲁棒成本节省计算订单组与新订单的匹配度
        fit(B_j, o_i) = cost_robust(B_j) + cost_robust(o_i) - cost_robust(B_j U o_i)
        """
        vd_pos = virtual_driver_pos
        # 匹配度计算应基于同一个时间基准
        start_time = self.time_to_minutes(new_order.stp_time)

        # 1. cost_robust(o_i) - 计算单个新订单的鲁棒成本
        new_order_seq = self._get_sequence_from_order_group([new_order], vd_pos)
        if new_order_seq is None:
            return -float('inf')  # 新订单本身不可行

        # 核心修正：此处应使用鲁棒成本计算，而不仅仅是距离
        cost_new_order, _, _ = self._calculate_robust_cost_for_sequence(
            new_order_seq, vd_pos, start_time, {new_order.order_id: new_order}
        )
        if cost_new_order == float('inf'): return -float('inf')

        # 2. cost_robust(B_j) - 计算现有订单组的鲁棒成本
        base_sequence = self._get_sequence_from_order_group(order_group, vd_pos)
        if base_sequence is None:
            # 理论上不应发生，因为 order_group 应该是已经验证过的可行组
            return -float('inf')
        
        cost_base_group, _, _ = self._calculate_robust_cost_for_sequence(
            base_sequence, vd_pos, start_time, orders_map
        )
        if cost_base_group == float('inf'): return -float('inf')

        # 3. cost_robust(B_j U o_i) - 计算合并后订单组的鲁棒成本
        merged_order_group = order_group + [new_order]
        merged_sequence = self._get_sequence_from_order_group(merged_order_group, vd_pos)

        if merged_sequence is None:
            return -float('inf')  # 合并后订单组不可行 (如超容)

        merged_orders_map = orders_map.copy()
        merged_orders_map[new_order.order_id] = new_order
        
        cost_merged_group, _, _ = self._calculate_robust_cost_for_sequence(
            merged_sequence, vd_pos, start_time, merged_orders_map
        )
        if cost_merged_group == float('inf'): return -float('inf')

        # 成本节省 = 合并前的总成本 - 合并后的成本
        fit = (cost_base_group + cost_new_order) - cost_merged_group
        return fit
    
    def calculate_max_delay_degree_for_group(self, order_group, virtual_driver_lng, virtual_driver_lat):
        """计算订单组中所有订单的最大延迟度（鲁棒）"""
        if not order_group:
            return 0.0

        start_pos = (virtual_driver_lng, virtual_driver_lat)
        sequence = self._get_sequence_from_order_group(order_group, start_pos)
        orders_map = {order.order_id: order for order in order_group}
        
        start_time = self.time_to_minutes(order_group[0].stp_time)

        _, _, penalty_time = self._calculate_robust_cost_for_sequence(
            sequence, start_pos, start_time, orders_map
        )
        
        max_delay_degree = 0.0
        if penalty_time > 0 and len(order_group) > 0:
             avg_estimated_time = sum(o.estimated_delivery_time for o in order_group) / len(order_group)
             if avg_estimated_time > 0:
                max_delay_degree = (penalty_time / len(order_group)) / avg_estimated_time

        return max_delay_degree
    
    def create_virtual_drivers(self, orders_at_stp):
        """为每个订单创建虚拟骑手"""
        virtual_drivers_info = {}
        
        for order in orders_at_stp:
            distances = []
            for driver_lng, driver_lat in self.driver_positions:
                dist = self.haversine(driver_lng, driver_lat, order.restaurant_lng, order.restaurant_lat)
                distances.append((driver_lng, driver_lat, dist))
            
            distances.sort(key=lambda x: x[2])
            closest_drivers = distances[:self.delta_bar]
            
            avg_lng = np.mean([d[0] for d in closest_drivers])
            avg_lat = np.mean([d[1] for d in closest_drivers])
            
            stp_time_minutes = self.time_to_minutes(order.stp_time)
            
            virtual_drivers_info[order.order_id] = (avg_lng, avg_lat, stp_time_minutes)
        
        return virtual_drivers_info
    
    def oggm_algorithm(self, orders_at_stp):
        """订单组生成算法（OGGM）核心实现"""
        
        if not orders_at_stp:
            return []
        
        all_order_groups = []
        num_orders = len(orders_at_stp)
        orders_map = {order.order_id: order for order in orders_at_stp}
        virtual_drivers_info = self.create_virtual_drivers(orders_at_stp)
        
        for start_idx, starting_order in enumerate(orders_at_stp):
            # 关键修复：检查单个订单是否就超过容量
            if getattr(starting_order, 'volume', 1) > self.driver_capacity:
                continue  # 如果单个订单就超容，则不能构成任何订单组

            current_cluster = [starting_order]
            # 只有在单个订单有效时，才将其作为第一个候选组
            order_groups_for_start = [current_cluster.copy()]
            remaining_indices = [i for i in range(num_orders) if i != start_idx]
            
            vd_lng, vd_lat, vd_start_time = virtual_drivers_info[starting_order.order_id]
            virtual_driver_pos = (vd_lng, vd_lat)
            
            while len(current_cluster) < self.U and remaining_indices:
                order_priorities = []
                
                for remain_idx in remaining_indices:
                    remain_order = orders_at_stp[remain_idx]
                    matching_degree = self.calculate_order_group_matching_degree_j4(
                        current_cluster, remain_order, virtual_driver_pos, orders_map
                    )
                    order_priorities.append((remain_idx, remain_order, matching_degree))
                
                order_priorities.sort(key=lambda x: x[2], reverse=True)
                
                if not order_priorities or order_priorities[0][2] <= 0:
                    break
                
                best_idx, best_order, _ = order_priorities[0]
                
                test_cluster = current_cluster + [best_order]
                
                temp_sequence_for_check = self._get_sequence_from_order_group(test_cluster, virtual_driver_pos)
                if not temp_sequence_for_check:
                    # 如果返回的序列是None，说明无法满足容量约束
                    remaining_indices.remove(best_idx)
                    continue

                # 容量检查现在已经内置在序列生成中，但为保险起见可以保留
                if not self._is_sequence_capacity_feasible(temp_sequence_for_check, {o.order_id: o for o in test_cluster}):
                    remaining_indices.remove(best_idx)
                    continue

                max_delay_degree = self.calculate_max_delay_degree_for_group(test_cluster, vd_lng, vd_lat)
                
                if max_delay_degree > self.mu:
                    remaining_indices.remove(best_idx)
                    continue
                
                current_cluster.append(best_order)
                remaining_indices.remove(best_idx)
                order_groups_for_start.append(current_cluster.copy())
            
            all_order_groups.extend(order_groups_for_start)
        
        return all_order_groups
    
    def run_oggm_for_stp(self, stp_time):
        """为指定STP运行OGGM算法"""
        orders_at_stp = [order for order in self.merged_orders if order.stp_time == stp_time]
        return self.oggm_algorithm(orders_at_stp)
    
    def run_oggm_for_all_stps(self):
        """为所有STP运行OGGM算法"""
        stp_groups = {}
        for order in self.merged_orders:
            stp = order.stp_time
            if stp not in stp_groups:
                stp_groups[stp] = []
            stp_groups[stp].append(order)
        
        all_results = {}
        for stp in sorted(stp_groups.keys()):
            try:
                order_groups = self.run_oggm_for_stp(stp)
                all_results[stp] = order_groups
            except Exception as e:
                import traceback
                traceback.print_exc()
                raise
        
        return all_results, stp_groups
    
    def save_results(self, all_results, stp_groups, filename="oggm_results.json"):
        """将OGGM算法的运行结果保存到JSON文件"""
        results_summary = {}
        for stp, order_groups in all_results.items():
            num_input_orders = len(stp_groups.get(stp, []))
            
            results_summary[stp] = {
                'total_groups': len(order_groups),
                'input_orders': num_input_orders,
                'groups_detail': []
            }
            
            for i, og in enumerate(order_groups):
                # 为订单组生成序列
                start_pos = (og[0].restaurant_lng, og[0].restaurant_lat)
                sequence = self._get_sequence_from_order_group(og, start_pos)
                
                group_info = {
                    'group_id': i,
                    'orders': [order.order_id for order in og],
                    'size': len(og),
                    'starting_order': og[0].order_id,
                    'sequence': sequence  # 保存序列
                }
                results_summary[stp]['groups_detail'].append(group_info)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results_summary, f, ensure_ascii=False, indent=2)

    def get_algorithms_data(self):
        """获取算法所需的基础数据，供其他算法使用"""
        return {
            'orders': self.merged_orders,
            'driver_positions': self.driver_positions,
            'order_data': self.order_data,
            'stp_data': self.stp_data
        }
