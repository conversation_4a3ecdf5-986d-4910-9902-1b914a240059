import json
import pandas as pd
import numpy as np
from math import radians, cos, sin, asin, sqrt
import copy
import math

class OGAHAlgorithm:
    """订单组分配启发式算法（OGAH）"""
    
    def __init__(self, stp_choices_path, oggm_results_path, ogsa_results_path,
                 orders_data_path='meituan_orders_with_delivery_time.csv',
                 alpha=0.2, beta=1, epsilon=1.6, delta_max=3, delta_bar=2, 
                 rider_speed_kmh=20, num_drivers=30, # 新增：num_drivers
                 theta_t=0.3, travel_time_deviation_ratio=0.2, driver_capacity=50):
        """
        初始化OGAH算法
        
        Args:
            stp_choices_path: 消费者STP选择结果文件路径
            oggm_results_path: OGGM算法结果文件路径
            ogsa_results_path: OGSA算法结果文件路径
            orders_data_path: 订单数据文件路径
            alpha: 旅行成本系数
            beta: 延迟成本系数
            epsilon: 订单组规模调节参数
            delta_max: 最大δ值
            delta_bar: 虚拟司机计算参数
            rider_speed_kmh: 骑手速度（公里/小时）
            num_drivers: 司机数量
            theta_t: 鲁棒优化预算 gamma
            travel_time_deviation_ratio: 旅行时间最大偏差率
            driver_capacity: 车辆容量
        """
        self.orders_data_path = orders_data_path
        self.stp_choices_path = stp_choices_path
        self.ogsa_results_path = ogsa_results_path
        self.oggm_results_path = oggm_results_path
        
        # 算法参数
        self.alpha = alpha
        self.beta = beta
        self.epsilon = epsilon
        self.delta_max = delta_max
        self.delta_bar = delta_bar
        self.rider_speed_kmh = rider_speed_kmh
        self.num_drivers = num_drivers # 新增：存储司机数量
        self.driver_capacity = driver_capacity
        
        # === Robust travel-time uncertainty parameters ===
        self.theta_t = theta_t
        self.travel_time_deviation_ratio = travel_time_deviation_ratio
        
        # 数据存储
        self.driver_positions = None
        self.order_positions = None
        self.ogsa_results = None
        self.oggm_results = None
        self.reconstructed_order_groups = None
        
        # 初始化数据
        self._load_all_data()
    
    def _load_all_data(self):
        """加载所有必要的数据"""
        
        # 加载基础数据
        self._load_driver_positions()
        self._load_order_positions()
        self._load_ogsa_results()
        self._process_loaded_ogsa_results()
        self._load_oggm_results()
        
        # 重构订单组数据
        self._reconstruct_order_groups()
        
    
    def _load_driver_positions(self):
        """加载司机初始位置数据"""
      
        df = pd.read_csv(self.orders_data_path)
        grab_positions = df[['grab_lng_decimal', 'grab_lat_decimal']].dropna()
        grab_positions = grab_positions[(grab_positions['grab_lng_decimal'] != 0) & 
                                       (grab_positions['grab_lat_decimal'] != 0)]
        
        if len(grab_positions) < self.num_drivers:
            self.driver_positions = grab_positions.values.tolist()
        else:
            selected_positions = grab_positions.sample(n=self.num_drivers, random_state=42)
            self.driver_positions = selected_positions.values.tolist()
        
    
    def _load_order_positions(self):
        """加载订单位置数据"""
        
        # 读取订单数据（包含位置信息）
        orders_df = pd.read_csv(self.orders_data_path)
        
        # 读取消费者STP选择结果  
        stp_df = pd.read_csv(self.stp_choices_path)
        stp_df = stp_df[stp_df['choice_type'] == 'purchase']
        
        
        # 建立consumer_id到订单位置的映射
        self.order_positions = {}
        
        for _, stp_row in stp_df.iterrows():
            consumer_id = stp_row['consumer_id']
            
            # consumer_id从1开始，对应orders_df的索引从0开始
            if consumer_id <= len(orders_df):
                order_row = orders_df.iloc[consumer_id - 1]
                
                # 检查必要字段是否存在
                if (pd.notna(order_row['sender_lng_decimal']) and pd.notna(order_row['sender_lat_decimal']) and
                    pd.notna(order_row['recipient_lng_decimal']) and pd.notna(order_row['recipient_lat_decimal'])):
                    
                    # 直接读取预期配送时长（已经是分钟为单位的时间段）
                    estimated_delivery_time_value = order_row.get('estimated_delivery_time', None)
                    
                    if pd.notna(estimated_delivery_time_value) and estimated_delivery_time_value > 0:
                        estimated_delivery_time = float(estimated_delivery_time_value)
                    else:
                        continue
                    
                    preparation_time = order_row.get('preparation_time', 0)
                    volume = order_row.get('volume', 1)  # 默认为1

                    self.order_positions[str(consumer_id)] = {
                                'restaurant_lng': order_row['sender_lng_decimal'],
                                'restaurant_lat': order_row['sender_lat_decimal'],
                                'customer_lng': order_row['recipient_lng_decimal'],
                                'customer_lat': order_row['recipient_lat_decimal'],
                                'chosen_time': stp_row['chosen_time'],
                                'chosen_price': stp_row['chosen_price'],
                                'order_value': order_row['price'],
                                'order_id': order_row['order_id'],
                                'estimated_delivery_time': estimated_delivery_time,  # 使用计算出的预期配送时长
                                'preparation_time': preparation_time,
                                'volume': volume
                            } 
        
    
    def _load_ogsa_results(self):
        """加载OGSA算法的详细结果"""
        
        with open(self.ogsa_results_path, 'r', encoding='utf-8') as f:
            self.ogsa_results = json.load(f)
    
    def _process_loaded_ogsa_results(self):
        """处理从文件加载的OGSA结果，确保订单包含estimated_delivery_time"""
        if not hasattr(self, 'ogsa_results') or not self.ogsa_results or \
           not hasattr(self, 'order_positions') or not self.order_positions:
            return

        for stp, stp_data in self.ogsa_results.items():
            if 'best_result' in stp_data and 'selected_order_groups' in stp_data['best_result']:
                for group in stp_data['best_result']['selected_order_groups']:
                    if 'orders' in group and isinstance(group['orders'], list):
                        processed_orders = []
                        for order_in_group in group['orders']:
                            order_id_str = None
                            reconstructed_order = None

                            if isinstance(order_in_group, dict) and 'order_id' in order_in_group:
                                order_id_str = str(order_in_group['order_id'])
                                reconstructed_order = order_in_group
                            elif isinstance(order_in_group, (str, int)):
                                order_id_str = str(order_in_group)
                            else: # Unknown structure
                                processed_orders.append(order_in_group)
                                continue
                            
                            if order_id_str:
                                if order_id_str in self.order_positions:
                                    pos_info = self.order_positions[order_id_str]
                                    if reconstructed_order is None:
                                        reconstructed_order = {
                                            'order_id': order_id_str,
                                            'restaurant_lng': pos_info['restaurant_lng'],
                                            'restaurant_lat': pos_info['restaurant_lat'],
                                            'customer_lng': pos_info['customer_lng'],
                                            'customer_lat': pos_info['customer_lat'],
                                            'chosen_time': pos_info['chosen_time'],
                                            'chosen_price': pos_info['chosen_price'],
                                            'order_value': pos_info['order_value'],
                                            'original_order_id': pos_info['order_id'],
                                            'estimated_delivery_time': pos_info['estimated_delivery_time']
                                        }
                                    elif 'estimated_delivery_time' not in reconstructed_order:
                                        reconstructed_order['estimated_delivery_time'] = pos_info['estimated_delivery_time']
                                else:
                                    if reconstructed_order is None: 
                                         processed_orders.append(order_in_group)
                                    else: 
                                         processed_orders.append(reconstructed_order)
                                    continue
                            
                            if reconstructed_order is not None:
                                processed_orders.append(reconstructed_order)
                            elif order_in_group is not None:
                                processed_orders.append(order_in_group)

                        group['orders'] = processed_orders
    
    def _load_oggm_results(self):
        """加载OGGM算法结果"""
        
        with open(self.oggm_results_path, 'r', encoding='utf-8') as f:
            self.oggm_results = json.load(f)
        
    
    def _reconstruct_order_groups(self):
        """重构订单组，补充位置信息"""
        
        reconstructed_results = {}
        
        for stp, stp_data in self.oggm_results.items():
            reconstructed_groups = []
            
            for group_detail in stp_data['groups_detail']:
                order_ids = group_detail['orders']  # 这些是consumer_id
                
                # 重构订单组，添加位置信息
                reconstructed_orders = []
                valid_orders = 0
                
                for consumer_id in order_ids:
                    if consumer_id in self.order_positions:
                        pos_info = self.order_positions[consumer_id]
                        
                        reconstructed_orders.append({
                            'order_id': consumer_id,  # 使用consumer_id作为标识
                            'restaurant_lng': pos_info['restaurant_lng'],
                            'restaurant_lat': pos_info['restaurant_lat'],
                            'customer_lng': pos_info['customer_lng'],
                            'customer_lat': pos_info['customer_lat'],
                            'chosen_time': pos_info['chosen_time'],
                            'chosen_price': pos_info['chosen_price'],
                            'order_value': pos_info['order_value'],
                            'original_order_id': pos_info['order_id'],
                            'estimated_delivery_time': pos_info['estimated_delivery_time']  # 关键：包含预期配送时长
                        })
                        valid_orders += 1
                
                if reconstructed_orders:  # 只保留有效的订单组
                    reconstructed_groups.append({
                        'group_id': group_detail['group_id'],
                        'orders': reconstructed_orders,
                        'size': len(reconstructed_orders),
                        'starting_order': group_detail['starting_order']
                    })
            
            reconstructed_results[stp] = {
                'total_groups': len(reconstructed_groups),
                'groups_detail': reconstructed_groups
            }
            
        
        self.reconstructed_order_groups = reconstructed_results
    
    @staticmethod
    def haversine(lon1, lat1, lon2, lat2):
        """计算两点间的地理距离（单位：公里）"""
        if pd.isna(lon1) or pd.isna(lat1) or pd.isna(lon2) or pd.isna(lat2):
            return 0
        
        lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
        dlon = lon2 - lon1 
        dlat = lat2 - lat1 
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a)) 
        r = 6371
        return c * r
    
    @staticmethod
    def time_to_minutes(time_str):
        """将时间字符串转换为从午夜开始的分钟数"""
        if isinstance(time_str, str):
            hour, minute = map(int, time_str.split(':'))
            return hour * 60 + minute

    def _get_sequence_from_order_group(self, order_group, start_pos=None):
        """从订单组信息中提取配送序列 - OGAH不生成新序列，直接使用OGSA的结果"""
        # OGAH算法不重新生成序列，而是直接使用OGSA传递的订单组序列
        # 这与Robust SOCDA 7.py中的逻辑一致：将订单组序列附加到司机现有序列
        if not order_group['orders']:
            return []

        # 如果订单组中包含序列信息，直接使用
        if 'sequence' in order_group and order_group['sequence']:
            return order_group['sequence']
        
        # 如果没有序列信息，生成简单的序列（备用方案）
        sequence = []
        for order in order_group['orders']:
            sequence.append(f'r_{order["order_id"]}')
        
        # 添加送货节点（按最近距离排序）
        remaining_orders = order_group['orders'].copy()
        if remaining_orders:
            # 从最后一个取货点开始
            last_order = remaining_orders[-1]
            current_pos = (last_order['restaurant_lng'], last_order['restaurant_lat'])
            
            while remaining_orders:
                min_distance = float('inf')
                nearest_order = None
                
                for order in remaining_orders:
                    distance = self.haversine(current_pos[0], current_pos[1], 
                                            order['customer_lng'], order['customer_lat'])
                    if distance < min_distance:
                        min_distance = distance
                        nearest_order = order
                
                if nearest_order:
                    sequence.append(f'c_{nearest_order["order_id"]}')
                    current_pos = (nearest_order['customer_lng'], nearest_order['customer_lat'])
                    remaining_orders.remove(nearest_order)
        
        return sequence

    def _calculate_robust_cost_with_sequence(self, sequence, start_pos, start_time, orders_map):
        """
        精确计算给定配送序列的鲁棒成本（旅行成本+延迟惩罚），并考虑备餐时间。
        采用动态规划实现。
        """
        if not sequence:
            return 0.0, 0.0, 0.0, start_pos, start_time

        # 1. 构建节点、坐标、弧、名义时间、偏差
        nodes_in_sequence = ['start'] + sequence
        start_pos_tuple = (start_pos[0], start_pos[1])
        coords = [start_pos_tuple]
        
        for node_str in sequence:
            node_type, order_id_str = node_str.split('_', 1)
            order = orders_map[order_id_str]
            coords.append((order['restaurant_lng'], order['restaurant_lat']) if node_type == 'r' else (order['customer_lng'], order['customer_lat']))

        arcs = [(coords[i], coords[i+1]) for i in range(len(coords)-1)]
        nominal_times = [(self.haversine(s[0], s[1], e[0], e[1]) / self.rider_speed_kmh * 60) for s,e in arcs]
        deviations = [t * self.travel_time_deviation_ratio for t in nominal_times]
        
        # 2. 初始化DP表和不确定性预算
        gamma = math.ceil(self.theta_t * len(arcs)) if arcs else 0
        dp = [[-1.0] * (gamma + 1) for _ in range(len(nodes_in_sequence))]
        dp[0][0] = start_time

        # 3. DP状态转移
        for i in range(len(arcs)):  # 遍历每条弧段, i 是出发节点索引
            t_nom, dev = nominal_times[i], deviations[i]

            # 检查当前节点i是否为餐厅，并计算备餐完成时间
            node_i_str = nodes_in_sequence[i]
            is_restaurant_pickup = (i > 0 and (node_i_str.startswith('r_') or node_i_str.startswith('pickup_')))
            food_ready_time = -1.0
            if is_restaurant_pickup:
                _, order_id_str = node_i_str.split('_', 1)
                order_info = self.order_positions.get(order_id_str)
                if order_info:
                    food_ready_time = self.time_to_minutes(order_info['chosen_time']) + order_info.get('preparation_time', 0) / 2

            for g in range(gamma + 1):
                if dp[i][g] == -1.0: continue
                
                arrival_at_i = dp[i][g]
                departure_at_i = max(arrival_at_i, food_ready_time) if is_restaurant_pickup else arrival_at_i

                # 使用当前弧段的不确定性预算
                if g < gamma:
                    next_arrival_with_dev = departure_at_i + t_nom + dev
                    if dp[i+1][g+1] == -1.0 or next_arrival_with_dev > dp[i+1][g+1]:
                        dp[i+1][g+1] = next_arrival_with_dev

                # 不使用当前弧段的不确定性预算
                next_arrival_no_dev = departure_at_i + t_nom
                if dp[i+1][g] == -1.0 or next_arrival_no_dev > dp[i+1][g]:
                    dp[i+1][g] = next_arrival_no_dev

        # 4. 基于DP结果计算成本
        worst_finish_time = max((v for v in dp[-1] if v != -1.0), default=start_time)
        robust_travel_time = worst_finish_time - start_time
        # 行驶成本按距离计费，与Hybrid版本保持一致
        total_nominal_distance = sum((t_nom*self.rider_speed_kmh)/60 for t_nom in nominal_times)
        robust_travel_cost = total_nominal_distance * self.alpha
        
        # 建立delivery节点到弧索引的映射，与混合算法保持一致
        deliveries = []  # (order_id, arc_idx)
        for i, node_str in enumerate(sequence):
            if node_str.startswith("c_") or node_str.startswith("delivery_"):
                _, order_id_str = node_str.split('_', 1)
                deliveries.append((order_id_str, i))  # i是在sequence中的索引，也是对应的弧索引
        
        robust_penalty_cost = 0.0
        for order_id_str, arc_idx in deliveries:
            order = orders_map[order_id_str]
            deadline = self.time_to_minutes(order['chosen_time']) + order['estimated_delivery_time']
            
            # 获取弧终点的最坏到达时间，与混合算法compute_rg2_robust_cost一致
            try:
                worst_arrival = max(val for val in dp[arc_idx + 1] if val != -1.0)
                delay = max(worst_arrival - deadline, 0)
                robust_penalty_cost += delay * self.beta
            except ValueError:
                # 如果没有有效的到达时间，跳过这个订单的延迟惩罚
                continue
                        
        total_cost = robust_travel_cost + robust_penalty_cost
        final_pos = coords[-1] if arcs else start_pos
        
        return robust_travel_cost, robust_penalty_cost, total_cost, final_pos, worst_finish_time

    def get_virtual_driver_info(self, order_group, delta):
        """
        获取订单组的虚拟司机信息
        与OGSA.py中的逻辑完全一致
        """
        if not order_group['orders']:
            return {'lng': 0, 'lat': 0, 'start_time': 0}
        
        # 获取第一个订单的餐厅位置作为参考点（与OGGM.py一致）
        first_order = order_group['orders'][0]
        ref_lng = first_order['restaurant_lng']
        ref_lat = first_order['restaurant_lat']
        
        # 计算到所有司机位置的距离
        distances = []
        for driver_lng, driver_lat in self.driver_positions:
            dist = self.haversine(driver_lng, driver_lat, ref_lng, ref_lat)
            distances.append((driver_lng, driver_lat, dist))
        
        # 按距离排序，取前delta个最近的司机
        distances.sort(key=lambda x: x[2])
        closest_drivers = distances[:delta]
        
        # 计算平均位置
        avg_lng = np.mean([d[0] for d in closest_drivers])
        avg_lat = np.mean([d[1] for d in closest_drivers])
        
        # 获取STP时间作为起始时间
        stp_time_minutes = self.time_to_minutes(first_order['chosen_time'])
        
        return {
            'lng': avg_lng,
            'lat': avg_lat,
            'start_time': stp_time_minutes
        }
    
    def simulate_delivery_sequence_for_cost(self, order_group, virtual_driver_lng, virtual_driver_lat):
        """
        模拟订单组的配送过程，计算其完整的鲁棒成本（旅行成本 + 延迟惩罚）。
        此函数已被重写，以精确匹配 Robust SOCDA 2.py 的鲁棒成本计算逻辑，
        取代了原有的仅计算名义成本的简化实现。
        """
        if not order_group:
            return 0, 0, 0

        # --- 1. 构建配送序列的弧和名义时间 ---
        current_lng = virtual_driver_lng
        current_lat = virtual_driver_lat
        
        arcs_nominal_times = []
        delivery_arc_map = {}
        nominal_delivery_times = {}
        cumulative_time = 0

        # 第一阶段：依次取餐
        for order in order_group:
            distance = self.haversine(current_lng, current_lat,
                                      order['restaurant_lng'], order['restaurant_lat'])
            travel_time = (distance / self.rider_speed_kmh) * 60
            arcs_nominal_times.append(travel_time)
            cumulative_time += travel_time
            current_lng = order['restaurant_lng']
            current_lat = order['restaurant_lat']

        # 第二阶段：按最近原则送达
        remaining_orders = order_group.copy()
        while remaining_orders:
            min_distance = float('inf')
            nearest_order = None
            for order in remaining_orders:
                distance = self.haversine(current_lng, current_lat,
                                          order['customer_lng'], order['customer_lat'])
                if distance < min_distance:
                    min_distance = distance
                    nearest_order = order
            
            travel_time = (min_distance / self.rider_speed_kmh) * 60
            arcs_nominal_times.append(travel_time)
            cumulative_time += travel_time
            
            nominal_delivery_times[nearest_order['order_id']] = cumulative_time
            delivery_arc_map[nearest_order['order_id']] = len(arcs_nominal_times) - 1
            
            current_lng = nearest_order['customer_lng']
            current_lat = nearest_order['customer_lat']
            remaining_orders.remove(nearest_order)
            
        # --- 2. 计算鲁棒成本 ---
        deviations = [t * self.travel_time_deviation_ratio for t in arcs_nominal_times]
        positive_arc_count = sum(1 for t in arcs_nominal_times if t > 1e-8)
        gamma = int(math.ceil(self.theta_t * positive_arc_count)) if positive_arc_count > 0 else 0

        # 计算鲁棒旅行成本
        worst_extra_total_time = sum(sorted(deviations, reverse=True)[:gamma])
        total_nominal_time = sum(arcs_nominal_times)
        robust_total_travel_time = total_nominal_time + worst_extra_total_time
        travel_cost = robust_total_travel_time * self.alpha

        # 计算鲁棒延迟惩罚成本
        penalty_cost = 0
        for order in order_group:
            order_id = order['order_id']
            nominal_arrival = nominal_delivery_times.get(order_id)
            delivery_arc_idx = delivery_arc_map.get(order_id)
            
            if nominal_arrival is None or delivery_arc_idx is None:
                continue
                
            prefix_deviations = deviations[:delivery_arc_idx + 1]
            worst_extra_prefix_time = sum(sorted(prefix_deviations, reverse=True)[:gamma])
            worst_arrival_time = nominal_arrival + worst_extra_prefix_time
            
            delay = max(worst_arrival_time - order['estimated_delivery_time'], 0)
            penalty_cost += delay * self.beta

        total_cost = travel_cost + penalty_cost
        
        return total_cost, travel_cost, penalty_cost
    
    def run_ogsa_for_specific_delta(self, stp, delta):
        """
        为特定的δ值运行OGSA算法
        复制OGSA.py中的核心选择逻辑，但只处理指定的δ值
        """
        
        if stp not in self.reconstructed_order_groups:
            return []
        
        stp_data = self.reconstructed_order_groups[stp]
        all_order_groups = stp_data['groups_detail']
        
        if not all_order_groups:
            return []
        
        # Step 1: 初始化
        selected_order_groups = []  # Fδ
        remaining_order_groups = all_order_groups.copy()  # B̄
        covered_orders = set()
        
        # Step 3 & 4: 为每个订单组计算虚拟司机信息和配送成本
        order_group_evaluations = {}
        
        for i, og in enumerate(remaining_order_groups):
            # Step 3: 获取虚拟司机信息，使用delta个最近司机
            virtual_driver_info = self.get_virtual_driver_info(og, delta)
            
            # Step 4: 计算配送成本（旅行成本 + 延迟惩罚成本）
            total_cost, travel_cost, penalty_cost = self.simulate_delivery_sequence_for_cost(
                og['orders'], 
                virtual_driver_info['lng'], 
                virtual_driver_info['lat']
            )
            
            # 计算f值（根据公式24）
            og_size = len(og['orders'])
            f_value = total_cost / (og_size ** self.epsilon)
            
            order_group_evaluations[i] = {
                'order_group': og,
                'cost': total_cost,
                'travel_cost': travel_cost,
                'penalty_cost': penalty_cost,
                'size': og_size,
                'f_value': f_value,
                'virtual_driver_info': virtual_driver_info
            }
        
        # Step 5-7: 集合覆盖迭代
        while remaining_order_groups and order_group_evaluations:
            # Step 6: 找到f值最小的订单组
            best_og_idx = min(order_group_evaluations.keys(), 
                            key=lambda x: order_group_evaluations[x]['f_value'])
            
            best_evaluation = order_group_evaluations[best_og_idx]
            selected_og = best_evaluation['order_group']
            
            # 添加到已选择集合
            selected_order_groups.append(selected_og)
            
            # 记录覆盖的订单
            selected_order_ids = set(order['order_id'] for order in selected_og['orders'])
            covered_orders.update(selected_order_ids)
            
            # Step 7: 移除有重叠的订单组
            indices_to_remove = []
            
            for idx, evaluation in order_group_evaluations.items():
                og_order_ids = set(order['order_id'] for order in evaluation['order_group']['orders'])
                if og_order_ids & selected_order_ids:  # 有交集
                    indices_to_remove.append(idx)
            
            # 移除重叠的订单组
            for idx in indices_to_remove:
                del order_group_evaluations[idx]
            
            # 更新剩余订单组列表
            remaining_order_groups = [evaluation['order_group'] 
                                    for evaluation in order_group_evaluations.values()]
            
            # Step 5: 检查终止条件
            if not remaining_order_groups:
                break
        
        return selected_order_groups
    
    def simulate_order_group_delivery(self, order_group, start_lng, start_lat, start_time):
        """
        模拟订单组的配送过程，返回总距离、总时间和最终位置
        与OGGM/OGSA中的逻辑一致：先依次取餐，再按最近原则送达
        """
        if not order_group['orders']:
            return 0, 0, start_lng, start_lat
        
        current_lng = start_lng
        current_lat = start_lat
        current_time = start_time
        total_distance = 0
        
        # 第一阶段：依次取餐
        for order in order_group['orders']:
            distance = self.haversine(current_lng, current_lat, 
                               order['restaurant_lng'], order['restaurant_lat'])
            total_distance += distance
            travel_time = (distance / self.rider_speed_kmh) * 60  # 分钟
            current_time += travel_time
            current_lng = order['restaurant_lng']
            current_lat = order['restaurant_lat']
        
        # 第二阶段：按最近原则送达
        remaining_orders = order_group['orders'].copy()
        while remaining_orders:
            min_distance = float('inf')
            nearest_order = None
            
            for order in remaining_orders:
                distance = self.haversine(current_lng, current_lat, 
                                   order['customer_lng'], order['customer_lat'])
                if distance < min_distance:
                    min_distance = distance
                    nearest_order = order
            
            total_distance += min_distance
            travel_time = (min_distance / self.rider_speed_kmh) * 60
            current_time += travel_time
            
            current_lng = nearest_order['customer_lng']
            current_lat = nearest_order['customer_lat']
            remaining_orders.remove(nearest_order)
        
        return total_distance, current_time - start_time, current_lng, current_lat
    
    def calculate_og_delivery_cost_for_driver(self, order_group, driver_state, stp_time_minutes):
        """
        计算特定司机执行特定订单组的成本
        """
        # 司机实际开始时间 = max(司机可用时间, STP时间)
        actual_start_time = max(driver_state['available_time'], stp_time_minutes)
        
        
        # 1. 为订单组生成配送序列
        start_pos = (driver_state['lng'], driver_state['lat'])
        sequence = self._get_sequence_from_order_group(order_group, start_pos)
        if not sequence:
            return { 'total_cost': float('inf') }

        # 2. 创建一个临时的订单ID到订单详情的映射，方便查询
        orders_map = {str(order['order_id']): order for order in order_group['orders']}

        # 3. 计算鲁棒成本
        travel_cost, penalty_cost, total_cost, final_pos, completion_time = self._calculate_robust_cost_with_sequence(
            sequence,
            start_pos,
            actual_start_time,
            orders_map
        )
        
        # 计算配送时长
        delivery_duration = completion_time - actual_start_time

        return {
            'total_cost': total_cost,
            'travel_cost': travel_cost,
            'penalty_cost': penalty_cost,
            'delivery_duration': delivery_duration,
            'final_lng': final_pos[0],
            'final_lat': final_pos[1],
            'completion_time': completion_time
        }
    
    def ogah_algorithm_for_stp(self, selected_order_groups, driver_states, stp, stp_time_minutes):
        """
        为单个STP运行OGAH算法, 包含强制分配逻辑以确保所有订单组都被服务。
        """
        
        # 1. 初始化
        assignments = []
        total_travel_cost = 0.0
        total_penalty_cost = 0.0
        
        unassigned_ogs = [copy.deepcopy(og) for og in selected_order_groups]
        # 核心逻辑修正：根据论文，应在所有司机中进行选择，而不是仅在当前空闲的司机中
        remaining_driver_indices = list(driver_states.keys())

        # 2. 主分配循环 (Regret-k Heuristic), 此循环为每个可用司机分配一个最优的OG
        while remaining_driver_indices and unassigned_ogs:
            regret_values = []

            for i, og in enumerate(unassigned_ogs):
                costs_for_og = []
                # 在所有剩余的司机中为当前OG计算成本
                for driver_id in remaining_driver_indices:
                    result = self.calculate_og_delivery_cost_for_driver(og, driver_states[driver_id], stp_time_minutes)
                    costs_for_og.append((result['total_cost'], driver_id))

                if not costs_for_og: continue
                
                costs_for_og.sort() # 按成本升序排序
                
                # 计算悔值（与最优选择、次优选择等的成本差异总和）
                regret = sum(costs_for_og[j][0] - costs_for_og[0][0] for j in range(1, min(len(costs_for_og), 3)))
                best_cost, best_driver_id_for_og = costs_for_og[0]

                regret_values.append({
                    'idx': i, 
                    'og': og, 
                    'regret': regret, 
                    'best_cost': best_cost, 
                    'best_driver_id': best_driver_id_for_og
                })

            if not regret_values: break
            
            # 选择具有最大悔值的订单组进行优先分配
            best_og_to_assign_info = max(regret_values, key=lambda x: x['regret'])
            
            # 获取该订单组的最佳司机和成本信息
            best_driver_id = best_og_to_assign_info['best_driver_id']
            min_cost = best_og_to_assign_info['best_cost']

            # 重新计算完整的配送细节
            best_delivery_details = self.calculate_og_delivery_cost_for_driver(
                best_og_to_assign_info['og'], driver_states[best_driver_id], stp_time_minutes
            )
            
            if best_driver_id != -1:
                # 执行分配
                assignments.append({
                    'driver_id': best_driver_id, 'order_group': best_og_to_assign_info['og'],
                    'delivery_info': best_delivery_details, 'cost': min_cost, 'is_concatenated': False
                })
                total_travel_cost += best_delivery_details['travel_cost']
                total_penalty_cost += best_delivery_details['penalty_cost']
                
                # 更新司机状态
                driver_states[best_driver_id].update({
                    'lng': best_delivery_details['final_lng'],
                    'lat': best_delivery_details['final_lat'],
                    'available_time': best_delivery_details['completion_time']
                })
                
                # 从待处理列表中移除
                unassigned_ogs.pop(best_og_to_assign_info['idx'])
                remaining_driver_indices.remove(best_driver_id)

        # 3. 强制分配剩余的订单组
        if unassigned_ogs:
            for og_to_assign in unassigned_ogs:
                min_incremental_cost = float('inf')
                best_driver_for_concat = -1
                best_concat_details = None

                # 遍历所有骑手，找到追加此任务成本最低的
                for driver_id in range(self.num_drivers):
                    result = self.calculate_og_delivery_cost_for_driver(
                        og_to_assign, driver_states[driver_id], stp_time_minutes
                    )
                    
                    if result['total_cost'] < min_incremental_cost:
                        min_incremental_cost = result['total_cost']
                        best_driver_for_concat = driver_id
                        best_concat_details = {
                            'travel_cost': result['travel_cost'], 
                            'penalty_cost': result['penalty_cost'], 
                            'total_cost': result['total_cost'],
                            'final_lng': result['final_lng'], 
                            'final_lat': result['final_lat'], 
                            'completion_time': result['completion_time'],
                            'final_sequence': self._get_sequence_from_order_group(og_to_assign)
                        }

                if best_driver_for_concat != -1:
                    assignments.append({
                        'driver_id': best_driver_for_concat, 'order_group': og_to_assign,
                        'delivery_info': best_concat_details, 'cost': min_incremental_cost, 'is_concatenated': True
                    })
                    total_travel_cost += best_concat_details['travel_cost']
                    total_penalty_cost += best_concat_details['penalty_cost']
                    
                    # 更新该骑手的状态
                    driver_states[best_driver_for_concat].update({
                        'lng': best_concat_details['final_lng'],
                        'lat': best_concat_details['final_lat'],
                        'available_time': best_concat_details['completion_time']
                    })

        return {
            'stp': stp,
            'assignments': assignments,
            'total_cost': total_travel_cost + total_penalty_cost,
            'total_travel_cost': total_travel_cost,
            'total_penalty_cost': total_penalty_cost,
            'assigned_count': len([a for a in assignments if not a.get('is_concatenated')]),
            'concatenated_count': len([a for a in assignments if a.get('is_concatenated')])
        }
    
    def run_ogah_for_all_stps(self):
        """为所有STP运行OGAH算法，实现跨STP的司机状态跟踪"""
        
        # 初始化司机状态
        driver_states = {}
        for i, (lng, lat) in enumerate(self.driver_positions):
            driver_states[i] = {
                'lng': lng,
                'lat': lat,
                'available_time': 0  # 初始时所有司机都可用
            }
        
        # 按时间顺序处理所有STP
        all_ogah_results = {}
        sorted_stps = sorted(self.ogsa_results.keys())
        
        
        # 对每个STP运行OGAH
        for stp_idx, stp in enumerate(sorted_stps):
            
            # 获取该STP的OGSA结果
            stp_ogsa_result = self.ogsa_results[stp]
            selected_order_groups = stp_ogsa_result['best_result']['selected_order_groups']
            
            if not selected_order_groups:
                continue
            
            # 获取STP时间（分钟）
            stp_time_minutes = self.time_to_minutes(stp)
            
            # 显示当前司机状态
            available_drivers = sum(1 for d in driver_states.values() 
                                  if d['available_time'] <= stp_time_minutes)
            
            # 为每个δ值运行OGAH
            delta_results = {}
            for delta in range(1, self.delta_max + 1):
                
                # 为每个δ值重新运行OGSA选择逻辑
                if delta == stp_ogsa_result['best_delta']:
                    # 对于最优δ值，直接使用已有结果
                    delta_order_groups = selected_order_groups
                else:
                    # 对于非最优δ值，重新运行OGSA选择逻辑
                    delta_selected_groups = self.run_ogsa_for_specific_delta(stp, delta)
                    delta_order_groups = delta_selected_groups
                
                if not delta_order_groups:
                    continue
                
                # 复制司机状态（每个δ独立计算）
                delta_driver_states = copy.deepcopy(driver_states)
                
                # 运行OGAH
                ogah_result = self.ogah_algorithm_for_stp(
                    delta_order_groups,
                    delta_driver_states,
                    stp,
                    stp_time_minutes
                )
                
                delta_results[delta] = ogah_result
                
            
            # 选择最优δ
            if delta_results:
                best_delta = min(delta_results.keys(), 
                               key=lambda d: delta_results[d]['total_cost'])
                best_ogah_result = delta_results[best_delta]
                
                
                # 更新全局司机状态（使用最优结果）
                for assignment in best_ogah_result['assignments']:
                    driver_id = assignment['driver_id']
                    delivery_info = assignment['delivery_info']
                    driver_states[driver_id] = {
                        'lng': delivery_info['final_lng'],
                        'lat': delivery_info['final_lat'],
                        'available_time': delivery_info['completion_time']
                    }
                
                # 保存结果，包含所有δ值的信息
                all_ogah_results[stp] = {
                    'best_delta': best_delta,
                    'best_result': best_ogah_result,
                    'all_delta_results': delta_results
                }
                
                
        # 保存结果
        self.save_ogah_results(all_ogah_results)
        
        
        return all_ogah_results
    
    def save_ogah_results(self, all_ogah_results):
        """保存OGAH结果到JSON文件"""
        
        # 准备可序列化的结果
        serializable_results = {}
        
        for stp, result in all_ogah_results.items():
            best_result = result['best_result']
            
            serializable_stp_result = {
                'stp': stp,
                'best_delta': result['best_delta'],
                'total_cost': round(best_result['total_cost'], 2),
                'assigned_count': best_result['assigned_count'],
                'concatenated_count': best_result['concatenated_count'],
                'assignments': [],
                'all_delta_results': {}
            }
            
            # 保存最优结果的详细分配信息
            for assignment in best_result['assignments']:
                assignment_data = {
                    'order_group_idx': assignment['order_group_idx'],
                    'driver_id': assignment['driver_id'],
                    'cost': round(assignment['cost'], 2),
                    'travel_cost': round(assignment.get('travel_cost', 0), 2),
                    'penalty_cost': round(assignment.get('penalty_cost', 0), 2),
                    'is_concatenated': assignment.get('is_concatenated', False),
                    'delivery_info': {
                        'delivery_duration': round(assignment['delivery_info']['delivery_duration'], 2),
                        'completion_time': round(assignment['delivery_info']['completion_time'], 2),
                        'final_position': {
                            'lng': assignment['delivery_info']['final_lng'],
                            'lat': assignment['delivery_info']['final_lat']
                        }
                    }
                }
                
                # 添加订单组详情（如果存在）
                if 'order_group' in assignment:
                    assignment_data['order_group_details'] = {
                        'group_id': assignment['order_group']['group_id'],
                        'size': assignment['order_group']['size'],
                        'orders': [order['order_id'] for order in assignment['order_group']['orders']]
                    }
                
                serializable_stp_result['assignments'].append(assignment_data)
            
            # 保存所有delta值的结果统计
            for delta, delta_result in result['all_delta_results'].items():
                serializable_stp_result['all_delta_results'][str(delta)] = {
                    'total_cost': round(delta_result['total_cost'], 2),
                    'assigned_count': delta_result['assigned_count'],
                    'concatenated_count': delta_result['concatenated_count'],
                    'assignments_count': len(delta_result['assignments'])
                }
            
            serializable_results[stp] = serializable_stp_result
        
        # 保存详细结果
        with open('ogah_results.json', 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        
    
    def get_algorithm_data(self):
        """获取算法所需的基础数据，供其他算法使用"""
        return {
            'driver_positions': self.driver_positions,
            'order_positions': self.order_positions,
            'ogsa_results': self.ogsa_results,
            'oggm_results': self.oggm_results,
            'reconstructed_order_groups': self.reconstructed_order_groups,
            'algorithm_parameters': {
                'alpha': self.alpha,
                'beta': self.beta,
                'epsilon': self.epsilon,
                'delta_max': self.delta_max,
                'delta_bar': self.delta_bar,
                'rider_speed_kmh': self.rider_speed_kmh,
                'num_drivers': self.num_drivers
            }
        }
