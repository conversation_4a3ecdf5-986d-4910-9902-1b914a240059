import math
import copy
from typing import List, Dict, Tuple, Any, Set
import numpy as np
import gurobipy as gp
from gurobipy import GRB

# === RG2 等价鲁棒成本计算函数 ===
# 该函数直接复用 Robust SOCDA 2.py 中的实现，用于评估序列在最坏情况下的旅行与延迟成本。
# 不强制骑手返仓，配送完最后一单即结束。

def compute_rg2_robust_cost(sequence, start_pos, start_time, slot,
                             restaurant_coords, customer_coords,
                             order_restaurants,
                             driver_speed, slot_duration,
                             theta_t, deviation_ratio,
                             travel_cost_per_unit, penalty_cost_per_unit,
                             est_delivery_duration, depot_coord):
    """返回 (robust_travel_cost, robust_penalty_cost, total_cost, final_pos, final_time)"""
    if not sequence:
        return 0.0, 0.0, 0.0, start_pos, start_time

    arcs = []
    deliveries = []  # (cust_id, arc_idx)
    cur = start_pos
    for node in sequence:
        if node.startswith("pickup_"):
            idx = int(node.split("_")[1])
            rest_id = order_restaurants[idx]
            nxt = restaurant_coords[rest_id]
        elif node.startswith("delivery_"):
            idx = int(node.split("_")[1])
            nxt = customer_coords[idx]
            deliveries.append((idx, len(arcs)))
        else:
            continue
        arcs.append((cur, nxt))
        cur = nxt

    nominal_times, deviations = [], []
    for a, b in arcs:
        dist = math.hypot(a[0] - b[0], a[1] - b[1])
        t_nom = dist / driver_speed
        nominal_times.append(t_nom)
        deviations.append(t_nom * deviation_ratio)

    positive_arc_count = sum(1 for t in nominal_times if t > 1e-8)
    gamma = math.ceil(theta_t * positive_arc_count)

    prefix_nom = 0.0
    dev_prefix = []
    arrival_worst = {}
    for arc_idx, (t_nom, dev) in enumerate(zip(nominal_times, deviations)):
        prefix_nom += t_nom
        dev_prefix.append(dev)
        worst_extra_prefix = sum(sorted(dev_prefix, reverse=True)[:gamma])
        time_now = start_time + prefix_nom + worst_extra_prefix
        for cust_id, idx_in_arc in deliveries:
            if idx_in_arc == arc_idx:
                arrival_worst[cust_id] = time_now

    worst_extra_total = sum(sorted(deviations, reverse=True)[:gamma])
    worst_total_time = start_time + sum(nominal_times) + worst_extra_total

    final_service_pos = arcs[-1][1] if arcs else start_pos
    final_service_time = worst_total_time

    robust_travel_cost = (worst_total_time - start_time) * travel_cost_per_unit

    slot_start_time = (slot - 1) * slot_duration
    robust_penalty_cost = 0.0
    for cust_id, worst_arrival in arrival_worst.items():
        target = slot_start_time + est_delivery_duration[cust_id]
        delay = max(worst_arrival - target, 0)
        robust_penalty_cost += delay * penalty_cost_per_unit

    total_cost = robust_travel_cost + robust_penalty_cost
    return robust_travel_cost, robust_penalty_cost, total_cost, final_service_pos, final_service_time

class OrderGroup:
    """订单组类，包含订单序列和相关信息"""
    def __init__(self, orders: List[int], sequence: List[int], starting_order: int):
        self.orders = orders  # 订单ID列表
        self.sequence = sequence  # 访问序列（包含取餐点和送餐点）
        self.starting_order = starting_order  # 起始订单ID
        self.virtual_driver_id = None  # 虚拟司机ID
        self.estimated_cost = 0.0  # 预估成本
        self.dispatch_cost = 0.0  # 调度成本
        
    def __len__(self):
        return len(self.orders)
        
    def contains_order(self, order_id: int) -> bool:
        return order_id in self.orders
    
    def get_orders_set(self) -> Set[int]:
        """返回订单集合"""
        return set(self.orders)

class OGGM:
    """订单组生成方法 (Order Group Generation Method) - 修正版"""
    
    def __init__(self, food_delivery_optimizer):
        """
        初始化OGGM
        
        Args:
            food_delivery_optimizer: FoodDeliveryOptimizer实例，提供所有必要参数
        """
        self.optimizer = food_delivery_optimizer
        
        # 从优化器获取参数（确保与Gurobi.py一致）
        self.commission_rate = food_delivery_optimizer.commission_rate
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.slot_duration = food_delivery_optimizer.slot_duration
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.max_orders_per_driver = food_delivery_optimizer.max_orders_per_driver
        
        # 空间和订单信息
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.customer_coords = food_delivery_optimizer.customer_coords
        self.driver_start_coords = food_delivery_optimizer.driver_start_coords
        self.order_values = food_delivery_optimizer.order_values
        self.order_restaurants = food_delivery_optimizer.order_restaurants
        self.estimated_delivery_duration = food_delivery_optimizer.estimated_delivery_duration
        
        # OGGM特定参数 - 修正
        self.delta = 1  # 虚拟司机计算时考虑的最近司机数量
        self.delay_threshold = 0.3  # 延迟度阈值μ（调整为更合理的值）
        
        print("OGGM初始化完成 - 修正版")
        print(f"参数设置 - 旅行成本: {self.travel_cost_per_unit}, 延迟惩罚: {self.penalty_cost_per_unit}")
        print(f"最大订单数: {self.max_orders_per_driver}, 延迟阈值: {self.delay_threshold}")
    
    def calculate_distance(self, coord1: Tuple[float, float], coord2: Tuple[float, float]) -> float:
        """
        计算两点间的欧几里得距离（与Gurobi.py一致）
        """
        return math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)
    
    def get_order_coordinates(self, order_id: int) -> Tuple[Tuple[float, float], Tuple[float, float]]:
        """获取订单的餐厅和顾客坐标"""
        restaurant_id = self.order_restaurants[order_id]
        restaurant_coord = self.restaurant_coords[restaurant_id]
        customer_coord = self.customer_coords[order_id]
        return restaurant_coord, customer_coord
    
    def calculate_single_order_distance(self, order_id: int, driver_start: Tuple[float, float]) -> float:
        """计算单独配送一个订单的总距离：起点→餐厅→顾客"""
        restaurant_coord, customer_coord = self.get_order_coordinates(order_id)
        
        dist_to_restaurant = self.calculate_distance(driver_start, restaurant_coord)
        dist_restaurant_to_customer = self.calculate_distance(restaurant_coord, customer_coord)
        
        return dist_to_restaurant + dist_restaurant_to_customer
    
    def calculate_two_orders_combined_distance(self, order1_id: int, order2_id: int, 
                                            driver_start: Tuple[float, float]) -> Tuple[float, float]:
        """
        计算两个订单合并配送的最短距离（按照附录J的公式J.1和J.2）
        修正版：确保正确计算所有可能路径
        """
        rest1_coord, cust1_coord = self.get_order_coordinates(order1_id)
        rest2_coord, cust2_coord = self.get_order_coordinates(order2_id)
        
        # 机制1：固定order1的餐厅为第一个访问节点
        dist_start_to_r1 = self.calculate_distance(driver_start, rest1_coord)
        
        # 三种可能的序列（对应Figure 10）
        sequences_order1_first = [
            # 序列1: 起点→r1→r2→c2→c1  
            dist_start_to_r1 + 
            self.calculate_distance(rest1_coord, rest2_coord) +
            self.calculate_distance(rest2_coord, cust2_coord) +
            self.calculate_distance(cust2_coord, cust1_coord),
            
            # 序列2: 起点→r1→r2→c1→c2
            dist_start_to_r1 + 
            self.calculate_distance(rest1_coord, rest2_coord) +
            self.calculate_distance(rest2_coord, cust1_coord) +
            self.calculate_distance(cust1_coord, cust2_coord),
            
            # 序列3: 起点→r1→c1→r2→c2
            dist_start_to_r1 + 
            self.calculate_distance(rest1_coord, cust1_coord) +
            self.calculate_distance(cust1_coord, rest2_coord) +
            self.calculate_distance(rest2_coord, cust2_coord)
        ]
        
        dist_order1_first = min(sequences_order1_first)
        
        # 机制2：固定order2的餐厅为第一个访问节点
        dist_start_to_r2 = self.calculate_distance(driver_start, rest2_coord)
        
        sequences_order2_first = [
            # 序列1: 起点→r2→r1→c1→c2
            dist_start_to_r2 + 
            self.calculate_distance(rest2_coord, rest1_coord) +
            self.calculate_distance(rest1_coord, cust1_coord) +
            self.calculate_distance(cust1_coord, cust2_coord),
            
            # 序列2: 起点→r2→r1→c2→c1
            dist_start_to_r2 + 
            self.calculate_distance(rest2_coord, rest1_coord) +
            self.calculate_distance(rest1_coord, cust2_coord) +
            self.calculate_distance(cust2_coord, cust1_coord),
            
            # 序列3: 起点→r2→c2→r1→c1
            dist_start_to_r2 + 
            self.calculate_distance(rest2_coord, cust2_coord) +
            self.calculate_distance(cust2_coord, rest1_coord) +
            self.calculate_distance(rest1_coord, cust1_coord)
        ]
        
        dist_order2_first = min(sequences_order2_first)
        
        return dist_order1_first, dist_order2_first
    
    def calculate_order_matching_degree(self, order1_id: int, order2_id: int, 
                                      virtual_driver1_pos: Tuple[float, float],
                                      virtual_driver2_pos: Tuple[float, float]) -> Tuple[float, float]:
        """
        计算两个订单的匹配度（按照附录J公式J.3）
        修正版：确保计算逻辑正确
        """
        # 分别配送的总距离
        dist1_separate = self.calculate_single_order_distance(order1_id, virtual_driver1_pos)
        dist2_separate = self.calculate_single_order_distance(order2_id, virtual_driver2_pos)
        total_separate_distance = dist1_separate + dist2_separate
        
        # 合并配送的距离 - 使用order1的虚拟司机位置
        dist_order1_first, dist_order2_first = self.calculate_two_orders_combined_distance(
            order1_id, order2_id, virtual_driver1_pos)
        
        # 计算匹配度（距离节省）
        fit_o1_o2 = total_separate_distance - dist_order1_first
        fit_o2_o1 = total_separate_distance - dist_order2_first
        
        return fit_o1_o2, fit_o2_o1
    
    def cheapest_insertion_single_order(self, sequence: List[str], new_order_id: int, 
                                      current_start_pos: Tuple[float, float]) -> Tuple[List[str], float]:
        """
        使用最便宜插入法将单个订单插入到现有序列中
        修正版：正确处理起始位置
        """
        if not sequence:
            # 空序列，直接创建新序列
            new_sequence = [f"pickup_{new_order_id}", f"delivery_{new_order_id}"]
            return new_sequence, 0.0
        
        best_sequence = None
        best_cost = float('inf')
        
        # 获取新订单的取餐点和送餐点
        pickup_node = f"pickup_{new_order_id}"
        delivery_node = f"delivery_{new_order_id}"
        
        # 尝试所有可能的插入位置
        for pickup_pos in range(len(sequence) + 1):
            for delivery_pos in range(pickup_pos + 1, len(sequence) + 2):
                # 创建新序列
                temp_sequence = sequence.copy()
                temp_sequence.insert(pickup_pos, pickup_node)
                temp_sequence.insert(delivery_pos, delivery_node)
                
                # 计算插入成本
                insertion_cost = self.calculate_sequence_distance_increase(
                    sequence, temp_sequence, current_start_pos)
                
                if insertion_cost < best_cost:
                    best_cost = insertion_cost
                    best_sequence = temp_sequence
        
        return best_sequence, best_cost
    
    def calculate_sequence_distance_increase(self, old_sequence: List[str], 
                                           new_sequence: List[str], 
                                           start_pos: Tuple[float, float]) -> float:
        """计算序列变化导致的距离增加"""
        old_distance = self.calculate_sequence_total_distance(old_sequence, start_pos)
        new_distance = self.calculate_sequence_total_distance(new_sequence, start_pos)
        return new_distance - old_distance
    
    def calculate_sequence_total_distance(self, sequence: List[str], 
                                        start_pos: Tuple[float, float]) -> float:
        """
        计算序列的总配送距离
        修正版：正确处理起始位置
        """
        if len(sequence) <= 1:
            return 0.0
        
        total_distance = 0.0
        current_pos = start_pos
        
        for node in sequence:
            # 解析节点信息
            if node.startswith("pickup_"):
                order_id = int(node.split("_")[1])
                restaurant_id = self.order_restaurants[order_id]
                next_pos = self.restaurant_coords[restaurant_id]
            elif node.startswith("delivery_"):
                order_id = int(node.split("_")[1])
                next_pos = self.customer_coords[order_id]
            else:
                continue
            
            # 累加距离
            total_distance += self.calculate_distance(current_pos, next_pos)
            current_pos = next_pos
        
        return total_distance
    
    def estimate_sequence_delivery_times(self, sequence: List[str], start_time: float, 
                                       start_pos: Tuple[float, float]) -> Dict[int, float]:
        """
        估算序列中各订单的送达时间
        修正版：正确处理时间和位置
        """
        delivery_times = {}
        current_time = start_time
        current_pos = start_pos
        
        for i, node in enumerate(sequence):
            if node.startswith("pickup_"):
                order_id = int(node.split("_")[1])
                restaurant_id = self.order_restaurants[order_id] 
                next_pos = self.restaurant_coords[restaurant_id]
            elif node.startswith("delivery_"):
                order_id = int(node.split("_")[1])
                next_pos = self.customer_coords[order_id]
            else:
                continue
            
            # 计算旅行时间并更新位置和时间
            travel_distance = self.calculate_distance(current_pos, next_pos)
            travel_time = travel_distance / self.driver_speed
            current_time += travel_time
            current_pos = next_pos
            
            # 如果是送餐节点，记录送达时间
            if node.startswith("delivery_"):
                delivery_times[order_id] = current_time
        
        return delivery_times
    
    def calculate_order_delay_degree(self, order_id: int, actual_delivery_time: float, 
                                   slot_start_time: float) -> float:
        """
        计算订单延迟度
        修正版：正确实现延迟度计算逻辑
        """
        # 目标配送时间 = 时段开始时间 + 预期配送时长
        target_delivery_time = slot_start_time + self.estimated_delivery_duration[order_id]
        
        # 延迟时间（如果实际时间超过目标时间）
        delay_time = max(actual_delivery_time - target_delivery_time, 0)
        
        # 延迟度 = 延迟时间 / 预期配送时长
        if self.estimated_delivery_duration[order_id] > 0:
            delay_degree = delay_time / self.estimated_delivery_duration[order_id]
        else:
            delay_degree = 0.0
        
        return delay_degree
    
    def calculate_max_delay_degree_in_sequence(self, sequence: List[str], start_time: float, 
                                             slot_start_time: float, 
                                             start_pos: Tuple[float, float]) -> float:
        """
        计算序列中所有订单的最大延迟度
        修正版：正确处理时间计算
        """
        delivery_times = self.estimate_sequence_delivery_times(sequence, start_time, start_pos)
        
        max_delay_degree = 0.0
        for order_id, delivery_time in delivery_times.items():
            delay_degree = self.calculate_order_delay_degree(order_id, delivery_time, slot_start_time)
            max_delay_degree = max(max_delay_degree, delay_degree)
        
        return max_delay_degree
    
    def get_virtual_driver_info(self, order_id: int, slot: int, 
                              actual_start_pos: Tuple[float, float] = None,
                              actual_start_time: float = None) -> Tuple[Tuple[float, float], float]:
        """
        为订单确定虚拟司机信息
        修正版：允许传入实际的司机起始位置和时间
        """
        # 如果未提供实际起始状态，则使用默认（通常是时段开始时或原始起点）
        virtual_position = actual_start_pos if actual_start_pos is not None else self.driver_start_coords[0]
        
        # 虚拟司机开始时间：如果提供了实际开始时间，则使用它，否则使用时段的名义开始时间
        nominal_slot_start_time = (slot - 1) * self.slot_duration
        virtual_start_time = actual_start_time if actual_start_time is not None else nominal_slot_start_time
        
        return virtual_position, virtual_start_time
    
    def generate_order_groups_for_slot(self, orders_in_slot: List[int], slot: int,
                                     actual_driver_start_pos: Tuple[float, float] = None,
                                     actual_driver_start_time: float = None) -> List[OrderGroup]:
        """
        为指定时段生成所有可能的订单组
        修正版：使用实际的司机起始位置和时间
        """
        print(f"\n=== 开始为时段{slot}生成订单组（修正版）===")
        print(f"时段{slot}订单: {orders_in_slot}")
        if actual_driver_start_pos and actual_driver_start_time is not None:
            print(f"使用实际司机起点: Pos={actual_driver_start_pos}, Time={actual_driver_start_time:.2f}")

        if not orders_in_slot:
            return []
        
        all_order_groups = []
        # 使用实际的或名义上的时段开始时间进行延迟度计算
        delay_calc_slot_start_time = actual_driver_start_time if actual_driver_start_time is not None else (slot - 1) * self.slot_duration


        print(f"时段{slot}用于延迟计算的开始时间: {delay_calc_slot_start_time:.2f}")
        
        # Step 1 & 2: 为每个订单初始化并获取虚拟司机信息
        
        base_start_pos, base_start_time = self.get_virtual_driver_info(
            orders_in_slot[0], slot, actual_driver_start_pos, actual_driver_start_time
        )


        # Step 3: 计算所有订单对的匹配度矩阵
        # 匹配度计算应使用统一的起始点（即实际司机起点）
        matching_matrix = {}
        for order1 in orders_in_slot:
            for order2 in orders_in_slot:
                if order1 != order2:
                    # calculate_order_matching_degree 使用的 virtual_driver_pos 应该是实际的司机起始位置
                    # 这里假设两个订单的匹配度计算基于同一个起点（实际司机起点）
                    fit_12, fit_21 = self.calculate_order_matching_degree(order1, order2, base_start_pos, base_start_pos)
                    matching_matrix[(order1, order2)] = fit_12
                    matching_matrix[(order2, order1)] = fit_21
        
        # Step 4-8: 对每个订单作为起始订单进行扩展
        for starting_order in orders_in_slot:
            # 起始订单的序列和扩展，都应该基于实际的司机起始位置和时间
            # start_pos, start_time = virtual_driver_info[starting_order] # OLD
            current_actual_start_pos = base_start_pos
            current_actual_start_time = base_start_time
            
            # 初始化
            current_order_cluster = {starting_order}
            current_sequence = [f"pickup_{starting_order}", f"delivery_{starting_order}"]
            remaining_orders = set(orders_in_slot) - {starting_order}
            
            # 创建初始订单组（只包含起始订单）
            initial_og = OrderGroup([starting_order], current_sequence.copy(), starting_order)
            all_order_groups.append(initial_og)
            
            # 尝试扩展订单组
            while remaining_orders and len(current_order_cluster) < self.max_orders_per_driver:
                # Step 5: 计算剩余订单与当前订单组的匹配度 (已在 matching_matrix 中基于 base_start_pos 计算)
                order_matching_scores = []
                for order_id in remaining_orders:
                    total_fit = 0.0
                    count = 0
                    for existing_order in current_order_cluster:
                        # 使用预先计算的基于共同起点的匹配度
                        fit_key = (existing_order, order_id) # 或者 (order_id, existing_order)
                        if (existing_order, order_id) in matching_matrix:
                             total_fit += matching_matrix[(existing_order, order_id)]
                             count +=1
                        if (order_id, existing_order) in matching_matrix: # 也考虑反向
                             total_fit += matching_matrix[(order_id, existing_order)]
                             count +=1

                    avg_fit = total_fit / count if count > 0 else 0.0
                    order_matching_scores.append((order_id, avg_fit))
                
                order_matching_scores.sort(key=lambda x: x[1], reverse=True)
                
                if not order_matching_scores:
                    break
                
                best_order, best_fit = order_matching_scores[0]
                
                if best_fit <= 0 and len(current_order_cluster) > 0 : #如果只有一个订单，匹配度可以是0
                    # print(f"    匹配度 {best_fit:.3f} <= 0，停止扩展订单组 {current_order_cluster}")
                    break
                
                # Step 8: 使用最便宜插入法将订单加入序列
                # 插入和延迟计算都使用实际的司机起始位置和时间
                new_sequence, insertion_cost = self.cheapest_insertion_single_order(
                    current_sequence, best_order, current_actual_start_pos)
                
                max_delay_degree = self.calculate_max_delay_degree_in_sequence(
                    new_sequence, current_actual_start_time, delay_calc_slot_start_time, current_actual_start_pos)
                
                if max_delay_degree > self.delay_threshold:
                    # print(f"    订单 {best_order} 加入导致最大延迟 {max_delay_degree:.3f} > {self.delay_threshold}，拒绝插入")
                    # 从remaining_orders中移除此订单，不再尝试它，因为它会导致超时
                    remaining_orders.remove(best_order)
                    if not remaining_orders: # 如果没有其他可尝试的订单了
                        break
                    else: # 否则，尝试下一个匹配度最高的订单
                        continue # 跳过下面的接受步骤，尝试下一个订单


                current_order_cluster.add(best_order)
                current_sequence = new_sequence
                remaining_orders.remove(best_order)
                
                new_og = OrderGroup(list(current_order_cluster), current_sequence.copy(), starting_order)
                all_order_groups.append(new_og)
        
        print(f"\n=== 时段{slot}订单组生成完成，共生成{len(all_order_groups)}个订单组 ===")
        
        # 去重，因为相同的订单组合可能从不同的starting_order生成
        unique_order_groups = []
        seen_order_sets = set()
        for og in sorted(all_order_groups, key=lambda x: (len(x.orders), tuple(sorted(x.orders)))):
            orders_tuple = tuple(sorted(og.orders))
            if orders_tuple not in seen_order_sets:
                unique_order_groups.append(og)
                seen_order_sets.add(orders_tuple)
        
        print(f"去重后剩余 {len(unique_order_groups)} 个订单组。")
        return unique_order_groups

class Driver:
    """司机状态类"""
    def __init__(self, driver_id: int, start_pos: Tuple[float, float], start_time: float, capacity: int):
        self.driver_id = driver_id
        self.current_pos = start_pos
        self.current_time = start_time
        self.remaining_capacity = capacity
        self.assigned_orders = []
        self.total_cost = 0.0
        self.total_distance = 0.0
        
    def can_handle_order_group(self, order_group) -> bool:
        """检查司机是否能处理该订单组"""
        return self.remaining_capacity >= len(order_group)
    
    def assign_order_group(self, order_group, execution_cost: float, new_pos: Tuple[float, float], 
                          new_time: float, distance: float):
        """分配订单组给司机"""
        self.assigned_orders.extend(order_group.orders)
        self.remaining_capacity -= len(order_group)
        self.total_cost += execution_cost
        self.total_distance += distance
        self.current_pos = new_pos
        self.current_time = new_time

class OGAH:
    """订单组分配启发式算法 (Order Group Assignment Heuristic) - 作为Gurobi模型的辅助"""
    
    def __init__(self, food_delivery_optimizer):
        self.optimizer = food_delivery_optimizer
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.slot_duration = food_delivery_optimizer.slot_duration
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.max_orders_per_driver = food_delivery_optimizer.max_orders_per_driver
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.customer_coords = food_delivery_optimizer.customer_coords
        self.driver_start_coords = food_delivery_optimizer.driver_start_coords
        self.order_restaurants = food_delivery_optimizer.order_restaurants
        self.estimated_delivery_duration = food_delivery_optimizer.estimated_delivery_duration
        self.num_drivers = food_delivery_optimizer.num_drivers
        self.current_driver_states = None
    
    def calculate_distance(self, coord1: Tuple[float, float], coord2: Tuple[float, float]) -> float:
        return math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)
    
    def calculate_sequence_execution_details(self, sequence: List[str], driver_start_pos: Tuple[float, float], 
                                           driver_start_time: float, slot: int) -> Tuple[float, float, Tuple[float, float], float]:
        if not sequence:
            return 0.0, 0.0, driver_start_pos, driver_start_time
        
        current_pos = driver_start_pos
        current_time = driver_start_time
        total_distance = 0.0
        total_penalty = 0.0
        slot_start_time = (slot - 1) * self.slot_duration
        
        for i, node in enumerate(sequence):
            if node.startswith("pickup_"):
                order_id = int(node.split("_")[1])
                restaurant_id = self.order_restaurants[order_id]
                next_pos = self.restaurant_coords[restaurant_id]
            elif node.startswith("delivery_"):
                order_id = int(node.split("_")[1])
                next_pos = self.customer_coords[order_id]
            else:
                continue
            
            travel_distance = self.calculate_distance(current_pos, next_pos)
            travel_time = travel_distance / self.driver_speed
            
            total_distance += travel_distance
            current_time += travel_time
            current_pos = next_pos
            
            if node.startswith("delivery_"):
                target_delivery_time = slot_start_time + self.estimated_delivery_duration[order_id]
                delay_time = max(current_time - target_delivery_time, 0)
                total_penalty += delay_time * self.penalty_cost_per_unit
        
        travel_cost = total_distance * self.travel_cost_per_unit
        
        return travel_cost, total_penalty, current_pos, current_time
    
    def calculate_driver_dispatch_cost(self, order_group, driver: Driver, slot: int) -> Tuple[float, Dict]:
        if not driver.can_handle_order_group(order_group):
            return float('inf'), {}
        
        travel_cost, penalty_cost, final_pos, final_time = self.calculate_sequence_execution_details(
            order_group.sequence, driver.current_pos, driver.current_time, slot)
        
        total_cost = travel_cost + penalty_cost
        
        execution_details = {
            'travel_cost': travel_cost,
            'penalty_cost': penalty_cost,
            'total_distance': travel_cost / self.travel_cost_per_unit if self.travel_cost_per_unit > 0 else 0,
            'final_pos': final_pos,
            'final_time': final_time
        }
        
        return total_cost, execution_details
    
    def calculate_regret_value(self, dispatch_costs: List[float]) -> float:
        if len(dispatch_costs) < 2:
            return 0.0
        
        costs = sorted(dispatch_costs)
        best_cost = costs[0]
        
        regret_value = 0.0
        for i in range(1, min(4, len(costs))):
            regret_value += costs[i] - best_cost
        
        return regret_value
    
    def initialize_drivers(self, slot: int, initial_states: List[Driver] = None) -> List[Driver]:
        drivers = []
        slot_start_time = (slot - 1) * self.slot_duration
        
        if initial_states:
            for i, prev_driver_state in enumerate(initial_states):
                start_time_for_current_slot = max(prev_driver_state.current_time, slot_start_time)
                driver = Driver(
                    driver_id=prev_driver_state.driver_id,
                    start_pos=prev_driver_state.current_pos, 
                    start_time=start_time_for_current_slot,
                    capacity=self.max_orders_per_driver
                )
                driver.total_cost = 0.0
                driver.total_distance = 0.0
                driver.assigned_orders = []
                driver.remaining_capacity = self.max_orders_per_driver
                drivers.append(driver)
        else:
            for driver_id in range(self.num_drivers):
                start_pos = self.driver_start_coords[driver_id] if driver_id in self.driver_start_coords else self.driver_start_coords[0]
                driver = Driver(driver_id, start_pos, slot_start_time, self.max_orders_per_driver)
                drivers.append(driver)
        
        return drivers
    
    def assign_order_groups_to_drivers(self, selected_order_groups: List, slot: int, initial_driver_states: List[Driver] = None) -> Tuple[float, List[Driver], Dict]:
        drivers = self.initialize_drivers(slot, initial_driver_states)
        self.current_driver_states = drivers
        
        if not selected_order_groups:
             # 返回初始化的司机状态和零成本
             # 这确保了即使没有订单，司机状态（尤其是时间）也能正确地传递到下一时段
             return 0.0, drivers, {'assignments': [], 'total_travel_cost': 0.0, 'total_penalty_cost': 0.0}

        available_drivers = drivers.copy()
        unassigned_order_groups = selected_order_groups.copy()
        assignment_details = {
            'assignments': [], 'total_travel_cost': 0.0, 'total_penalty_cost': 0.0, 'unassigned_groups': []
        }
        
        while unassigned_order_groups and available_drivers:
            og_evaluations = []
            for og_idx, order_group in enumerate(unassigned_order_groups):
                driver_costs = []
                driver_details = []
                for driver in available_drivers:
                    cost, details = self.calculate_driver_dispatch_cost(order_group, driver, slot)
                    driver_costs.append(cost)
                    driver_details.append((driver, details))
                
                valid_assignments = [(cost, driver, details) for cost, (driver, details) in 
                                   zip(driver_costs, driver_details) if cost < float('inf')]
                
                if valid_assignments:
                    valid_assignments.sort(key=lambda x: x[0])
                    costs_only = [cost for cost, _, _ in valid_assignments]
                    regret_value = self.calculate_regret_value(costs_only)
                    best_cost, best_driver, best_details = valid_assignments[0]
                    og_evaluations.append({
                        'order_group': order_group, 'regret_value': regret_value, 'best_cost': best_cost,
                        'best_driver': best_driver, 'best_details': best_details
                    })
            
            if not og_evaluations:
                break
            
            og_evaluations.sort(key=lambda x: x['regret_value'], reverse=True)
            best_assignment = og_evaluations[0]
            
            selected_og = best_assignment['order_group']
            selected_driver = best_assignment['best_driver']
            execution_details = best_assignment['best_details']
            
            selected_driver.assign_order_group(
                selected_og, best_assignment['best_cost'], execution_details['final_pos'],
                execution_details['final_time'], execution_details['total_distance']
            )
            
            assignment_details['assignments'].append({
                'order_group': selected_og, 'driver_id': selected_driver.driver_id,
                'cost': best_assignment['best_cost'], 'travel_cost': execution_details['travel_cost'],
                'penalty_cost': execution_details['penalty_cost']
            })
            assignment_details['total_travel_cost'] += execution_details['travel_cost']
            assignment_details['total_penalty_cost'] += execution_details['penalty_cost']
            
            unassigned_order_groups.remove(selected_og)
            if selected_driver.remaining_capacity <= 0:
                available_drivers.remove(selected_driver)
        
        if unassigned_order_groups:
            if drivers:
                best_driver = min(drivers, key=lambda d: d.total_cost)
                for og in unassigned_order_groups:
                    best_driver.assigned_orders.extend(og.orders)
                    assignment_details['unassigned_groups'].append(og)
        
        total_cost = assignment_details['total_travel_cost'] + assignment_details['total_penalty_cost']
        return total_cost, drivers, assignment_details

class GurobiSelectorAssigner:
    """
    使用Gurobi精确求解器替代OGSA和OGAH，实现订单组的最优选择和分配。
    该模型解决的是一个结合了集合划分和车辆路径问题的复杂优化问题。
    """
    def __init__(self, food_delivery_optimizer):
        self.optimizer = food_delivery_optimizer
        # 共享参数
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.slot_duration = food_delivery_optimizer.slot_duration
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.max_orders_per_driver = food_delivery_optimizer.max_orders_per_driver
        self.num_drivers = food_delivery_optimizer.num_drivers
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.customer_coords = food_delivery_optimizer.customer_coords
        self.order_restaurants = food_delivery_optimizer.order_restaurants
        self.estimated_delivery_duration = food_delivery_optimizer.estimated_delivery_duration
        self.driver_start_coords = food_delivery_optimizer.driver_start_coords
        # 鲁棒参数（若优化器未显式提供则使用默认值）
        self.theta_t = getattr(food_delivery_optimizer, 'theta_t', 0.3)
        self.travel_time_deviation_ratio = getattr(food_delivery_optimizer, 'travel_time_deviation_ratio', 0.2)

        print("GurobiSelectorAssigner初始化完成")

    def calculate_distance(self, coord1: Tuple[float, float], coord2: Tuple[float, float]) -> float:
        """计算两点间的欧几里得距离"""
        return math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)
    
    def get_node_pos(self, node_str: str) -> Tuple[float, float]:
        """从节点字符串获取坐标"""
        parts = node_str.split('_')
        order_id = int(parts[1])
        if parts[0] == "pickup":
            restaurant_id = self.order_restaurants[order_id]
            return self.restaurant_coords[restaurant_id]
        else: # delivery
            return self.customer_coords[order_id]

    def calculate_sequence_details(self, sequence: List[str], start_pos: Tuple[float, float], 
                                   start_time: float, slot: int) -> Dict:
        """基于 RG2 递推公式返回鲁棒成本及执行后的司机状态"""
        robust_travel, robust_penalty, robust_total, final_pos, final_time = compute_rg2_robust_cost(
            sequence,
            start_pos,
            start_time,
            slot,
            self.restaurant_coords,
            self.customer_coords,
            self.order_restaurants,
            self.driver_speed,
            self.slot_duration,
            self.theta_t,
            self.travel_time_deviation_ratio,
            self.travel_cost_per_unit,
            self.penalty_cost_per_unit,
            self.estimated_delivery_duration,
            self.driver_start_coords[0]
        )

        duration = final_time - start_time

        return {
            'travel_cost': robust_travel,
            'penalty_cost': robust_penalty,
            'total_cost': robust_total,
            'duration': duration,
            'final_pos': final_pos,
            'final_time': final_time,
            'delivery_times': {}  # 最坏到达时间已包含在成本中，此处保留占位
        }

    def solve(self, order_groups: List[OrderGroup], orders_in_slot: List[int], slot: int, 
              initial_driver_states: List['Driver'] = None) -> Tuple[float, List['Driver'], Dict]:
        """
        使用Gurobi构建并求解VRP-SP模型
        """
        print(f"\n--- GurobiSelectorAssigner开始为时段{slot}求解 ---")
        if not order_groups or not orders_in_slot:
            # OGAH的initialize_drivers能够处理空订单的情况，并正确传递司机状态
            ogah_fallback = OGAH(self.optimizer)
            return ogah_fallback.assign_order_groups_to_drivers([], slot, initial_driver_states)

        # 1. 初始化司机状态（借用OGAH逻辑，因为本案例只有1个司机）
        ogah_helper = OGAH(self.optimizer)
        drivers = ogah_helper.initialize_drivers(slot, initial_driver_states)
        initial_driver = drivers[0] # 单司机场景

        # 2. 预计算成本和时间矩阵
        num_groups = len(order_groups)
        
        # 预计算每个订单组的内部信息
        group_details = {}
        for i, og in enumerate(order_groups):
            # 修正：内部指标的计算不应依赖于司机的初始位置，而应基于组本身的序列
            first_node_pos = self.get_node_pos(og.sequence[0])
            # 内部成本/时间是从第一个节点开始的路径成本
            # start_time=0因为我们只关心相对时长和内部延迟，绝对延迟由模型中的a[i]变量处理
            internal_details = self.calculate_sequence_details(og.sequence, first_node_pos, 0, slot)
            
            group_details[i] = {
                'internal_travel_cost': internal_details['travel_cost'],
                'internal_total_cost': internal_details['total_cost'],
                'internal_duration': internal_details['duration'],
                'sequence_start_pos': first_node_pos,
                'sequence_end_pos': self.get_node_pos(og.sequence[-1]),
                'orders': og.orders,
                'num_orders': len(og.orders)
            }
        
        model = gp.Model(f"VRP_SP_Slot_{slot}")
        model.setParam('OutputFlag', 0)

        # 3. 决策变量
        # x_ij = 1 如果从i组到j组 (i,j in {-1} U {0..num_groups-1})
        x = model.addVars(num_groups + 1, num_groups + 1, vtype=GRB.BINARY, name="x")
        
        # y_i = 1 如果i组被选中
        y = model.addVars(num_groups, vtype=GRB.BINARY, name="y")

        # a_i: 开始执行i组的时间
        a = model.addVars(num_groups, vtype=GRB.CONTINUOUS, lb=0, name="a")
        
        # 4. 目标函数: 最小化鲁棒总成本
        route_travel_cost_expr = gp.quicksum(
            self.calculate_distance(initial_driver.current_pos, group_details[j]['sequence_start_pos']) *
            (1 + self.travel_time_deviation_ratio) * self.travel_cost_per_unit * x[num_groups, j]
            for j in range(num_groups)
        ) + gp.quicksum(
            self.calculate_distance(group_details[i]['sequence_end_pos'], group_details[j]['sequence_start_pos']) *
            (1 + self.travel_time_deviation_ratio) * self.travel_cost_per_unit * x[i, j]
            for i in range(num_groups) for j in range(num_groups) if i != j
        ) + gp.quicksum(group_details[i]['internal_total_cost'] * y[i] for i in range(num_groups))

        model.setObjective(route_travel_cost_expr, GRB.MINIMIZE)

        # 5. 约束条件
        # 集合划分约束：每个订单必须被覆盖
        for order_id in orders_in_slot:
            model.addConstr(gp.quicksum(y[i] for i, og in enumerate(order_groups) if order_id in og.orders) == 1, f"cover_{order_id}")

        # VRP流约束
        model.addConstr(x.sum(num_groups, '*') == 1, "leave_depot") # 从车库出发
        model.addConstr(x.sum('*', num_groups) == 1, "enter_depot") # 回到车库

        for i in range(num_groups):
            model.addConstr(x.sum(i, '*') == y[i], f"flow_out_{i}")
            model.addConstr(x.sum('*', i) == y[i], f"flow_in_{i}")
            model.addConstr(x[i, i] == 0, f"no_self_loop_{i}")

        # 时间约束 (MTZ-style)
        M = 10000 
        for i in range(num_groups):
            # 从车库出发
            travel_time_from_depot = self.calculate_distance(initial_driver.current_pos, group_details[i]['sequence_start_pos']) / self.driver_speed
            model.addConstr(a[i] >= initial_driver.current_time + travel_time_from_depot - M * (1 - x[num_groups, i]), f"time_from_depot_{i}")

            # 组间旅行
            for j in range(num_groups):
                if i != j:
                    travel_time_ij = self.calculate_distance(group_details[i]['sequence_end_pos'], group_details[j]['sequence_start_pos']) / self.driver_speed
                    model.addConstr(a[j] >= a[i] + group_details[i]['internal_duration'] + travel_time_ij - M * (1 - x[i, j]), f"time_between_{i}_{j}")

        # 6. 求解
        model.optimize()
        
        if model.status != GRB.OPTIMAL:
            print("警告: Gurobi未能找到最优解。")
            if model.status == GRB.INFEASIBLE:
                print("模型不可行。请检查约束。")
                model.computeIIS()
                model.write("model.ilp")
                print("IIS写入model.ilp")
            # 返回空结果或使用备用
            return 0.0, [initial_driver], {'assignments': [], 'message': 'Gurobi failed'}
        
        # 7. 解析结果
        selected_groups_indices = [i for i in range(num_groups) if y[i].X > 0.5]
        
        # 重构路径
        path = []
        curr_node = num_groups
        while len(path) < len(selected_groups_indices):
            found_next = False
            for j in range(num_groups):
                if x[curr_node, j].X > 0.5:
                    path.append(j)
                    curr_node = j
                    found_next = True
                    break
            if not found_next:
                break
        
        # 计算最终成本和司机状态
        total_travel_cost = 0
        total_penalty_cost = 0
        
        current_pos = initial_driver.current_pos
        current_time = initial_driver.current_time
        assignment_details = {'assignments': []}
        
        final_driver = copy.deepcopy(initial_driver)
        final_driver.assigned_orders = []
        final_driver.total_cost = 0
        
        for group_idx in path:
            og = order_groups[group_idx]
            details = self.calculate_sequence_details(og.sequence, current_pos, current_time, slot)
            
            total_travel_cost += details['travel_cost']
            total_penalty_cost += details['penalty_cost']
            
            final_driver.assigned_orders.extend(og.orders)
            
            assignment_details['assignments'].append({
                'order_group': og,
                'driver_id': final_driver.driver_id,
                'cost': details['total_cost'],
                'travel_cost': details['travel_cost'],
                'penalty_cost': details['penalty_cost']
            })
            current_pos = details['final_pos']
            current_time = details['final_time']

        final_driver.current_pos = current_pos
        final_driver.current_time = current_time
        final_driver.total_cost = total_travel_cost + total_penalty_cost
        
        assignment_details['total_travel_cost'] = total_travel_cost
        assignment_details['total_penalty_cost'] = total_penalty_cost
        
        print(f"Gurobi选择并排序了{len(path)}个订单组: {[order_groups[i].orders for i in path]}")
        print(f"总旅行成本: {total_travel_cost:.3f}")
        print(f"总延迟惩罚: {total_penalty_cost:.3f}")
        print(f"总成本: {total_travel_cost + total_penalty_cost:.3f}")

        return total_travel_cost + total_penalty_cost, [final_driver], assignment_details

class HybridSolver:
    """
    单波次订单整合调度混合算法 (Hybrid Single Order Consolidation Dispatching Algorithm)
    结合OGGM启发式生成订单组和Gurobi精确选择/分配订单组。
    """
    
    def __init__(self, food_delivery_optimizer):
        """
        初始化HybridSolver
        
        Args:
            food_delivery_optimizer: FoodDeliveryOptimizer实例
        """
        self.optimizer = food_delivery_optimizer
        
        # 初始化子算法
        self.oggm = OGGM(food_delivery_optimizer)
        self.gurobi_assigner = GurobiSelectorAssigner(food_delivery_optimizer)
        
        print("HybridSolver初始化完成 - 整合OGGM + Gurobi精确求解")
    
    def solve_single_slot_dispatch(self, orders_in_slot: List[int], slot: int, 
                                 initial_driver_states: List[Driver] = None) -> Tuple[float, float, List[Driver], Dict]:
        """
        求解单个时段的调度问题
        """
        print(f"\n{'='*60}")
        print(f"HybridSolver求解时段{slot}调度问题")
        print(f"订单: {orders_in_slot}")
        
        actual_start_pos_for_slot = None
        actual_start_time_for_slot = None
        
        if initial_driver_states and len(initial_driver_states) > 0:
            representative_driver_prev_state = initial_driver_states[0]
            actual_start_pos_for_slot = representative_driver_prev_state.current_pos
            nominal_slot_start_time = (slot - 1) * self.optimizer.slot_duration
            actual_start_time_for_slot = max(representative_driver_prev_state.current_time, nominal_slot_start_time)
            print(f"HybridSolver: 时段{slot}将使用代表性司机起点: Pos={actual_start_pos_for_slot}, Time={actual_start_time_for_slot:.2f} for OGGM")

        if not orders_in_slot:
            # 当没有订单时，需要正确地将司机状态推进到下一个时段
            ogah_fallback = OGAH(self.optimizer) # 使用OGAH的初始化逻辑来处理
            _, drivers, details = ogah_fallback.assign_order_groups_to_drivers([], slot, initial_driver_states)
            return 0.0, 0.0, drivers, details

        try:
            print(f"\n--- Step 1: OGGM生成订单组 ---")
            order_groups = self.oggm.generate_order_groups_for_slot(
                orders_in_slot, slot, actual_start_pos_for_slot, actual_start_time_for_slot
            )
            
            if not order_groups:
                print("警告：OGGM未生成任何订单组")
                return 0.0, 0.0, initial_driver_states if initial_driver_states else [], {'assignments': [], 'message': 'No order groups generated'}
            
            print(f"\n--- Step 2: Gurobi精确选择并分配订单组 ---")
            total_cost, drivers_final_state, assignment_details = self.gurobi_assigner.solve(
                order_groups, orders_in_slot, slot, initial_driver_states
            )
            
            travel_cost = assignment_details.get('total_travel_cost', 0)
            penalty_cost = assignment_details.get('total_penalty_cost', 0)
            
            print(f"\n=== HybridSolver时段{slot}求解完成 ===")
            print(f"旅行成本: {travel_cost:.3f}")
            print(f"延迟惩罚: {penalty_cost:.3f}")
            print(f"总成本: {total_cost:.3f}")
            
            dispatch_details = {
                'slot': slot,
                'orders': orders_in_slot,
                'assignments': assignment_details['assignments'],
                'drivers': [{'driver_id': d.driver_id, 'assigned_orders': d.assigned_orders, 
                           'current_pos': d.current_pos, 'current_time': d.current_time,
                           'total_cost': d.total_cost} for d in drivers_final_state],
                'total_travel_cost': travel_cost,
                'total_penalty_cost': penalty_cost,
                'total_cost': total_cost
            }
            
            return travel_cost, penalty_cost, drivers_final_state, dispatch_details
            
        except Exception as e:
            print(f"HybridSolver求解时段{slot}出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return 0.0, 0.0, [], {'error': str(e)}

def test_complete_hybrid_solver():
    """完整测试HybridSolver算法，验证与Gurobi.py结果的一致性"""
    print("="*80)
    print("HybridSolver完整算法测试 - 复现论文3.3案例")
    print("="*80)
    
    # 模拟FoodDeliveryOptimizer（与Gurobi.py完全一致）
    class MockFoodDeliveryOptimizer:
        def __init__(self):
            # 基本参数设置 - 严格按照Gurobi.py
            self.commission_rate = 0.18
            self.travel_cost_per_unit = 0.2  
            self.penalty_cost_per_unit = 1.0
            self.slot_duration = 25
            self.driver_speed = 1.0
            self.max_orders_per_driver = 10
            self.num_drivers = 1  # 论文案例中只有1个司机
            
            # 订单基本信息 - 严格按照论文Table 1
            self.order_values = [31, 22, 36, 43]
            self.order_restaurants = [0, 1, 0, 1]  # O1,O3→R1, O2,O4→R2
            self.estimated_delivery_duration = [12, 15, 18, 20]
            
            # 顾客偏好列表 - 严格按照论文Table 1
            self.customer_preferences = {
                0: {(1,3): 1, (1,6): 2, (2,3): 4, (2,6): 5, 'no_purchase': 3},
                1: {(1,3): 1, (1,6): 2, (2,3): 3, (2,6): 4, 'no_purchase': 5},
                2: {(1,3): 1, (1,6): 4, (2,3): 2, (2,6): 5, 'no_purchase': 3},
                3: {(1,3): 4, (1,6): 5, (2,3): 1, (2,6): 2, 'no_purchase': 3}
            }
            
            # 空间布局设置 - 严格按照Gurobi.py
            self.restaurant_coords = {0: (2, 2), 1: (8, 2)}
            self.customer_coords = {0: (1, 6), 1: (9, 6), 2: (3, 8), 3: (7, 8)}
            self.driver_start_coords = {0: (5, 1)}
        
        def get_customer_choice(self, customer_id, available_tpcs):
            """根据偏好获取顾客选择（与Gurobi.py一致）"""
            preferences = self.customer_preferences[customer_id]
            available_options = list(available_tpcs) + ['no_purchase']
            
            best_choice = min(available_options, 
                             key=lambda x: preferences.get(x, float('inf')))
            
            return None if best_choice == 'no_purchase' else best_choice
        
        def get_all_customer_choices(self, price_slot1, price_slot2):
            """获取所有顾客在给定价格下的选择（与Gurobi.py一致）"""
            available_tpcs = [(1, price_slot1), (2, price_slot2)]
            choices = []
            
            for customer_id in range(4):  # 4个顾客
                choice = self.get_customer_choice(customer_id, available_tpcs)
                choices.append(choice)
            
            return choices
    
    try:
        # 创建模拟优化器
        mock_optimizer = MockFoodDeliveryOptimizer()
        
        # 创建HybridSolver实例
        hybrid_solver = HybridSolver(mock_optimizer)
        
        current_drivers_state_list: List[Driver] = None 

        pricing_schemes = [(3, 3), (3, 6), (6, 3), (6, 6)]
        all_results = []
        
        for price_slot1, price_slot2 in pricing_schemes:
            print(f"\n{'='*70}")
            print(f"测试定价方案: (时段1=¥{price_slot1}, 时段2=¥{price_slot2})")
            print(f"{'='*70}")
            
            customer_choices = mock_optimizer.get_all_customer_choices(price_slot1, price_slot2)
            
            print("顾客选择:")
            slot1_orders = []
            slot2_orders = []
            
            for i, choice in enumerate(customer_choices):
                if choice:
                    choice_str = f"选择时段{choice[0]}, 支付¥{choice[1]}"
                    if choice[0] == 1:
                        slot1_orders.append(i)
                    else:
                        slot2_orders.append(i)
                else:
                    choice_str = "选择不购买"
                print(f"  顾客O{i+1}: {choice_str}")
            
            print(f"\n时段分配:")
            print(f"  时段1订单: {slot1_orders}")
            print(f"  时段2订单: {slot2_orders}")
            
            total_travel_cost = 0.0
            total_penalty_cost = 0.0
            
            current_drivers_state_list = None

            # 求解时段1
            travel1, penalty1, drivers_after_slot1, _ = hybrid_solver.solve_single_slot_dispatch(
                slot1_orders, 1, current_drivers_state_list
            )
            total_travel_cost += travel1
            total_penalty_cost += penalty1
            current_drivers_state_list = drivers_after_slot1

            # 求解时段2
            travel2, penalty2, drivers_after_slot2, _ = hybrid_solver.solve_single_slot_dispatch(
                slot2_orders, 2, current_drivers_state_list
            )
            total_travel_cost += travel2
            total_penalty_cost += penalty2
            
            total_cost = total_travel_cost + total_penalty_cost
            
            revenue = 0
            for i, choice in enumerate(customer_choices):
                if choice:
                    order_id = i
                    revenue += mock_optimizer.order_values[order_id] * mock_optimizer.commission_rate
                    revenue += choice[1]  
            
            profit = revenue - total_cost
            
            result = {
                'scheme': (price_slot1, price_slot2),
                'revenue': revenue,
                'travel_cost': total_travel_cost,
                'penalty_cost': total_penalty_cost,
                'total_cost': total_cost,
                'profit': profit
            }
            
            all_results.append(result)
            
            print(f"\n--- 方案{(price_slot1, price_slot2)}汇总 ---")
            print(f"总收入: {revenue:.2f}")
            print(f"总旅行成本: {total_travel_cost:.2f}")
            print(f"总延迟惩罚: {total_penalty_cost:.2f}")
            print(f"总运营成本: {total_cost:.2f}")
            print(f"总利润: {profit:.2f}")
        
        best_scheme = max(all_results, key=lambda x: x['profit'])
        
        print(f"\n{'='*80}")
        print("所有定价方案结果汇总对比")
        print(f"{'='*80}")
        print(f"{'方案':<10} {'收入':<15} {'旅行成本':<15} {'延迟成本':<15} {'总成本':<15} {'利润':<15} {'最优':<10}")
        print("-" * 80)
        
        for result in all_results:
            scheme = result['scheme']
            is_best = "★" if result == best_scheme else ""
            scheme_str = f"({scheme[0]}, {scheme[1]})"
            print(f"{scheme_str:<10} {result['revenue']:<15.2f} {result['travel_cost']:<15.2f} {result['penalty_cost']:<15.2f} {result['total_cost']:<15.2f} {result['profit']:<15.2f} {is_best:<10}")
        
        return hybrid_solver, all_results
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    # 运行完整测试
    hybrid_solver_instance, test_results = test_complete_hybrid_solver()